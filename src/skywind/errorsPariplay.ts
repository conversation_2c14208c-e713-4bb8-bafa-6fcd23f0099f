import { BaseErrorToLog, ErrorInfoToLog } from "./errors";
import { PariplayErrorContainer } from "./entities/pariplay";

export class PariplayBaseError extends Error implements PariplayErrorContainer {
    constructor(public responseStatus: number, public ErrorCode: number, public ErrorMessage: string) {
        super(ErrorMessage);
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.ErrorCode,
                message: this.ErrorMessage,
                stack: this.stack
            }
        };
    }
}

export class AuthenticateFailedError extends PariplayBaseError {
    constructor() {
        super(200, 2, "Authenticate failed");
    }
}

export class SessionExpiredError extends PariplayBaseError {
    constructor(message = "Session expired") {
        super(200, 4, message);
    }
}

export class GeneralError extends PariplayBaseError {
    constructor(message = "General error") {
        super(200, 900, message);
    }
}

export class InsufficientBalanceError extends PariplayBaseError {
    constructor() {
        super(200, 1, "Insufficient balance error");
    }
}

export class UnknownTransactionError extends PariplayBaseError {
    constructor(trxId = "string") {
        super(200, 7, `Unknown transaction - ${trxId}`);
    }
}

export class AmountNegativeError extends PariplayBaseError {
    constructor() {
        super(200, 8, "Amount should be positive");
    }
}
