import { suite, test } from "mocha-typescript";
const request = require("supertest");
import * as uuid from "uuid";
import { expect } from "chai";
import * as Transaction from "../../skywind/models/transaction";
import config from "../../skywind/config";
import { getTransaction<PERSON>ey } from "../../skywind/models/transaction";
import { WALLET_ACTION } from "../../skywind/models/transaction";
import { PariplayBaseSpec } from "./auth.spec";
import { PariplayPaymentResponse } from "../../skywind/entities/pariplay";
import { ACTION } from "../../skywind/service/pariplay/actions";

@suite
class PariplayDebitSpec extends PariplayBaseSpec {
    @test
    public async testDebitSuccess() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send(this.debitRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount - 100);
        expect(response.body.TransactionId).to.be.equal(this.trxId);
    }

    @test
    public async testMerchantNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send({
                ...this.debitRequest,
                Account: { Username: "invalidMerch", Password: "SomePwd"}
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(2);
    }

    @test
    public async testDebitPlayerNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send({
                ...this.debitRequest,
                PlayerId: "invalidPlayer"
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async testDebitSessionNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send({
                ...this.debitRequest,
                Token: "invalidSession"
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async testDebitInsufficientBalanceError() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send({
                ...this.debitRequest,
                Amount: 100000000
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(1);

    }

    @test
    public async debitShouldBeIdempotency() {
        const externalId = uuid.v4();
        Transaction.setAll({
            [getTransactionKey(this.trxId, WALLET_ACTION.debit)]: [this.trxId, -100, externalId, true]
        });
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send(this.debitRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount);
        expect(response.body.TransactionId).to.be.equal(this.trxId);
    }

    @test
    public async testCustomError() {
        this.createCustomError(900, ACTION.DEBIT);
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send(this.debitRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(900);
    }

    @test
    public async testZeroBet() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send({
                ...this.debitRequest,
                Amount: 0
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(8);
    }

    @test
    public async testFreebet() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Debit")
            .send({
                ...this.debitRequest,
                BonusId: "testBonusId"
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount);
        expect(response.body.TransactionId).to.be.equal(this.trxId);
    }
}
