import { parseBoolean } from "../utils/helper";
import { Ticket, tickets } from "../models/ticket";
import { Forbidden, GameTokenExpired } from "../errors";
import { defaultCustomerCurrency, defaultCustomerId, getDefaultCustomer } from "./customer";
import { InvalidSecureToken, SessionExpired } from "../popErrors";
import config from "../config";
import { deleteCustomerSession } from "../models/session";
import * as gvcErrors from "../gvcErrors";
import * as SBTechErrors from "../errorsSBTech";
import * as GANErrors from "../errorsGAN";
import * as SisalErrors from "../sisal/sisal.errors";

export enum SpecialTicket {
    PHANTOM = "phantom-ticket"
}

export function getTicket(ticketId, expirationTime, error: new (message: string) => Error = GameTokenExpired) {
    const currentTimeMs = new Date().getTime();
    const ticket: Ticket = tickets[ticketId] && { ...tickets[ticketId] } as Ticket;
    if (ticket) {
        if (currentTimeMs > ticket.creationTime + expirationTime) {
            delete tickets[ticketId];
            throw new error("Ticket is expired");
        }
    } else {
        throw new error("Ticket not found");
    }
    ticket.id = ticketId;
    return ticket;
}

export function getSBTechTicket(ticketId, expirationTime) {
    const currentTimeMs = new Date().getTime();
    const ticket: Ticket = tickets[ticketId] && { ...tickets[ticketId] } as Ticket;

    if (!ticket) {
        throw new SBTechErrors.InvalidClientTokenError();
    }

    if (currentTimeMs > ticket.creationTime + expirationTime) {
        delete tickets[ticketId];
        throw new SBTechErrors.InvalidClientTokenError();
    }
    ticket.id = ticketId;
    return ticket;
}

export function getSisalTicket(ticketId, expirationTime) {
    const currentTimeMs = new Date().getTime();
    const ticket: Ticket = tickets[ticketId] && { ...tickets[ticketId] } as Ticket;

    if (!ticket) {
        throw new SisalErrors.UnauthorizedError();
    }

    if (currentTimeMs > ticket.creationTime + expirationTime) {
        delete tickets[ticketId];
        throw new SisalErrors.UnauthorizedError();
    }
    ticket.id = ticketId;
    return ticket;
}

export function getGANTicket(ticketId, expirationTime) {
    const currentTimeMs = new Date().getTime();
    const ticket: Ticket = tickets[ticketId] && { ...tickets[ticketId] } as Ticket;

    if (!ticket) {
        throw new GANErrors.InvalidToken();
    }

    if (currentTimeMs > ticket.creationTime + expirationTime) {
        delete tickets[ticketId];
        throw new GANErrors.InvalidToken();
    }
    ticket.id = ticketId;
    return ticket;
}

export function getGVCTicket(ticketId, expirationTime) {
    const currentTimeMs = new Date().getTime();
    const ticket: Ticket = tickets[ticketId] && { ...tickets[ticketId] } as Ticket;

    if (ticket) {
        if (currentTimeMs > ticket.creationTime + expirationTime) {
            delete tickets[ticketId];
            throw new gvcErrors.PlayerSessionExpiredError();
        }
    } else {
        throw new gvcErrors.InvalidSecureTokenError();
    }
    ticket.id = ticketId;
    return ticket;
}

export function getPOPTicket(ticketId: string, expirationTime: number = config.expirationTime.ticket) {
    const currentTimeMs = new Date().getTime();
    const ticket: Ticket = tickets[ticketId] && { ...tickets[ticketId] } as Ticket;
    if (ticket) {
        if (currentTimeMs > ticket.creationTime + expirationTime) {
            delete tickets[ticketId];
            throw new SessionExpired();
        }
    } else {
        throw new InvalidSecureToken();
    }
    delete tickets[ticketId];

    ticket.id = ticketId;
    return ticket;
}

export function createTicket(merchantId, custId): Ticket {
    const currentTimeMs: number = new Date().getTime();
    const ticketId = "TICKET-" + currentTimeMs + "-" + Math.round(Math.random() * 1048576);

    const ticket = {
        id: ticketId,
        merchantId: merchantId,
        custId: custId,
        creationTime: currentTimeMs,
    };

    tickets[ticketId] = ticket;

    return ticket;
}

// generates a valid ticket for a test user
export function generateTicketForUser(merchant,
                                      custId = defaultCustomerId,
                                      currencyCode = defaultCustomerCurrency,
                                      singleSession = false,
                                      isTest = true,
                                      amount?: number): Ticket {
    if (typeof singleSession !== "undefined" || typeof singleSession !== "undefined") {
        singleSession = parseBoolean(singleSession);
    }

    const savedCustomer = merchant.customers[custId];
    if (!savedCustomer) {
        // Create customer
        merchant.customers[custId] = getDefaultCustomer(custId, currencyCode, isTest, amount);
    } else if (!savedCustomer.test_cust) {
        throw new Forbidden("only test customer available for this method");
    } else if (savedCustomer.currency_code !== currencyCode) {
        throw new Forbidden("currency_code from request does not match with currency_code from saved player");
    } else {
        if (singleSession) {
            deleteCustomerSession(custId);
        }
    }

    return createTicket(merchant.merch_id, custId);
}

export function isSpecialTicket(ticketId: string): boolean {
    return config.specialFeatures.phantomCompanyTournamentTicket &&
        typeof ticketId === "string" &&
        ticketId.startsWith(SpecialTicket.PHANTOM);
}

export function getDataFromTemplateTicket(ticketId = "") {
    // tslint:disable-next-line:prefer-const
    let [customerId, currency, hash, jurisdiction, country] = ticketId.split("__");
    customerId = customerId ? customerId.trim() : defaultCustomerId;
    currency = currency ? currency.trim() : defaultCustomerCurrency;
    jurisdiction = jurisdiction && jurisdiction.trim();
    country = country && country.trim();

    return [customerId, currency, jurisdiction, country];
}
