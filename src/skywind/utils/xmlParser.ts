import { XML<PERSON><PERSON><PERSON>, XMLParser, XmlBuilderOptions, X2jOptions } from "fast-xml-parser";

const objToXmlOptions: XmlBuilderOptions = {
    ignoreAttributes : false,
    attributeNamePrefix : "_",
};

const xmlToObjOptions: X2jOptions = {
    ...objToXmlOptions,
    parseAttributeValue : true,
};

const xmlParser = new XMLParser(xmlToObjOptions);
const builder = new XMLBuilder(objToXmlOptions);

export function parseXML(xml: string): any {
    return builder.build(xml);
}

export function convertToXML(data: any): string {
    return xmlParser.parse(data);
}
