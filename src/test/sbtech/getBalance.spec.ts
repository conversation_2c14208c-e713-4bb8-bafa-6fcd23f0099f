import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");

import { SBTechBaseSpec } from "./base.spec";
import { createTicket } from "../../skywind/service/ticket";

@suite("SBTech.balance")
class SBTechBalanceSpec extends SBTechBaseSpec {

    @test("should return player's balance")
    public async getBalance() {
        const expectedResponse = {
            "balance": 10000,
            "sessionId": "test_session"
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(this.fixtures.getBalanceRequest)
            .expect(200);

        delete actualResponse.body.timestamp;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }
}
