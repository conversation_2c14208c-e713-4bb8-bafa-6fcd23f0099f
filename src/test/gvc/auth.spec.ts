import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { SinonStub, spy, stub } from "sinon";

const request = require("supertest");

import config from "../../skywind/config";
import { GVCBaseSpec } from "./baseSpec";
import { createTicket } from "../../skywind/service/ticket";

@suite("[GVC.auth]")
class GVCAuthSpec extends GVCBaseSpec {

    @test("should be successful authentication")
    public async testSuccessAuth() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedAuthRequest = Object.assign(this.authRequest, { secureToken: ticket.id });

        await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(updatedAuthRequest)
            .expect(200);
    }

    @test("error InvalidAccountIdError should be thrown when MERCHANT does not exist")
    public async testMerchantNotFound() {
        const updatedAuthRequest = Object.assign(
            this.authRequest, { accountId: `WrongMerchant__${this.playerCode}` });

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(updatedAuthRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(122);
    }

    @test("error InvalidAccountIdError should be thrown when PLAYER does not exist")
    public async testPlayerNotFound() {
        const updatedAuthRequest = Object.assign(
            this.authRequest, { accountId: `${this.merchantCode}__WrongPlayer` });

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(updatedAuthRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(122);
    }

    @test("error PlayerSessionExpiredError should be thrown")
    public async testPlayerSessionExpired() {
        config.expirationTime.ticket = 0; // to check expired ticket

        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedAuthRequest = Object.assign(this.authRequest, { secureToken: ticket.id });

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(updatedAuthRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(106);

        config.expirationTime.ticket = this.ticketExpirationTimeDefault; // revert default
    }

    @test("error InvalidSecureTokenError should be thrown")
    public async testInvalidSecureToken() {
        const updatedAuthRequest = Object.assign(this.authRequest, {});

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(updatedAuthRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(101);
    }
}
