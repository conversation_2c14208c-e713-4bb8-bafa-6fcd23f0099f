import { BaseErrorToLog } from "../errors";

export interface PariplayAuthContainer {
    Account: PariplayAuthData;
}

export interface PariplayAuthData {
    Username: string; // skywind_$operatorCode
    Password: string;
}

export interface MessageContainer {
    Message: any[];
}

export interface PariplayVerifyTokenRequest extends PariplayAuthContainer {
    ClientToken: string;
    GameCode: string;
    ClientIp: string;
    ExtraData: string;
}

export interface PariplayVerifyTokenResponse extends PariplayErrorContainer {
    PlayerId: string; // $OperatorCode_$playerId
    Token: string; // Session ID
    Balance: number;
    CurrencyCode: string;
    CountryCode: string;
    OperatorCode: string;
    UserSettings?: {
        MaxBet?: number;
        MaxExposure?: number;
        MinBet?: number;
        DefaultBet?: number;
    };
}

export interface PariplayErrorContainer extends BaseErrorToLog {
    ErrorCode: number;
    ErrorMessage?: string;
}

export interface PariplayBalanceRequest extends PariplayAuthContainer {
    PlayerId: string;
    Token: string;
    GameCode: string;
}

export interface PariplayBalanceResponse extends PariplayErrorContainer {
    Balance: number;
}

export interface PariplayPaymentResponse extends PariplayErrorContainer {
    Balance: number;
    TransactionId: string;
}

export interface PariplayPaymentRequest extends PariplayAuthContainer {
    Amount: number;
    Token: string;
    GameCode: string;
    PlayerId: string;
    RoundId: string;
    TransactionId: string;
    BonusId?: string;
}

export interface PariplayDebitTransactionContainer {
    DebitTransactionId: string;
}

export interface PariplayWinPaymentRequest extends PariplayPaymentRequest, PariplayDebitTransactionContainer {
    IsRoundEnded: boolean;
}

export interface PariplayRollbackRequest extends PariplayAuthContainer, PariplayDebitTransactionContainer {
    Token: string;
    GameCode: string;
    PlayerId: string;
    RoundId: string;
    TransactionId: string;
    IsRoundEnded: boolean;
}
