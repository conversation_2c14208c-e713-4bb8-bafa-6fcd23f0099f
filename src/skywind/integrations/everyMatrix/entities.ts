export enum EVERY_MATRIX_MOCK_TYPE {
    /**
     * Mock engine with fully manual token setup
     */
    MockEngine,
    /**
     * Mock engine with semi-manual token setup
     */
    MockEnginePredefined,
    /**
     * Simplified engine, completely predefined users
     * without merchant, customers and other staff
     */
    SimpleEngine
}

export enum EVERY_MATRIX_JURISDICTION {
    UK = "UK",
    MT = "MT",
    FI = "FI"
}

export enum EVERY_MATRIX_SEX {
    Male = "male",
    Female = "female"
}

export interface BaseRequest {
    Token: string;
    Hash?: string;
}

/**
 * Response for win, bet, cancel, get balance
 */
export interface BaseResponse {
    TotalBalance: string;
    Currency: string;
    Status: string;
}

/**
 * Response for RoundInfo
 */
export interface EMRoundInfoResponse {
    Status: string;
}

/// region Authenticate Player

export interface EMAuthenticateRequest {
    LaunchToken: string;
    RequestScope: string;
}

export interface EMAuthenticateResponse extends BaseResponse {
    Token: string;
    UserName: string;
    UserId: string;
    Country: string;
    Age: string;
    Sex: string;
    Jurisdiction: string;
    BetLimit?: number;
}

// endregion

export interface BasePaymentRequest extends  BaseRequest {
    Amount: number;
    Currency: string;
    ExternalId: string;
    GameId: string;
    RoundId: string;
}

// region Bet

export interface BetRequest extends BasePaymentRequest {
    JackpotContribution?: JackpotContribution[];
}

export interface JackpotContribution {
    JackpotId: string;
    JackpotContributionAmount: number;
}

// endregion

// region Win

export interface WinRequest extends BasePaymentRequest {
    JackpotPayout?: JackpotPayout[];
    BetExternalId: string;
    BonusId?: string;
    RoundEnd: boolean;
}

export interface BonusWinRequest extends WinRequest {
    BonusType: string;
}

export interface EMRoundInfoRequest {
    Token: string;
    Currency: string;
    GameId: string;
    RoundId: string;
    UserId: string;
    SpinId: number;
    GameType: "normal" | "freegame" | "bonusgame";
    Timestamp: string;
    RoundInfo: string;
    Hash: string;
}

export interface JackpotPayout {
    JackpotId: string;
    JackpotPayoutAmount: number;
}

// endregion

// region Cancel

export interface CancelBetRequest {
    Token: string;
    ExternalId: string;
    CanceledExternalId: string;
    Hash: string;
}

// endregion

// region Get Balance

export interface BalanceRequest extends BaseRequest  {
    Currency: string;
}

// endregion

export class EveryMatrixPlayerInfo {
    public LaunchToken: string;
    public GameToken: string;
    public TotalBalance: number;
    public Currency: string;
    public UserName: string;
    public UserId: string;
    public Country: string;
    public Age: number;
    public Sex: string;
    public Jurisdiction: string;
}

// normal player token format is "emLaunchToken_{operatorId}_{jurisdiction}"
export enum EVERY_MATRIX_TOKEN {
    NORMAL = "emLaunchToken",
    BET_UNKNOWN = "emLaunchToken:Bet:Unknown",
    BET_CURRENCY_NOT_MATCH = "emLaunchToken:Bet:CurrencyNotMatch",
    WIN_UNKNOWN = "emLaunchToken:Win:Unknown",
    WIN_CURRENCY_NOT_MATCH = "emLaunchToken:Win:CurrencyNotMatch",
    CANCEL_UNKNOWN = "emLaunchToken:Cancel:Unknown",
    CANCEL_CURRENCY_NOT_MATCH = "emLaunchToken:Cancel:CurrencyNotMatch",
    BET_CANCEL_UNKNOWN = "emLaunchToken:Bet:Cancel:Unknown",
}

export enum EVERY_MATRIX_GAME_TOKEN {
    NORMAL = "emGameToken"
}

export enum EVERY_MATRIX_ACTION {
    AUTHENTICATE = "authenticate",
    BET = "bet",
    WIN = "win",
    ROUND_INFO = "roundinfo",
    BONUS_WIN = "bonuswin",
    CANCEL = "cancel",
    GET_BALANCE = "getbalance"
}
