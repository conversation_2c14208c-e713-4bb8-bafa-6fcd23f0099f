import {
    FAKE_GAME_ROUND_ID,
    GVC_TRANSACTION_TYPE,
    GVCFundsTransferRequest,
    GVCFundsTransferResponse,
    GVCPlayerBalanceRequest,
    GVCPlayerBalanceResponse,
    GVCTransactionDetail,
    GVCVerifyTokenRequest,
    GVCVerifyTokenResponse,
    GVCVoidTransactionRequest,
    GVCVoidTransactionResponse
} from "../entities/gvc";
import * as gvcErrors from "../gvcErrors";

import { Customer, Merchant } from "../models/merchant";
import * as TransactionModel from "../models/transaction";
import { TRANSACTION_INDEX, WALLET_ACTION } from "../models/transaction";
import { sumMajorUnits } from "../utils/helper";
import { throwCustomError } from "./customError";
import { extraDataHandler } from "./extraData";
import { createTicket } from "./ticket";
import { Ticket } from "../models/ticket";

export interface GVCService {
    verifyToken(data: GVCVerifyTokenRequest): Promise<GVCVerifyTokenResponse>;

    getBalance(data: GVCPlayerBalanceRequest): Promise<GVCPlayerBalanceResponse>;

    makePayment(data: GVCFundsTransferRequest): Promise<GVCFundsTransferResponse>;

    cancelBet(data: GVCVoidTransactionRequest): Promise<GVCVoidTransactionResponse>;
}

export class GVCServiceImpl implements GVCService {
    constructor(public merchant: Merchant, public customer?: Customer) {
    }

    @throwCustomError({ action: "verifytoken" })
    @extraDataHandler()
    public async verifyToken(data: GVCVerifyTokenRequest): Promise<GVCVerifyTokenResponse> {
        const newTicket: Ticket = createTicket(this.merchant.merch_id, data.accountId);
        const response = {
            accountId: data.accountId,
            partnerId: data.partnerId,
            currencyCode: this.customer.balance.currency_code,
            gameCode: data.gameCode,
            balance: this.toMinorUnits(this.customer.balance.amount),
            renewedSecureToken: newTicket.id
        } as GVCVerifyTokenResponse;

        if (this.customer.bet_limit) {
            response.scalableParams = [{ key: "maxStake", value: this.customer.bet_limit + "" }];
        }

        return response;
    }

    @throwCustomError({ action: "getbalance" })
    @extraDataHandler()
    public async getBalance(data: GVCPlayerBalanceRequest): Promise<GVCPlayerBalanceResponse> {
        return {
            accountId: data.accountId,
            partnerId: data.partnerId,
            currencyCode: this.customer.balance.currency_code,
            gameCode: data.gameCode,
            balance: this.toMinorUnits(this.customer.balance.amount)
        } as GVCPlayerBalanceResponse;
    }

    @throwCustomError({ action: "makepayment" })
    @extraDataHandler()
    public async makePayment(data: GVCFundsTransferRequest): Promise<GVCFundsTransferResponse> {
        const { transactionId: trxId, transactionDetails } = data;
        const action = this.getWalletAction(trxId);

        this.validateTransaction(action, trxId);

        const totalAmount = transactionDetails
            .reduce((result: number, { amount, transactionType }: GVCTransactionDetail): number => {
                return this.validateTransactionType(transactionType)
                       ? result + Math.abs(amount)
                       : result;
            }, 0);

        this.checkPlayerBalance(
            this.toMinorUnits(this.customer.balance.amount), Math.abs(totalAmount) * action); // bet only

        const transaction = TransactionModel.setById(trxId, action, totalAmount, this.customer.cust_id);
        this.customer.balance.amount = sumMajorUnits(
            this.customer.balance.amount, this.toMajorUnits(totalAmount * action));

        return {
            partnerId: data.partnerId,
            accountId: data.accountId,
            currencyCode: this.customer.balance.currency_code,
            gameCode: data.gameCode,
            balance: this.toMinorUnits(this.customer.balance.amount),
            gameContext: data.gameContext,
            bpGameRoundId: FAKE_GAME_ROUND_ID,
            bpTransactionId: transaction[TRANSACTION_INDEX.EXTERNAL_ID],
            transactionId: data.transactionId,
        } as GVCFundsTransferResponse;
    }

    @throwCustomError({ action: "cancelbet" })
    @extraDataHandler()
    public async cancelBet(data: GVCVoidTransactionRequest): Promise<GVCVoidTransactionResponse> {
        const {
            transactionId: rollbackTrxId,
            cancelledTransactionId: debitTrxId
        } = data;

        let rollbackBpTrxId = {};
        if (!TransactionModel.getById(rollbackTrxId, WALLET_ACTION.rollback)) {
            const originalTransaction = TransactionModel.getById(debitTrxId, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new gvcErrors.TransactionToCancelNotFoundError();
            }

            const [_, amount, __, isRollback] = originalTransaction;
            if (isRollback) {
                throw new gvcErrors.TransactionAlreadyCancelledError(debitTrxId);
            }

            TransactionModel.rollbackById(debitTrxId, WALLET_ACTION.debit);
            this.customer.balance.amount = this.toMajorUnits(
                this.toMinorUnits(this.customer.balance.amount) - amount);
            rollbackBpTrxId = TransactionModel.setById(rollbackTrxId, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        }

        return {
            partnerId: data.partnerId,
            accountId: data.accountId,
            currencyCode: this.customer.balance.currency_code,
            balance: this.toMinorUnits(this.customer.balance.amount),
            bpGameRoundId: FAKE_GAME_ROUND_ID,
            bpTransactionId: rollbackBpTrxId[TRANSACTION_INDEX.EXTERNAL_ID],
            transactionId: data.transactionId,
        } as GVCVoidTransactionResponse;
    }

    private validateTransactionType(transactionType: string): boolean {
        if (!transactionType) {
            return false;
        }
        return GVC_TRANSACTION_TYPE[transactionType && transactionType.toUpperCase()]
            || transactionType.startsWith("JP_WIN;");
    }

    private validateTransaction(type: WALLET_ACTION, action: string) {
        const transaction = TransactionModel.getById(action, type);

        if (transaction) {
            throw new gvcErrors.DuplicateTransactionRequestError();
        }
    }

    private checkPlayerBalance(playerBalance: number, amount: number) {
        if (playerBalance + amount < 0) {
            throw new gvcErrors.InsufficientBalanceError();
        }
    }

    private getWalletAction(trxId: string): WALLET_ACTION {
        const [_, type] = trxId.split("_");

        switch (type) {
            case "bet":
                return WALLET_ACTION.debit;
            case "zwin":
                return WALLET_ACTION.zero_win;
            case "FS":
            case "redeem":
            case "win":
                return WALLET_ACTION.credit;
            case "rollback":
                return WALLET_ACTION.rollback;
            default:
                throw new gvcErrors.UnknownTransactionError();
        }
    };

    private toMinorUnits(value: number): number {
        return value * 100;
    }

    private toMajorUnits(value: number): number {
        if (!value) {
            return 0;
        }

        return value / 100;
    }
}

export function getGVCService(merchant: Merchant, customer?: Customer): GVCServiceImpl {
    return new GVCServiceImpl(merchant, customer);
}
