import { TICKET_TYPE_TERMINAL } from "../utils/helper";
import config from "../config";
import { Customer, Merchant } from "../models/merchant";
import * as SessionModel from "../models/session";

export function createCustomerSession(merchant: Merchant, customer: Customer, type?: string) {
    customer.cust_session_id = generateCustomerSessionId(customer.cust_id);

    // remove old Session
    if (!merchant.multiple_session) {
        SessionModel.deleteCustomerSession(customer.cust_id);
    }

    SessionModel.storeCustomerSession(customer.cust_session_id, type, merchant.merch_id, customer.cust_id);
    return customer.cust_session_id;
}

export function generateCustomerSessionId(customerId: string) {
    return `SESSION_FOR_USER_${customerId}__${
        Math.random().toString(36).substr(2, 4)
        }_${new Date().toJSON().substr(0, 19).replace(/-/g, "").replace(/:/g, "_")}`;
}

/**
 * Validate customer session Id, delete session from sessions storage is it expired.
 * @param customerSessionId customer session token
 * @returns {boolean} true if session is valid and not expired
 */
export function validateCustomerSession(customerSessionId: string, isFinalization = false): boolean {
    const extendIfValid: boolean = true;
    if (!customerSessionId) {
        return false;
    }
    const customerSession = SessionModel.getById(customerSessionId);
    if (!customerSession) {
        return isFinalization ? true : false;
    }
    const expirationTime = customerSession.type === TICKET_TYPE_TERMINAL
                           ? config.expirationTime.terminalSession
                           : config.expirationTime.customerSession;
    const currentTimeMs = new Date().getTime();
    if (currentTimeMs > customerSession.ts + expirationTime && !isFinalization) {
        SessionModel.deleteById(customerSessionId);
        return false;
    }
    if (extendIfValid) {
        SessionModel.storeCustomerSession(
            customerSessionId,
            customerSession.type,
            customerSession.merchantId,
            customerSession.customerId
        );
    }
    return true;
}
