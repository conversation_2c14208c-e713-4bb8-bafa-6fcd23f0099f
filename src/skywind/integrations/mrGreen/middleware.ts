import { Customer, getById, Merchant } from "../../models/merchant";
import { NextFunction, Request, Response } from "express";
import {
    AuthenticationFailure, InvalidJurisdictionError,
    JurisdictionNotSetError,
    Mr<PERSON><PERSON>Error404,
    <PERSON><PERSON><PERSON><PERSON>n, OperatorNotFoundError,
    OperatorNotSetError,
    WrongTokenFormatError
} from "./errors";
import * as MerchantService from "../../service/merchant";
import { MR_GREEN_TOKEN, MR_GREEN_JURISDICTION, MR_GREEN_MOCK_TYPE } from "./entities";
import { MrGreenService } from "./service";
import { Ticket } from "../../models/ticket";

export interface RequestAdditionalInfo {
    token: string;
    ticket: Ticket;
    merchant: Merchant;
    customer: Customer;
    operatorId: string;
    jurisdiction: string;
    mockType: MR_GREEN_MOCK_TYPE;
}

export const MrGreenConfig = {
    swCreds: {
        callerauth: "Skywind",
        callerpassword: "testpassword"
    }
};

export function authoriseMerchant(req: Request & RequestAdditionalInfo,
                                  res: Response,
                                  next: NextFunction) {
    if (MrGreenConfig.swCreds.callerauth !== req.query.callerauth ||
        MrGreenConfig.swCreds.callerpassword !== req.query.callerpassword) {
        throw new MrGreenError404();
    }

    req.token = req.query.sessionid as string;

    if (!req.token) {
        throw new NotLoggedOn();
    }

    if (req.token.startsWith(MR_GREEN_TOKEN.NORMAL)) {
        // Predefined player
        const parts = req.token.split("_");
        if (parts.length !== 3) {
            throw new WrongTokenFormatError();
        }
        const operatorId = parts[1];
        if (!operatorId) {
            throw new OperatorNotSetError();
        }
        let merchant = getById(operatorId);

        if (!merchant) {
            merchant = MerchantService.createMerchant(operatorId, {
                merch_id: operatorId,
                merch_pwd: "qwerty123",
                isPromoInternal: "false",
                multiple_session: "false"
            });
        }

        const jurisdiction = parseJurisdiction(parts[2]);

        req.merchant = merchant;
        req.operatorId = operatorId;
        req.jurisdiction = jurisdiction;
        req.mockType = MR_GREEN_MOCK_TYPE.MockEnginePredefined;
    } else {
        req.ticket = MrGreenService.authorizePlayer(req.token);
        if (!req.ticket) {
            throw new AuthenticationFailure();
        }
        req.merchant = getById(req.ticket.merchantId);
        if (!req.merchant) {
            throw new OperatorNotFoundError();
        }
        req.mockType = MR_GREEN_MOCK_TYPE.MockEngine;
    }

    return next();
}

export function authorisePlayer(req: Request & RequestAdditionalInfo,
                                res: Response,
                                next: NextFunction) {

    const ticket = MrGreenService.authorizePlayer(req.token);
    req.customer = req.merchant.customers[ticket.custId];
    if (!req.customer) {
        throw new NotLoggedOn();
    }
    if (!req.jurisdiction && req.customer.jurisdiction) {
        req.jurisdiction = req.customer.jurisdiction;
    }
    if (!req.jurisdiction) {
        throw new JurisdictionNotSetError();
    }

    if (req.jurisdiction !== MR_GREEN_JURISDICTION.MGA &&
        req.jurisdiction !== MR_GREEN_JURISDICTION.UK &&
        req.jurisdiction !== MR_GREEN_JURISDICTION.Sweden) {
        throw new InvalidJurisdictionError();
    }

    return next();
}

function parseJurisdiction(jurisdictionInput: string): MR_GREEN_JURISDICTION {
    if (jurisdictionInput.toLowerCase() === MR_GREEN_JURISDICTION.UK.toLowerCase()) {
        return MR_GREEN_JURISDICTION.UK;
    } else if (jurisdictionInput.toLowerCase() === MR_GREEN_JURISDICTION.MGA.toLowerCase()) {
        return MR_GREEN_JURISDICTION.MGA;
    } else if (jurisdictionInput.toLowerCase() === MR_GREEN_JURISDICTION.Sweden.toLowerCase()) {
        return MR_GREEN_JURISDICTION.Sweden;
    }

    throw new JurisdictionNotSetError();
}
