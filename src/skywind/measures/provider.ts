import { measures } from "./measures";
import { EventEmitter } from "node:events";
import config from "../config";
import { Readable } from "stream";
import { generateTraceId } from "./traceId";
import { createTransactionRunner } from "./runner/transactionRunner";
import { DisabledTransactionRunner } from "./runner/disabledRunner";
import { errors } from "../errors";
import { type TransactionRunner } from "./runner/definition";
import { SQLOperationParser } from "./sqlOperationParser";
import MeasureInfo = measures.MeasureInfo;
import MeasurableFunction = measures.MeasurableFunction;
import ERROR_LEVEL = errors.ERROR_LEVEL;
import { RedisCommandList } from "./redisCommand";

export type MeasureDecorator = (target: any,
                                propertyName: string,
                                descriptor: TypedPropertyDescriptor<Function>) => void;

export interface MeasureProvider {

    baseInstrument(): void;

    instrument(module, name: string, properties?: string[], detailsExtractor?: (...args) => string);

    instrumentFunction(f: Function, name: string, detailsExtractor?: DetailsProvider): Function;

    incrementGauge(name: string, value: number, detauls?: string, attributes?: any): void;

    setGauge(name: string, value: number, detauls?: string, attributes?: any): void;

    saveError(err): void;

    getTransaction(): string;

    setTransaction(name: string): void;

    getTraceID(): string;

    setTraceID(traceID: string): void;

    setContextVariable<T>(name: string, value: T, transferable?: boolean): boolean;

    getContextVariable<T>(name: string, transferable?: boolean): T;

    getContext<T>(transferable?: boolean): T;

    runInTransaction<T>(name: string, action: (...args) => T): T;

    trackTransaction<T>(action: MeasurableFunction<T>): MeasurableFunction<T>;

    getMeasureDecorator(info: MeasureInfo): MeasureDecorator;

    getMeasures(): Promise<any>;

    getMeasuresStream(): Promise<Readable>;

    getMeasure(name: string): Promise<any>;
}

export const HEADER_PREFIX = "x-sw-t-";
export const TRACE_ID_HEADER = "x-sw-trace-id";
export const EXTERNAL_CALL_HEADER = "x-sw-external-call";

export type DetailsProvider = (args: any[]) => string;
export type GaugeProvider<G> = (args: any[]) => G;

const HTTP_METHODS = ["all", "get", "post", "put", "patch", "delete", "head", "options"];
const NETWORK_ERRORS: Set<string> = new Set(["ECONNREFUSED", "ECONNRESET", "ENETUNREACH", "ETIMEDOUT", "ENOTFOUND"]);

interface CallbackProvider {
    getCallback(args: IArguments): Function;

    setCallback(args: IArguments, callback: Function): void;
}

type ResultProcessor = (result, funcArgs, details, timer: () => void) => void;

export const CONTEXT_HOLDER = Symbol("sw_context");

export enum ContextField {
    TRANSACTION = "transaction",
    URI = "uri",
    TRACE_ID = "trace-id",
    OWNER = "owner",
    OWNER_TRANSACTION = "owner-transaction",
    SERVICE = "service"
}

export abstract class AbstractMeasureProvider<G, C, T> implements MeasureProvider {
    protected transactionRunner: TransactionRunner = new DisabledTransactionRunner();
    private instrumentedModulesMap = new WeakSet();

    public static makeTransactionData(measureParameters, args): string {
        let result = "";
        if (measureParameters) {
            for (const parameterIndex of measureParameters) {
                const param = args[parameterIndex];
                if (typeof param === "object" && "getMeasureKey" in param) {
                    result += param.getMeasureKey() + ", ";
                } else if (typeof param === "string" || typeof param === "number") {
                    result += param + ",";
                }
            }
        }

        if (result.length > 0) {
            result = result.substring(0, result.length - 1);
        }
        return result;
    }

    private readonly gauges = new Map<string, G>();
    private readonly counters = new Map<string, C>();
    private readonly DEFAULT_DETAILS_PROVIDER = () => "";
    private readonly DEFAULT_CALLBACK_PROVIDER: CallbackProvider = {
        getCallback: (args: IArguments) => args.length > 0 && args[args.length - 1],

        setCallback: (args: IArguments, callback: Function) => {
            args[args.length - 1] = callback;
        },
    };

    private readonly RESULT_PROCESSOR: ResultProcessor = (result, funcArgs, details, timer: () => void) => {
        if (result instanceof Promise) {
            return result.then((resolve) => {
                timer();
                return resolve;
            }).catch((reason) => {
                timer();
                return reason;
            });
        } else if (result instanceof EventEmitter) {
            const emitter = result as EventEmitter;
            for (const event of ["end", "error", "finish"]) {
                emitter.once(event, timer);
            }
        } else {
            timer();
        }
    };

    // eslint-disable-next-line sonarjs/cognitive-complexity
    public baseInstrument(): void {
        this.transactionRunner = createTransactionRunner();
        if (!config.measures.baseInstrument) {
            return;
        }
        const Module = require("module");
        if (this.isInstrumented(Module)) {
            this.markInstrumented(Module);
            return;
        }
        const original = Module._load;
        const self = this;
        Module._load = function(name) {
            let module = original.apply(this, arguments);
            if (module && !self.isInstrumented(module)) {
                if (name === "pg") {
                    module = self.instrumentPostgres(module);
                } else if (name === "http" || name === "https") {
                    module = self.instrumentHttp(module);
                } else if (name === "ioredis") {
                    module = self.instrumentIoRedis(module);
                } else if (name === "redis") {
                    module = self.instrumentRedis(module);
                } else if (name === "kafka-node") {
                    module = self.instrumentKafka(module);
                } else if (name === "express") {
                    module = self.instrumentExpress(module);
                } else if (name === "fastify") {
                    module = self.instrumentFastify(module);
                } else if (name === "@nestjs/platform-fastify") {
                    module = self.instrumentNestFastify(module);
                } else {
                    const instrumentedModule = self.transactionRunner.instrumentModule(name, module);
                    if (instrumentedModule) {
                        module = instrumentedModule;
                        self.markInstrumented(module);
                    }
                }
            }
            return module;
        };
    }

    public instrument(module, name: string, properties?: string[], detailsProvider?: DetailsProvider) {
        const keys = properties || Object.getOwnPropertyNames(module);
        this.markInstrumented(module);
        for (const propertyName of keys) {
            const property = module[propertyName];
            if (typeof property === "function") {
                this.instrumentModuleFunction(module, name, propertyName, property, detailsProvider);
            }
        }
    }

    public instrumentFunction(f: Function, name: string, detailsProvider?: DetailsProvider): Function {
        const gauge: G = this.findOrCreateGauge({ name });
        return this.wrapFunction(f, () => gauge, detailsProvider);
    }

    public incrementGauge(name: string, value: number, details?: string, attributes?: any): void {
        const counter: C = this.findOrCreateCounter(name, attributes);
        this.incrementProviderGauge(counter, value, details, attributes);
    }

    public setGauge(name: string, value: number, details?: string, attributes?: any): void {
        const counter: C = this.findOrCreateCounter(name, attributes);
        this.setProviderGauge(counter, value, details, attributes);
    }

    public isSWError(err) {
        return (err.responseStatus || err.status) && err.code && err.message;
    }

    public saveError(err, attributes?) {
        let errorDescription;
        const errorType = typeof err;
        let type = errorType;
        if (errorType === "object") {
            const errName = err.constructor.name;
            type = errName;
            errorDescription = this.isSWError(err) ?
                               `${errName}:${err.responseStatus || err.status}:${err.code}` :
                               ((err.name !== Error.prototype.name ? err.name : errName) || "error");
        } else {
            errorDescription = `exception:${err}`;
        }

        this.incrementGauge("error", 1, errorDescription, {
            external: err.external || false,
            level: err.level || ERROR_LEVEL.ERROR,
            type,
            status: err.status || err.responseStatus || "",
            code: err.code || "",
            ...(attributes || {}),
        });
    }

    public getTransaction(): string {
        return this.getContextVariable(ContextField.TRANSACTION);
    }

    public setTransaction(name: string): void {
        let owner = this.getContextVariable(ContextField.OWNER, true);
        if (!owner && config.serviceName) {
            owner = config.serviceName;
            this.setContextVariable(ContextField.OWNER, config.serviceName, true);
        }
        if(owner) {
            this.setContextVariable(ContextField.OWNER_TRANSACTION, name, true);
        }

        this.setContextVariable(ContextField.TRANSACTION, name);
        if (!this.getTraceID()) {
            this.setTraceID();
        }
    }

    public getTraceID(): string {
        return this.getContextVariable(ContextField.TRACE_ID);
    }

    public setTraceID(id?: string): void {
        this.setContextVariable(ContextField.TRACE_ID, id || generateTraceId());
    }

    public getURI(): string {
        return this.getContextVariable(ContextField.URI);
    }

    private setURI(uri?: string): void {
        this.setContextVariable(ContextField.URI, uri);
    }

    public getContextVariable<T>(name: string, transferable: boolean = false): T {
        return this.transactionRunner.getContextVariable(name, transferable);
    }

    public setContextVariable<T>(name: string, value: T, transferable: boolean = false): boolean {
        return this.transactionRunner.setContextVariable(name, value, transferable);
    }

    public getContext<T>(transferable: boolean = false): T {
        return this.transactionRunner.getContext(transferable);
    }

    public runInTransaction<T>(name: string, action: (...args) => T): T {
        return this.transactionRunner.runInTransaction(() => {
            this.setTransaction(name);
            return action();
        });
    }

    public trackTransaction<T>(action: MeasurableFunction<T>): MeasurableFunction<T> {
        return this.transactionRunner.trackTransaction(action);
    }

    public getMeasureDecorator(info: MeasureInfo) {
        if (info.debugOnly && !process.env.MEASURES_INCLUDE_DEBUG_ONLY) {
            return this.getDebugMeasurer();
        }

        const gauge: G = this.findOrCreateGauge(info);

        return info.isAsync ? this.getAsyncMeasurer(gauge) : this.getSyncMeasurer(gauge);
    }

    public async getMeasures(): Promise<any> {
        const result = {};
        for (const n of this.gauges.keys()) {
            result[n] = this.getMeasure(n);
        }
        return result;
    }

    public abstract getMeasuresStream(): Promise<Readable> ;

    public async getMeasure(name: string): Promise<any> {
        let result: any = this.gauges.get(name);
        if (!result) {
            result = this.counters.get(name);
        }

        return result;
    }

    protected abstract createGauge(info: MeasureInfo): G;

    protected abstract createProviderGauge(name: string, attributes?: string[]): C;

    protected abstract incrementProviderGauge(counter: C, value: number, details?: string, attributes?: any);

    protected abstract setProviderGauge(counter: C, value: number, details?: string, attributes?: any);

    protected abstract startTimer(gauge: G): T;

    protected abstract endTimer(gauge: G, timer: T, details?: string, ctx?: any);

    protected resolveTransaction(ctx?: any) {
        return ctx ? ctx[ContextField.TRANSACTION] : this.getTransaction();
    }

    protected resolveURI(ctx?: any) {
        return ctx ? ctx.uri : this.getURI();
    }

    protected abstract setUpExpressMiddleware(instance);

    protected abstract setUpFastifyMiddleware(instance);

    protected wrapFunction(original: Function,
                           getGauge: GaugeProvider<G>,
                           detailsProvider: DetailsProvider = this.DEFAULT_DETAILS_PROVIDER,
                           callbackProvider: CallbackProvider = this.DEFAULT_CALLBACK_PROVIDER,
                           resultProcessor: ResultProcessor = this.RESULT_PROCESSOR) {
        const provider = this;
        return function() {
            const funcArgs = arguments;
            const gauge = getGauge.call(getGauge, funcArgs);
            const ctx = provider.transactionRunner.getContext();
            const timer = provider.startTimer(gauge);

            const callback = callbackProvider.getCallback(funcArgs);
            const details = detailsProvider.call(detailsProvider, funcArgs);
            if (typeof callback === "function") {
                const endTimer = function() {
                    provider.endTimer(gauge, timer, details, ctx);
                    return callback.apply(callback, arguments);
                };

                callbackProvider.setCallback(funcArgs, endTimer);
            }

            let result;
            try {
                result = original.apply(this, funcArgs);
            } catch (err) {
                provider.endTimer(gauge, timer, details, ctx);
                throw err;
            }
            resultProcessor(result, funcArgs, details, () => provider.endTimer(gauge, timer, details, ctx));

            return result;
        };
    }

    protected findOrCreateGauge(info: MeasureInfo): G {
        let gauge = this.gauges.get(info.name);
        if (!gauge) {
            gauge = this.createGauge(info);
            this.gauges.set(info.name, gauge);
        }

        return gauge;
    }

    private getDebugMeasurer() {
        return function(target: Object, key: string,
                        descriptor: TypedPropertyDescriptor<any>): TypedPropertyDescriptor<any> {
            return descriptor;
        };
    }

    private getAsyncMeasurer(gauge: G) {
        const provider = this;

        return function(target: Object, key: string,
                        descriptor: TypedPropertyDescriptor<any>): TypedPropertyDescriptor<any> {
            const original: Function = descriptor.value;

            descriptor.value = async function() {
                const measureParameters = Reflect.getOwnMetadata(measures.MetadataLogParamKey, target, key);
                const details = AbstractMeasureProvider.makeTransactionData(measureParameters, arguments);
                const timer: T = provider.startTimer(gauge);
                try {
                    return await original.apply(this, arguments);
                } finally {
                    provider.endTimer(gauge, timer, details);
                }
            };

            return descriptor;
        };
    }

    private getSyncMeasurer(gauge: G) {
        const provider = this;
        return function(target: Object, key: string,
                        descriptor: TypedPropertyDescriptor<any>): TypedPropertyDescriptor<any> {
            const original: Function = descriptor.value;

            descriptor.value = function() {
                const measureParameters = Reflect.getOwnMetadata(measures.MetadataLogParamKey, target, key);
                const details = AbstractMeasureProvider.makeTransactionData(measureParameters, arguments);
                const timer: T = provider.startTimer(gauge);
                try {
                    return original.apply(this, arguments);
                } finally {
                    provider.endTimer(gauge, timer, details);
                }
            };

            return descriptor;
        };
    }

    private instrumentModuleFunction(target,
                                     name: string,
                                     key: string,
                                     original: Function,
                                     detailsExtractor?: (args) => string) {
        const gauge: G = this.findOrCreateGauge({ name: `${name}.${key}` });
        target[key] = this.wrapFunction(original, () => gauge, detailsExtractor);
    }

    private findOrCreateCounter(name: string, attributes?: any): C {
        let counter = this.counters.get(name);
        if (!counter) {
            counter = this.createProviderGauge(name, attributes ? Object.keys(attributes) : []);
            this.counters.set(name, counter);
        }

        return counter;
    }

    private instrumentIoRedis(module) {
        const instrumentPrototype = (proto) => {
            this.instrument(proto, "redis", this.getRedisCommand());
            this.instrument(proto, "redis", ["call", "send_command"], (args) => {
                return args && args[0] || "";
            });
        };

        if (!this.isInstrumented(module.prototype)) {
            instrumentPrototype(module.prototype);
            instrumentPrototype(module.Cluster.prototype);
        }

        return module;
    }

    private instrumentRedis(module) {
        if (!this.isInstrumented(module.RedisClient.prototype)) {
            this.instrument(module.RedisClient.prototype, "redis", this.getRedisCommand().map((i) => i.toUpperCase()));
            this.instrument(module.RedisClient.prototype, "redis", ["command"], (args) => {
                return args && args[0] || "";
            });
        }
        return module;
    }

    private getRedisCommand() {
        return RedisCommandList;
    }

    private instrumentPostgres(module) {
        if (!this.isInstrumented(module.Client.prototype)) {
            this.instrument(module.Client.prototype, "pg", ["query"], (args) => {
                if (args && args.length >= 1) {
                    return SQLOperationParser.getOperation(args[0]);
                }
            });
        }

        return module;
    }

    private instrumentHttp(module) {
        const detailsProvider = (args: any[]) => {
            const options = args[0];
            const traceID = this.getTraceID();
            if (options) {

                const headers = options.headers || {};
                if (headers[EXTERNAL_CALL_HEADER] === undefined || headers[EXTERNAL_CALL_HEADER] !== true) {
                    if (traceID) {
                        headers[TRACE_ID_HEADER] = traceID;
                    }
                    this.setTransferableHeaders((h, v) => headers[h] = v);
                    options.headers = headers;
                }
            }

            return `${options.host || options.hostname}${options.port ? ":" + options.port : ""}`;
        };
        const callbackProvider: CallbackProvider = {
            getCallback: (args: IArguments) => args[0].callback || args[1],

            setCallback: (args: IArguments, callback: Function) => {
                if (args[1]) {
                    args[1] = callback;
                } else {
                    args[0].callback = callback;
                }
            },
        };
        module.request = this.wrapFunction(
            module.request,
            (args) => {
                const method = args[0]?.method || args[1]?.method || "";
                return this.findOrCreateGauge({ name: `request.${method.toLowerCase()}` });
            },
            detailsProvider,
            callbackProvider,
            (result, args, details, timer) => {
                const handleError = (error) => {
                    const errorCode = (error && (error.code || error.constructor.name)) || "unknown";
                    const method = args[0]?.method || args[1]?.method || "";
                    this.incrementGauge(
                        "request_error",
                        1,
                        details,
                        {
                            method: method.toLowerCase(),
                            errorCode,
                        },
                    );
                    const isExternal = args[0] && args[0].headers && args[0].headers[exports.EXTERNAL_CALL_HEADER] === true;
                    if (isExternal && error && error.code && NETWORK_ERRORS.has(error.code)) {
                        error.external = true;
                    }
                    timer();
                };

                if (!result) {
                    timer();
                } else {
                    const res = result as EventEmitter;
                    res.once("error", handleError);
                    res.once("timeout", timer);
                    res.once("abort", timer);
                    res.once("response", (response: EventEmitter) => {
                        response.once("end", timer);
                        response.once("error", handleError);
                    });
                }
            },
        );

        const getGauge = this.findOrCreateGauge({ name: "request.get" });
        module.get = this.wrapFunction(module.get, () => getGauge, detailsProvider, callbackProvider);
        this.markInstrumented(module);

        return module;
    }

    private instrumentKafka(module) {
        if (!this.isInstrumented(module.Producer.prototype)) {
            this.instrument(module.Producer.prototype, "kafka", ["send"]);
        }
        if (!this.isInstrumented(module.Consumer.prototype)) {
            this.instrument(module.Consumer.prototype, "kafka", ["consume"]);
        }

        return module;
    }

    private instrumentExpress(module) {
        const provider = this;
        const router = module.Router;
        const application = module.application;
        const initFunction = application.init;
        application["init"] = function() {
            const result = initFunction.apply(this, arguments);
            this.use((req, res, next) => {
                provider.runInTransaction("", () => {
                    provider.trackRequest(req);
                    next();
                });
            });
            provider.setUpExpressMiddleware(this);
            return result;
        };
        for (const propertyName of HTTP_METHODS) {
            const property = router[propertyName];
            if (typeof property === "function") {
                router[propertyName] = function(path, ...handlers) {
                    const setUpTransaction = (req, res, next) => {
                        provider.setTransaction(`${propertyName.toUpperCase()}:${path}`);
                        provider.setURI(path);
                        const traceId = provider.getTraceID();
                        if (traceId) {
                            res.setHeader(TRACE_ID_HEADER, traceId);
                        }

                        provider.setTransferableHeaders((h, v) => res.setHeader(h, v));

                        return next();
                    };
                    return property.apply(this, [path, setUpTransaction, ...handlers]);
                };
            }
        }
        this.markInstrumented(module);
        return module;
    }

    private markInstrumented(module) {
        this.instrumentedModulesMap.add(module);
    }

    private isInstrumented(module) {
        return this.instrumentedModulesMap.has(module);
    }

    private instrumentFastify(module) {
        const provider = this;
        this.markInstrumented(module);
        function fastify() {
            const instance = module.apply(this, arguments);
            instance.addHook("preHandler", (fastifyReq, fastifyRes, done) => {
                provider.runInTransaction("", () => {
                    provider.trackRequest(fastifyReq);
                    done();
                });
            });
            provider.setUpFastifyMiddleware(instance);
            if (instance["_routePrefix"] === undefined) {
                provider.httpMethodsFastify(provider, instance);
            } else {
                // This code works for all HTTP methods in fastify 1+ version only.
                // TODO: backwards compatibility
                const propertyName = "route";
                const property = instance[propertyName];
                if (typeof property === "function") {
                    instance[propertyName] = function (options) {
                        const trxPath = instance["_routePrefix"] ?
                            `${instance["_routePrefix"]}${options.url}` :
                            `${options.url}`;
                        const handler = options.handler;
                        options.handler = function (req, reply) {
                            const method = Array.isArray(options.method) ? options.method[1] : options.method;
                            provider.setTransaction(`${method.toUpperCase()}:${trxPath}`);
                            provider.setURI(trxPath);
                            reply.header(TRACE_ID_HEADER, provider.getTraceID());
                            if (req && req.raw) {
                                req.raw[CONTEXT_HOLDER] = provider.transactionRunner.getContext();
                            }

                            provider.setTransferableHeaders((h, v) => reply.header(h, v));

                            return handler.apply(this, arguments);
                        };
                        property.apply(this, arguments);
                    };
                }
            }

            return instance;
        }

        fastify.fastify = fastify;
        fastify.default = fastify;
        return fastify;
    }

    private httpMethodsFastify(provider: any, instance: any) {
        for (const m of HTTP_METHODS) {
            const origin: Function = instance[m];
            instance[m] = function(path: string, opts: any, handler?: any) {
                const hookedHandler = (req, res, next) => {
                    const hookTrxData = () => {
                        const method = req?.req?.method || req?.raw?.method;
                        provider.setTransaction(`${method}:${path}`);
                        provider.setURI(path);
                        provider.trackRequest(req);
                        res.header(TRACE_ID_HEADER, provider.getTraceID());
                        if (req && req.raw) {
                            req.raw[CONTEXT_HOLDER] = provider.transactionRunner.getContext();
                        }
                        provider.setTransferableHeaders((h, v) => res.header(h, v));
                        if (typeof opts === "function") {
                            return opts(req, res, next);
                        }
                        return handler(req, res, next);
                    };

                    return (provider.getContext() !== undefined) ?
                        hookTrxData() :
                        provider.runInTransaction(path, hookTrxData);
                };
                if (typeof opts === "function") {
                    return origin.apply(this, [path, hookedHandler]);
                }
                return origin.apply(this, [path, opts, hookedHandler]);
            };
        }
    }
    private instrumentNestFastify(module) {
        const provider = this;
        this.markInstrumented(module);
        const adapter = module.FastifyAdapter.prototype;
        // disabling Middie registration in @nestjs/platform-fastify
        module.FastifyAdapter.prototype.isMiddieRegistered = true;
        this.httpMethodsFastify(provider, adapter);

        return module;
    }

    private trackRequest(req: any) {
        this.trackTransaction(req);
        const traceHeader = req.headers[TRACE_ID_HEADER];
        if (traceHeader) {
            this.setTraceID(traceHeader);
        }
        for (const header of Object.keys(req.headers)) {
            if (header.startsWith(HEADER_PREFIX)) {
                this.transactionRunner.setContextVariable(header.substring(HEADER_PREFIX.length),
                    req.headers[header],
                    true);
            }
        }
    }

    private setTransferableHeaders(setter: (header: string, value: any) => any) {
        if (!config.measures.setTransferableHeaders) {
            return;
        }
        const transferable = this.transactionRunner.getContext(true);
        if (transferable) {
            for (const field of Object.keys(transferable)) {
                const value = transferable[field];
                if (value) {
                    setter(`${HEADER_PREFIX}${field}`, value);
                }
            }
        }
    }
}
