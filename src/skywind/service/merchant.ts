import { Request } from "express";
import { getParamValues, parseBoolean } from "../utils/helper";
import * as Merchants from "../models/merchant";
import { EntityNotFound, InvalidRequest } from "../errors";
import { Merchant, MerchantInfo } from "../models/merchant";

export function getMerchantWithPassword(req: Request, isBonusRequest = false) {
    const merchant = getMerchant(req);
    if (isBonusRequest) {
        return merchant;
    }
    const [merchantPassword] = getParamValues(req, ["merch_pwd"]);
    if (merchant.merch_pwd !== merchantPassword) {
        throw new InvalidRequest("Invalid password");
    }

    return merchant;
}

export function getMerchant(req: Request) {
    const [merchantId] = getParamValues(req, ["merch_id"]);

    return Merchants.getById(merchantId, true);
}

export function updateMerchant(merchantId: string, data: MerchantInfo): Merchant {
    if (typeof data.merch_pwd !== "undefined") {
        Merchants.getById(merchantId).merch_pwd = data.merch_pwd;
    }
    if (typeof data.isPromoInternal !== "undefined") {
        Merchants.getById(merchantId).isPromoInternal = parseBoolean(data.isPromoInternal);
    }
    if (typeof data.multiple_session !== "undefined") {
        Merchants.getById(merchantId).multiple_session = parseBoolean(data.multiple_session);
    }
    return Merchants.getById(merchantId);
}

export function createMerchant(merchantId: string, data: MerchantInfo): Merchant {
    let useSkywindPromos: boolean = false;
    if (typeof data.isPromoInternal !== "undefined") {
        useSkywindPromos = parseBoolean(data.isPromoInternal);
    }

    let multipleSession: boolean = false;
    if (typeof data.multiple_session !== "undefined") {
        multipleSession = parseBoolean(data.multiple_session);
    }

    if (!Merchants.getById(merchantId)) {
        Merchants.setById(merchantId, {
            merch_id: merchantId,
            merch_pwd: data.merch_pwd,
            customers: {},
            isPromoInternal: !!useSkywindPromos,
            multiple_session: multipleSession
        });
    }
    return Merchants.getById(merchantId);
}
