///<reference path="../../skywind/api/routers.ts"/>
import { getApplication } from "../../skywind/server";

import * as SettingsModel from "../../skywind/models/settings";
import { Application } from "express";

const chai = require("chai");
chai.use(require("chai-shallow-deep-equal"));
const expect = chai.expect;

const request = require("supertest");

describe("IPM-mock test other API", async () => {

    let server: Application;
    before(async () => {
        server = await getApplication();
    });

    describe("Settings", () => {
        before(() => {
            SettingsModel.setToDefault();
        });

        it("Get", async () => {

            await request(server)
                .get("/v1/settings")
                .expect(200)
                .then(response => {
                    expect(response.body).to.have.property("aroundAmount", true);
                    expect(response.body).to.have.property("decreaseLoad", false);
                });
        });

        it("Update aroundAmount: false", async () => {

            await request(server)
                .patch("/v1/settings")
                .set("Accept", "application/json")
                .send({
                    aroundAmount: false,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.have.property("aroundAmount", false);
                });
        });

        it("Update aroundAmount: true", async () => {

            await request(server)
                .patch("/v1/settings")
                .set("Accept", "application/json")
                .send({
                    aroundAmount: true,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.have.property("aroundAmount", true);
                });
        });

        it("Update decreaseLoad: false", async () => {

            await request(server)
                .patch("/v1/settings")
                .set("Accept", "application/json")
                .send({
                    decreaseLoad: false,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.have.property("decreaseLoad", false);
                });
        });

        it("Update decreaseLoad: true", async () => {

            await request(server)
                .patch("/v1/settings")
                .set("Accept", "application/json")
                .send({
                    decreaseLoad: true,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.have.property("decreaseLoad", true);
                });
        });

    });
});
