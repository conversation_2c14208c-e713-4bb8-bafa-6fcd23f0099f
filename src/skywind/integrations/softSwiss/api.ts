import { NextFunction, Request, Response, Router } from "express";
import { routerCallbackWrapper } from "../../api/config/middleware";
import { SoftSwissService } from "./service";
import { validatePlayRequestData, validateFreespinsFinish } from "./middleware";
import { RequestEx } from "./entities";
import { RequestAdditionalInfo } from "../everyMatrix/middleware";
import { settings } from "../../models/settings";

const router: Router = Router();

const basicRes = (req: Request) => {
    return {
        balance: settings.amount,
        round_id: req.body.round_id || "mock_game"
    };
};

function notSaveAnyDataMiddleware(responder: (req: Request & RequestAdditionalInfo) => object) {
    return (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req));
            } catch (err) {
                return next(err);
            }
        }
        return next();
    };
}

router.post("/v2/provider_a8r.Player/Balance", notSaveAnyDataMiddleware(basicRes), validatePlayRequestData, routerCallbackWrapper(
    async ({ body, customer, merchant }: RequestEx, res: Response, next: NextFunction) => {
        const service = new SoftSwissService(merchant, customer);
        const balance = await service.balance(body);
        res.send(balance);
        next();
    }));

router.post("/v2/provider_a8r.Round/BetWin", notSaveAnyDataMiddleware(basicRes), validatePlayRequestData, routerCallbackWrapper(
    async ({ body, customer, merchant }: RequestEx, res: Response, next: NextFunction) => {
        const service = new SoftSwissService(merchant, customer);
        const betWin = await service.betWin(body);
        res.send(betWin);
        next();
    }));

router.post("/v2/provider_a8r.Round/Rollback", notSaveAnyDataMiddleware(basicRes), validatePlayRequestData, routerCallbackWrapper(
    async ({ body, customer, merchant }: RequestEx, res: Response, next: NextFunction) => {
        const service = new SoftSwissService(merchant, customer);
        const rollback = await service.rollback(body);
        res.send(rollback);
        next();
    }));

router.post("/v2/provider_a8r.Freespins/Finish", notSaveAnyDataMiddleware(basicRes), validateFreespinsFinish, routerCallbackWrapper(
    async ({ body, customer, merchant }: RequestEx, res: Response, next: NextFunction) => {
        const service = new SoftSwissService(merchant, customer);
        const freeSpinsFinish = await service.freeSpinsFinish(body);
        res.send(freeSpinsFinish);
        next();
    }));

export default router;
