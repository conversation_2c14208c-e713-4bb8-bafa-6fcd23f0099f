import { suite, test } from "mocha-typescript";
import * as uuid from "uuid";
import { expect } from "chai";
import { getApplication } from "../../skywind/relaxServer";
import { Merchant, setAll } from "../../skywind/models/merchant";
import { getRelaxService } from "../../skywind/service/relax";
import * as Transaction from "../../skywind/models/transaction";
import { getTransaction<PERSON>ey, WALLET_ACTION } from "../../skywind/models/transaction";
import config from "../../skywind/config";
import { RelaxBaseSpec } from "./base.spec";
import { INTEGRATION_VERSION } from "../../config/integrationVersion";

const request = require("supertest");

@suite
class RelaxRollbackSpec extends RelaxBaseSpec {
    @test
    public async AuthFail() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/rollback`)
            .auth("invalid", "invalid")
            .send({})
            .expect(401);

        expect(response.body.errorcode).to.be.equal("UNAUTHORIZED");
    }

    @test
    public async merchantNotFound() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/11/rollback`)
            .auth(config.auth.username, config.auth.password)
            .send({})
            .expect(500);

        expect(response.body.errorcode).to.be.equal("UNHANDLED");
    }

    @test
    public async requiredParams() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/rollback`)
            .auth(config.auth.username, config.auth.password)
            .send({})
            .expect(400);

        expect(response.body.errorcode).to.be.equal("INVALID_PARAMETERS");
    }

    @test
    public async playerNotFound() {
        const rollbackRequest = {
            "amount": 100,
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "originaltxid": "QezMtgACiqkAAALXQezMtptkIMI=withdraw",
            "playerid": "notFound",
            "roundid": "203016",
            "sessionid": this.sessionId,
            "txid": "QezMtgACiqkAAALXQezMtptkIMI=rollback"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/rollback`)
            .auth(config.auth.username, config.auth.password)
            .send(rollbackRequest)
            .expect(403);

        expect(response.body.errorcode).to.be.equal("SESSION_EXPIRED");
    }

    @test
    public async sessionNotFound() {
        const rollbackRequest = {
            "amount": 100,
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "originaltxid": "QezMtgACiqkAAALXQezMtptkIMI=withdraw",
            "playerid": this.playerCode,
            "roundid": "203016",
            "sessionid": "invalid",
            "txid": "QezMtgACiqkAAALXQezMtptkIMI=rollback"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/rollback`)
            .auth(config.auth.username, config.auth.password)
            .send(rollbackRequest)
            .expect(403);

        expect(response.body.errorcode).to.be.equal("SESSION_EXPIRED");
    }

    @test
    public async transactionNotFound() {
        const originaltxid = "notFound";
        const txid = "PQ+OAAAChlsAAALXPQ+OAJH5+v=rollback";
        const rollbackRequest = {
            "amount": 100,
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            originaltxid,
            "playerid": this.playerCode,
            "roundid": "203016",
            "sessionid": this.sessionId,
            txid
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/rollback`)
            .auth(config.auth.username, config.auth.password)
            .send(rollbackRequest)
            .expect(404);

        expect(response.body.errorcode).to.be.equal("TRANSACTION_NOT_FOUND");
    }

    @test
    public async rollbackSuccess() {
        const originaltxid = "PQ+OAAAChlsAAALXPQ+OAJH5+v=withdraw";
        Transaction.setAll({
            [getTransactionKey(originaltxid, WALLET_ACTION.debit)]: [originaltxid, -100, uuid.v4(), false]
        });

        const txid = "PQ+OAAAChlsAAALXPQ+OAJH5+v=rollback";
        const rollbackRequest = {
            "amount": 100,
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            originaltxid,
            "playerid": this.playerCode,
            "roundid": "203016",
            "sessionid": this.sessionId,
            txid
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/rollback`)
            .auth(config.auth.username, config.auth.password)
            .send(rollbackRequest)
            .expect(200);

        expect(response.body.txid).to.be.equal(txid);
        expect(response.body.sessionid).to.be.equal(this.sessionId);
        expect(response.body.relaxtxid).to.be.exist;

    }

    @test
    public async rollbackShouldBeIdempotency() {
        const originaltxid = "PQ+OAAAChlsAAALXPQ+OAJH5+v=withdraw";
        const txid = "PQ+OAAAChlsAAALXPQ+OAJH5+v=withdraw";
        const externalId = uuid.v4();
        Transaction.setAll({
            [getTransactionKey(originaltxid, WALLET_ACTION.debit)]: [originaltxid, -100, uuid.v4(), true],
            [getTransactionKey(txid, WALLET_ACTION.rollback)]: [txid, 0, externalId, false],
        });

        const rollbackRequest = {
            "amount": 100,
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            originaltxid,
            "playerid": this.playerCode,
            "roundid": "203016",
            "sessionid": this.sessionId,
            txid
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/rollback`)
            .auth(config.auth.username, config.auth.password)
            .send(rollbackRequest)
            .expect(200);

        expect(response.body.txid).to.be.equal(txid);
        expect(response.body.sessionid).to.be.equal(this.sessionId);
        expect(response.body.relaxtxid).to.be.equal(externalId);
    }
}
