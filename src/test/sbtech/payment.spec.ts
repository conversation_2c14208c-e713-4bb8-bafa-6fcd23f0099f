import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");

import { SBTechBaseSpec } from "./base.spec";
import { SBTECH_ERROR_NAME } from "../../skywind/errorsSBTech";
import { WALLET_TYPE } from "../../skywind/entities/sbtech";

@suite("SBTech.payment")
class SBTechPaymentSpec extends SBTechBaseSpec {

    @test("should make DEBIT operation")
    public async makeDebit() {
        const expectedResponse = {
            walletType: WALLET_TYPE.REAL,
            balance: 5000,
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToDebit)
            .send(this.fixtures.debitRequest)
            .expect(200);

        delete actualResponse.body.sessionId;
        delete actualResponse.body.timestamp;
        delete actualResponse.body.trxId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make CREDIT operation")
    public async makeCredit() {
        const expectedResponse = {
            balance: 15000,
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToCredit)
            .send(this.fixtures.creditRequest)
            .expect(200);

        delete actualResponse.body.sessionId;
        delete actualResponse.body.timestamp;
        delete actualResponse.body.trxId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make DEBIT and CREDIT operations")
    public async makeDebitAndCredit() {
        const expectedResponse = {
            balance: 10000,
        };

        await request(this.server)
            .post(this.urlPathToDebit)
            .send(this.fixtures.debitRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .post(this.urlPathToCredit)
            .send(this.fixtures.creditRequest)
            .expect(200);

        delete actualResponse.body.sessionId;
        delete actualResponse.body.timestamp;
        delete actualResponse.body.trxId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("throw error when required fields are missing or empty in DEBIT request")
    public async throwErrorWhenInvalidFieldsOnDebit() {
        const debitRequest = Object.assign(this.fixtures.debitRequest,
            { providerKey: "" });

        const actualResponse = await request(this.server)
            .post(this.urlPathToCredit)
            .send(debitRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.INTERNAL_ERROR);
    }

    @test("throw error when required fields are missing or empty in CREDIT request")
    public async throwErrorWhenInvalidFieldsOnCredit() {
        const creditRequest = Object.assign(this.fixtures.creditRequest,
            { providerKey: "" });

        const actualResponse = await request(this.server)
            .post(this.urlPathToCredit)
            .send(creditRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.INTERNAL_ERROR);
    }

    @test("throw error when such DEBIT transaction exists")
    public async throwErrorWhenDebitTransactionExists() {
        await request(this.server)
            .post(this.urlPathToDebit)
            .send(this.fixtures.debitRequest);

        const actualResponse = await request(this.server)
            .post(this.urlPathToDebit)
            .send(this.fixtures.debitRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.INTERNAL_ERROR);
    }

    @test("throw error when player's balance is insufficient in DEBIT request")
    public async throwErrorWhenPlayerBalanceIsInsufficient() {
        const debitRequest = Object.assign(this.fixtures.debitRequest,
            { amount: 15000 });

        const actualResponse = await request(this.server)
            .post(this.urlPathToDebit)
            .send(debitRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.INSUFFICIENT_FUNDS_REAL_MONEY);
    }
}
