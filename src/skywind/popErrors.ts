import { BaseErrorToLog, ErrorInfoToLog } from "./errors";

export interface POPError extends BaseErrorToLog {
    responseStatus: number;
    errorCode: string;
    message: string;
    errorMsg: string;
}

export class POPBaseError extends Error implements POPError {
    public responseStatus: number;  // use field name that doesn't interfere with fastify ('status' or 'statusCode')
    public errorCode: string;
    public errorMsg: string;
    public extraData: any;

    constructor(status: number, errorMsg: string, errorCode?: string, extraData?: any) {
        super(errorMsg);
        this.responseStatus = status;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.extraData = extraData;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.errorCode,
                message: this.errorMsg,
                stack: this.stack
            }
        };
    }
}

export class ValueIsMissing extends POPBaseError {
    constructor(value: string) {
        super(400, `${value} is missing`, "ERR003");
    }
}

export class InvalidRequest extends POPBaseError {
    constructor(message: string) {
        super(400, message, "ERR003");
    }
}

export class EntityNotFound extends POPBaseError {
    constructor(entityType: string, entityId: string) {
        super(400, `cannot find ${entityType} ${entityId}`);
    }
}

export class TransactionFailed extends POPBaseError {
    constructor() {
        super(400, "Transaction error", "ERR028");
    }
}

export class TransactionToCancelNotFound extends POPBaseError {
    constructor() {
        super(400, "Transaction to cancel not found", "ERR1022");
    }
}

export class ValueNotFound extends POPBaseError {
    constructor(value: string) {
        super(400, `${value} not found`, "ERR003");
    }
}

export class PlayerNotFound extends POPBaseError {
    constructor(customerId: string = "") {
        super(404, `Customer ${customerId} not found`, "ERR036");
    }
}

export class PlayerIsSuspended extends POPBaseError {
    constructor() {
        super(404, "Player is suspended", "ERR026");
    }
}

export class GameTokenExpired extends POPBaseError {
    constructor(message) {
        super(400, message || "Customer ticket or session not found", "ERR1004");
    }
}

export class BetLimitExceeded extends POPBaseError {
    constructor(message) {
        super(400, "Bet limit Exceeded", "ERR027");
    }
}

export class InsufficientBalance extends POPBaseError {
    constructor() {
        super(400, "Insufficient balance", "ERR025");
    }
}

export class MethodWorksOnlyWithJSON extends POPBaseError {
    constructor() {
        super(400, "Method works only with JSON");
    }
}

export class Forbidden extends POPBaseError {
    constructor(message?: string) {
        super(403, message || "Operation forbidden");
    }
}

export class InvalidSecureToken extends POPBaseError {
    constructor() {
        super(400, "Invalid secure token", "ERR022");
    }
}

export class SessionExpired extends POPBaseError {
    constructor() {
        super(400, "Session expired", "ERR1004");
    }
}

export class IncorrectPlayerIdOrToken extends POPBaseError {
    constructor() {
        super(400, "Incorrect playerId or parameters for secure token", "ERR036");
    }
}

export class IncorrectMoneyTransaction extends POPBaseError {
    constructor(field: string | string[]) {
        super(400, `Incorrect money trans. field : ${field}`, "ERR036");
    }
}

export class IncompleteRequest extends POPBaseError {
    constructor() {
        super(400, "Invalid remote service identifier.", "ERR004");
    }
}

export class InvalidGameType extends POPBaseError {
    constructor() {
        super(400, "First bet in round must be with gameType = 'normal'", "ERR034");
    }
}
