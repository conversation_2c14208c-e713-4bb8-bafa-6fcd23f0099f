import { getParamValues } from "../../utils/helper";
import { Request } from "express";
import {
    BetLimitExceeded,
    DuplicateTransaction, DuplicateTrxIdTransaction, GameTokenExpired, InsufficientBalance,
    InsufficientFreebets, InvalidFreebet,
    InvalidRequest,
    ValueIsMissing
} from "../../errors";
import { Customer, Merchant } from "../../models/merchant";
import * as TransactionModel from "../../models/transaction";
import { validateCustomerSession } from "../../service/session";
import { settings } from "../../models/settings";
import { TRANSACTION_INDEX, WALLET_ACTION } from "../../models/transaction";
import { body, validationResult } from "express-validator/check";
import { Currencies } from "@skywind-group/sw-currency-exchange";
const platforms = [
    "web",
    "mobile"
];
const gameTypes = [
    "normal",
    "freegame",
    "bonusgame"
];
const gameStatuses = [
    "settled",
    "bonusgame",
    "freegame"
];

const promoTypes = ["tournament", "bonus_coin", "shared_jp_prize", "freebet", "prize_win", undefined];

export const pageTypes: string[] = ["login", "registration", "playerinfo", "password"];
export const defaultPageType = "login";

export function validatePromoType(promoType: string): void {
    if (promoTypes.includes(promoType)) {
        return;
    }
    throw new InvalidRequest(`promo type ${promoType} not supported`);
}

export function validateCurrency(req: Request, customer: Customer) {
    const [currencyCode, trxId]: string[] = getParamValues(req, ["currency_code", "trx_id"]);
    if (!currencyCode) {
        throw new ValueIsMissing("currency_code");
    }
    if (customer.balance.currency_code !== currencyCode) {
        throw new InvalidRequest("currency_code does not match");
    }
}

export function validateWalletOperation(req: Request,
                                        merchant: Merchant,
                                        customer: Customer,
                                        action: TransactionModel.WALLET_ACTION) {
    const amount = +(req.query.amount || req.body.amount);
    if (!Number.isFinite(amount) || amount < 0) {
        throw new InvalidRequest("Amount is invalid value");
    }

    if (amount % 1 !== 0) {
        const reqCurrency = req.query.currency_code || req.body.currency_code;
        const currency = Currencies.get(reqCurrency);
        const fixedAmount = currency.toFixedByExponent(amount);
        if (amount !== fixedAmount) {
            throw new InvalidRequest("Amount has invalid number of decimals");
        }
    }

    if (action === TransactionModel.WALLET_ACTION.debit &&
        customer.bet_limit !== null && (amount > customer.bet_limit)) {
        throw new BetLimitExceeded();
    }

    validateCurrency(req, customer);

    const [
        trxId, gameId, currencyCode, gameCode, eventId, gameType, platform, roundId, eventType
    ] = getParamValues(
        req,
        [
            "trx_id", "game_id", "currency_code", "game_code", "event_id",
            "game_type",
            "platform",
            "round_id",
            "event_type"
        ]);

    const transaction = TransactionModel.getById(trxId, action);
    if (transaction) {
        const ipmTransactionData = transaction[TRANSACTION_INDEX.IPM_TRANSACTION_DATA];
        if (ipmTransactionData.roundId === roundId &&
            ipmTransactionData.platform === platform &&
            ipmTransactionData.gameType === gameType &&
            ipmTransactionData.eventId === eventId &&
            ipmTransactionData.gameId === gameId &&
            ipmTransactionData.gameCode === gameCode &&
            ipmTransactionData.currencyCode === currencyCode &&
            transaction[TRANSACTION_INDEX.CUSTOMER_ID] === customer.cust_id &&
            Math.abs(transaction[TRANSACTION_INDEX.AMOUNT]) === amount) {
            throw new DuplicateTransaction({
                balance: customer.balance.amount,
                trx_id: trxId,
                free_bet_count: customer.freeBets && customer.freeBets.count
            });
        } else {
            throw new DuplicateTrxIdTransaction({
                balance: customer.balance.amount,
                trx_id: trxId,
                free_bet_count: customer.freeBets && customer.freeBets.count
            });
        }
    }

    if (platforms.indexOf(platform) === -1) {
        throw new InvalidRequest("platform is invalid value");
    }
    if (gameTypes.indexOf(gameType) === -1) {
        throw new InvalidRequest("game_type is invalid value");
    }

    if (action === WALLET_ACTION.credit) {
        getParamValues(req, ["game_status"]);

        // in some cases win may not be accompanied by bet
        if (mustCheckBetTrxCameBeforeWin(req)) {
            const betTransaction = TransactionModel.getById(trxId, WALLET_ACTION.debit);

            // throw error if it's win without bet
            if (!settings.decreaseLoad && !betTransaction) {
                throw new InvalidRequest("Win without bet is not allowed");
            }
        }
    }

    if (eventType === "free-bet" && amount) {
        const freeBetCoin = +(req.query.free_bet_coin || req.body.free_bet_coin);
        validateFreeBets(merchant, customer, freeBetCoin);
    } else if (amount > 0 && (customer.balance.amount + (amount * action) < 0)) {
        throw new InsufficientBalance();
    }
}

// As in SWS-27662 we introduced dontSendZeroPayments flag that disables sending of 0-bets, then
// we have to disable validation for having bet before win. Gcp redeem also do not have bet transaction.
function mustCheckBetTrxCameBeforeWin(req: Request): boolean {
    return false;
}

function isGCPRedeem(req: Request): boolean {
    return Boolean(req.body.grc_redeem_info);
}

export function validateFreeBets(merchant: Merchant, customer: Customer, freeBetCoin: number) {
    if (!merchant.isPromoInternal) {
        if (!customer.freeBets || customer.freeBets.count < 1) {
            throw new InsufficientFreebets();
        }

        if (customer.freeBets.coin !== freeBetCoin) {
            throw new InvalidFreebet();
        }
    }
}

export function validateCustomerSessionId(customerSessionId, isFinalization = false) {
    if (!customerSessionId) {
        throw new ValueIsMissing("cust_session_id");
    }
    if (!validateCustomerSession(customerSessionId, isFinalization)) {
        throw new GameTokenExpired("cust_session_id is expired");
    }
}

export function validateGetLobbyPageRequest(req: Request) {
    getParamValues(req, ["page_type", "lobby_id"]);
    if (req.body.page_type === "playerinfo" && !req.body.token) {
        throw new ValueIsMissing("token");
    }
    if (!pageTypes.includes(req.body.page_type)) {
        throw new InvalidRequest("page_type is unknown");
    }
    return true;
}

export function getRegisterRoundValidators() {

    return [body("game_code").isString(),
        body("round_id").isString(),
        body("total_bet").isInt({min: 0}),
        body("total_win").optional().isInt({min: 0}),
        body("win_count").optional().isInt({min: 0}),
        body("bet_count").optional().isInt({min: 0}),
        body("started_at").isInt(),
        body("merch_id").optional().isString(),
        body("account_id").isString(),
        (req, res, next) => {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            }
            next();
        }
    ];

}
