#!/bin/bash

# Seamless Wallet + IMP-Mock Integration Test Runner
# This script runs all Hurl test scripts in the correct order with proper error handling

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_FILE="${SCRIPT_DIR}/environments/local.env"
RESULTS_DIR="${SCRIPT_DIR}/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${RESULTS_DIR}/test_run_${TIMESTAMP}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test scripts in execution order
TESTS=(
    "seamless-wallet-game-flow.hurl"
    "seamless-wallet-error-scenarios.hurl"
    "seamless-wallet-bonus-flow.hurl"
    "seamless-wallet-advanced-features.hurl"
)

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --env FILE          Environment file to use (default: environments/local.env)"
    echo "  -t, --test SCRIPT       Run specific test script only"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -h, --help              Show this help message"
    echo "  --staging               Use staging environment"
    echo "  --no-cleanup            Skip cleanup after tests"
    echo "  --parallel              Run tests in parallel (experimental)"
    echo ""
    echo "Examples:"
    echo "  $0                                          # Run all tests with local env"
    echo "  $0 --staging                                # Run all tests with staging env"
    echo "  $0 -t seamless-wallet-game-flow.hurl       # Run specific test"
    echo "  $0 -v                                       # Run with verbose output"
}

# Function to check prerequisites
check_prerequisites() {
    print_status $BLUE "Checking prerequisites..."
    
    # Check if Hurl is installed
    if ! command -v hurl &> /dev/null; then
        print_status $RED "Error: Hurl is not installed. Please install from https://hurl.dev/"
        exit 1
    fi
    
    # Check if environment file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        print_status $RED "Error: Environment file not found: $ENV_FILE"
        exit 1
    fi
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    print_status $GREEN "Prerequisites check passed"
}

# Function to check service health
check_services() {
    print_status $BLUE "Checking service health..."
    
    # Source environment variables
    source "$ENV_FILE"
    
    # Check Management API
    if curl -s -f "${MAPI_URL}/health" > /dev/null 2>&1; then
        print_status $GREEN "✓ Management API is healthy"
    else
        print_status $YELLOW "⚠ Management API health check failed"
    fi
    
    # Check IMP-Mock Service
    if curl -s -f "${MOCK_URL}/health" > /dev/null 2>&1; then
        print_status $GREEN "✓ IMP-Mock Service is healthy"
    else
        print_status $YELLOW "⚠ IMP-Mock Service health check failed"
    fi
    
    # Check Terminal Service
    if curl -s -f "${TERMINAL_URL}/health" > /dev/null 2>&1; then
        print_status $GREEN "✓ Terminal Service is healthy"
    else
        print_status $YELLOW "⚠ Terminal Service health check failed"
    fi
    
    # Check Wallet Service
    if curl -s -f "${WALLET_URL}/health" > /dev/null 2>&1; then
        print_status $GREEN "✓ Wallet Service is healthy"
    else
        print_status $YELLOW "⚠ Wallet Service health check failed"
    fi
}

# Function to run a single test
run_test() {
    local test_file=$1
    local test_name=$(basename "$test_file" .hurl)
    local result_file="${RESULTS_DIR}/${test_name}_${TIMESTAMP}.json"
    
    print_status $BLUE "Running test: $test_name"
    
    local hurl_args=(
        --variables-file "$ENV_FILE"
        --json
        --output "$result_file"
    )
    
    if [[ "$VERBOSE" == "true" ]]; then
        hurl_args+=(--verbose)
    fi
    
    if hurl "${hurl_args[@]}" "$test_file" >> "$LOG_FILE" 2>&1; then
        print_status $GREEN "✓ $test_name passed"
        return 0
    else
        print_status $RED "✗ $test_name failed"
        return 1
    fi
}

# Function to run all tests
run_all_tests() {
    local failed_tests=()
    local passed_tests=()
    
    print_status $BLUE "Starting test execution..."
    echo "Test run started at $(date)" > "$LOG_FILE"
    
    for test in "${TESTS[@]}"; do
        if [[ ! -f "$test" ]]; then
            print_status $YELLOW "Warning: Test file not found: $test"
            continue
        fi
        
        if run_test "$test"; then
            passed_tests+=("$test")
        else
            failed_tests+=("$test")
        fi
        
        echo "" # Add spacing between tests
    done
    
    # Print summary
    print_status $BLUE "Test Summary:"
    print_status $GREEN "Passed: ${#passed_tests[@]}"
    print_status $RED "Failed: ${#failed_tests[@]}"
    
    if [[ ${#failed_tests[@]} -gt 0 ]]; then
        print_status $RED "Failed tests:"
        for test in "${failed_tests[@]}"; do
            echo "  - $test"
        done
        return 1
    else
        print_status $GREEN "All tests passed!"
        return 0
    fi
}

# Function to cleanup test data
cleanup() {
    if [[ "$NO_CLEANUP" == "true" ]]; then
        print_status $YELLOW "Skipping cleanup as requested"
        return
    fi
    
    print_status $BLUE "Cleaning up test data..."
    
    # Source environment variables
    source "$ENV_FILE"
    
    # Cleanup merchants (if cleanup endpoints exist)
    local merchants=("seamless_test_merchant" "promo_merchant" "advanced_merchant" "test_merchant_dup")
    
    for merchant in "${merchants[@]}"; do
        if curl -s -X DELETE "${MOCK_URL}/v1/merchant/${merchant}" > /dev/null 2>&1; then
            print_status $GREEN "✓ Cleaned up merchant: $merchant"
        fi
    done
    
    print_status $GREEN "Cleanup completed"
}

# Function to generate test report
generate_report() {
    local report_file="${RESULTS_DIR}/test_report_${TIMESTAMP}.html"
    
    print_status $BLUE "Generating test report..."
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Seamless Wallet Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .passed { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .failed { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Seamless Wallet Integration Test Report</h1>
        <p class="timestamp">Generated: $(date)</p>
        <p>Environment: $(basename "$ENV_FILE")</p>
    </div>
    
    <h2>Test Results</h2>
EOF
    
    # Add test results to report
    for test in "${TESTS[@]}"; do
        local test_name=$(basename "$test" .hurl)
        local result_file="${RESULTS_DIR}/${test_name}_${TIMESTAMP}.json"
        
        if [[ -f "$result_file" ]]; then
            echo "    <div class=\"test-result passed\">" >> "$report_file"
            echo "        <h3>✓ $test_name</h3>" >> "$report_file"
            echo "        <p>Status: Passed</p>" >> "$report_file"
            echo "    </div>" >> "$report_file"
        else
            echo "    <div class=\"test-result failed\">" >> "$report_file"
            echo "        <h3>✗ $test_name</h3>" >> "$report_file"
            echo "        <p>Status: Failed</p>" >> "$report_file"
            echo "    </div>" >> "$report_file"
        fi
    done
    
    echo "</body></html>" >> "$report_file"
    
    print_status $GREEN "Test report generated: $report_file"
}

# Parse command line arguments
VERBOSE=false
SPECIFIC_TEST=""
NO_CLEANUP=false
PARALLEL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV_FILE="$2"
            shift 2
            ;;
        -t|--test)
            SPECIFIC_TEST="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --staging)
            ENV_FILE="${SCRIPT_DIR}/environments/staging.env"
            shift
            ;;
        --no-cleanup)
            NO_CLEANUP=true
            shift
            ;;
        --parallel)
            PARALLEL=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            print_status $RED "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status $BLUE "Seamless Wallet Integration Test Runner"
    print_status $BLUE "======================================="
    
    check_prerequisites
    check_services
    
    if [[ -n "$SPECIFIC_TEST" ]]; then
        print_status $BLUE "Running specific test: $SPECIFIC_TEST"
        if run_test "$SPECIFIC_TEST"; then
            print_status $GREEN "Test completed successfully"
            exit_code=0
        else
            print_status $RED "Test failed"
            exit_code=1
        fi
    else
        if run_all_tests; then
            exit_code=0
        else
            exit_code=1
        fi
    fi
    
    generate_report
    cleanup
    
    print_status $BLUE "Test execution completed. Check log file: $LOG_FILE"
    exit $exit_code
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
