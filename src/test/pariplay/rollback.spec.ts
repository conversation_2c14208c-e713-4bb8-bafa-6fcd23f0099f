import { suite, test } from "mocha-typescript";
import * as uuid from "uuid";
import { expect } from "chai";
import * as Transaction from "../../skywind/models/transaction";
import { getTransaction<PERSON>ey, WALLET_ACTION } from "../../skywind/models/transaction";
import config from "../../skywind/config";
import { PariplayBaseSpec } from "./auth.spec";
import { PariplayPaymentResponse } from "../../skywind/entities/pariplay";
import { ACTION } from "../../skywind/service/pariplay/actions";

const request = require("supertest");

@suite
class PariplayRollbackSpec extends PariplayBaseSpec {
    @test
    public async testRollbackSuccess() {
        Transaction.setById(this.trxId, WALLET_ACTION.debit, Math.abs(this.debitRequest.Amount), this.playerCode);
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/CancelBet")
            .send(this.rollbackRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount + 100);
        expect(response.body.TransactionId).to.be.equal(this.rollbackTrxId);
    }

    @test
    public async testMerchantNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/CancelBet")
            .send({
                ...this.rollbackRequest,
                Account: { Username: "invalidMerch", Password: "SomePwd"}
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(2);
    }

    @test
    public async testPlayerNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/CancelBet")
            .send({
                ...this.rollbackRequest,
                PlayerId: "invalidPlayer"
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async testSessionNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/CancelBet")
            .send({
                ...this.rollbackRequest,
                Token: "invalidSession"
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async testNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/CancelBet")
            .send(this.rollbackRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(7);

    }

    @test
    public async testRollbackShouldBeIdempotency() {
        Transaction.setAll({
            [getTransactionKey(this.trxId, WALLET_ACTION.debit)]: [this.trxId, -100, uuid.v4(), true],
            [getTransactionKey(this.rollbackTrxId, WALLET_ACTION.rollback)]: [this.rollbackTrxId, 100, uuid.v4(), true]
        });

        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/CancelBet")
            .send(this.rollbackRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount);
        expect(response.body.TransactionId).to.be.equal(this.rollbackTrxId);
    }

    @test
    public async testCustomError() {
        Transaction.setAll({
            [getTransactionKey(this.trxId, WALLET_ACTION.debit)]: [this.trxId, -100, uuid.v4(), true],
            [getTransactionKey(this.rollbackTrxId, WALLET_ACTION.rollback)]: [this.rollbackTrxId, 100, uuid.v4(), true],
        });
        this.createCustomError(900, ACTION.CANCEL_BET);
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/CancelBet")
            .send(this.rollbackRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(900);
    }
}
