import { WALLET_ACTION } from "./transaction";

export interface RoundInfo {
    gameId: string;
    merchantId: string;
    customerId: string;
    totalBet: number;
    totalWin: number;
}

export interface CreateRoundInfo {
    gameId: string;
    merchantId: string;
    customerId: string;
}

let rounds = [];
const registerRounds = {};
export function createOrUpdate(roundInfo: CreateRoundInfo,
                               action: WALLET_ACTION,
                               balanceChange: number,
                               totalBetForRoundStatsAction?: number,
                               totalWinForRoundStatsAction?: number) {
    const alreadyExists = findOne(roundInfo.merchantId, roundInfo.customerId, roundInfo.gameId);

    if (alreadyExists) {
        rounds = rounds.map(round => {
            if (round.gameId === roundInfo.gameId) {
                if (action === WALLET_ACTION.closeRoundWithRoundStats) {
                    round.totalBet = +totalBetForRoundStatsAction;
                    round.totalWin = +totalWinForRoundStatsAction;
                } else if (action === WALLET_ACTION.debit) {
                    round.totalBet += +balanceChange;
                } else {
                    round.totalWin += +balanceChange;
                }
            }
            return round;
        });
    } else {
        const newRound = {
            ...roundInfo,
            totalBet: 0,
            totalWin: 0
        };

        if (action === WALLET_ACTION.closeRoundWithRoundStats) {
            newRound.totalBet = +totalBetForRoundStatsAction;
            newRound.totalWin = +totalWinForRoundStatsAction;
        } else if (action === WALLET_ACTION.debit) {
            newRound.totalBet = +balanceChange;
        } else {
            newRound.totalWin = +balanceChange;
        }

        rounds.push(newRound);
    }
}

export function findOne(merchantId: string, customerId: string, gameId: string) {
    return rounds.find(round => round.gameId === gameId &&
        round.merchantId === merchantId &&
        round.customerId === customerId);
}

export function clearTotalBetWin(gameId: string) {
   rounds = rounds.map(round => {
       if (round.gameId === gameId) {
           round.totalBet = 0;
           round.totalWin = 0;
       }
       return round;
   });
}

export function setRegisterRound( value ) {
    const id = `${value.account_id}_${value.round_id}`;
    registerRounds[id] = value;
    delete registerRounds[id].account_id;

    return registerRounds[id];
}
export function getRegisterRound(account_id: string, round_id: string) {
    return registerRounds[`${account_id}_${round_id}`] || { error_code: -8383, error_msg: "account || round not found"};
}
