import { measures } from "./measures";

import * as prometheus from "prom-client";
import { AbstractMeasureProvider, CONTEXT_HOLDER } from "./provider";
import { lazy } from "../lazy";
import { Readable } from "node:stream";
import MeasureInfo = measures.MeasureInfo;
import config from "../config";

type PrometheusTimer = (labels?: object) => void;

class PrometheusProvider extends AbstractMeasureProvider<MeasureInfo, prometheus.Gauge, PrometheusTimer> {

    private readonly summary = new prometheus.Summary({
        name: "sw_operation", help: "Operation", labelNames: ["details", "transaction", "operation"], percentiles: [],
    });

    private readonly prometheusMiddleware = lazy(() => {

        const promBundle = require("express-prom-bundle");
        const promBundleOpts = {
            buckets: [],
            includeMethod: true,
            autoregister: false,
            includePath: true,
            includeUp: false,
            customLabels: { transaction: undefined },
            transformLabels: (labels, req) => {
                const context = req[CONTEXT_HOLDER];
                if (labels.path === undefined) {
                    const path = this.resolveURI(context);
                    labels.path = path ? path : "UNKNOWN";
                }
                const prefixes: string[] = config.measures.allowedPrefixes;
                if (prefixes?.length) {
                    const path = labels.path as string;
                    labels.path = prefixes.some((prefix) => path.startsWith(prefix)) ? path : "FORBIDDEN";
                }
                if (labels.transaction === undefined) {
                    labels.transaction = this.resolveTransaction(context) || `${labels.method}:${labels.path}`;
                }
                return labels;
            },
            promClient: {
                collectDefaultMetrics: {
                    timeout: 1000,
                },
            },
        };

        if (config.measures.normalizedPaths.length) {
            promBundleOpts["normalizePath"] = config.measures.normalizedPaths;
        }

        return promBundle(promBundleOpts);
    });

    protected async *generateMetrics() {
        const metrics = prometheus.register.getMetricsAsArray();
        for (const metric of metrics) {
            const info = await prometheus.register.getSingleMetricAsString(metric.name);
            yield info + "\n";
        }
    }

    public async getMeasuresStream(): Promise<Readable> {
        return Readable.from(this.generateMetrics(), { highWaterMark: 1024, objectMode: false });
    }

    protected createGauge(info: MeasureInfo): MeasureInfo {
        return info;
    }

    protected startTimer(gauge: MeasureInfo): PrometheusTimer {
        return this.summary.startTimer();
    }

    protected createProviderGauge(name: string, attributes: string[]): prometheus.Gauge {
        return new prometheus.Gauge({ name, help: name, labelNames: ["transaction", "details", ...attributes].sort() });
    }

    protected setUpExpressMiddleware(instance) {
        instance.use(this.prometheusMiddleware.get());
    }

    protected setUpFastifyMiddleware(instance: any) {
        if (instance.isMiddieRegistered) {
            return;
        }

        instance.isMiddieRegistered = true;
        if (instance.use) {
            // < v3
            instance.use(this.prometheusMiddleware.get());
            return;
        }
        // > v3
        instance.register(require("@fastify/middie")).after(() => {
            instance.use(this.prometheusMiddleware.get());
        });
    }

    protected incrementProviderGauge(counter: prometheus.Gauge,
                                     value: number,
                                     details: string,
                                     attributes?: any) {
        const transaction = this.resolveTransaction();
        const labels: any = {};

        if (details) {
            labels.details = details;
        }

        if (transaction) {
            labels.transaction = transaction;
        }
        if (attributes) {
            Object.assign(labels, attributes);
        }

        this.checkLabels(counter, labels);
        counter.inc(labels, value);
    }

    protected setProviderGauge(counter: prometheus.Gauge, value: number, details: string, attributes?: any) {
        const transaction = this.resolveTransaction();
        const labels: any = {};

        if (details) {
            labels.details = details;
        }

        if (transaction) {
            labels.transaction = transaction;
        }
        if (attributes) {
            Object.assign(labels, attributes);
        }
        counter.set(labels, value);
    }

    protected endTimer(gauge: MeasureInfo,
                       timer: PrometheusTimer,
                       details?: string,
                       ctx?: any) {
        const transaction = this.resolveTransaction(ctx);
        const labels: any = { operation: gauge.name };

        if (details) {
            labels.details = details;
        }
        if (transaction) {
            labels.transaction = transaction;
        }
        timer(labels);
    }

    private checkLabels(counter, labels: any): void {
        const currentLabels: string[] = counter.labelNames;
        const newLabels: string[] = Object.keys(labels).sort();
        if (newLabels.length !== currentLabels.length) {
            counter.labelNames = newLabels;
        } else {
            for (let i = 0; i < newLabels.length; i++) {
                if (newLabels[i] !== currentLabels[i]) {
                    counter.labelNames = newLabels;
                    break;
                }
            }
        }
    }
}

export default new PrometheusProvider();
