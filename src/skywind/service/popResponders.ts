import { Request } from "express";
import { getDataFromTemplateTicket } from "./ticket";
import { settings } from "../models/settings";
import { MerchantHolder } from "../entities/common";
import {
    CancelTransactionsResponse,
    MoneyTransactionResponse,
    PlayerBalanceResponse,
    PlayerInfoResponse,
    VerifyPlayerSessionResponse
} from "../scheme/pop/protocol";

export const verifyPlayerSessionResponder = (req: Request & MerchantHolder): VerifyPlayerSessionResponse => {
    const { secureToken } = req.body;
    const [custId, currency] = getDataFromTemplateTicket(secureToken);
    const playerId = custId || Date.now().toString();

    return {
        secureToken: secureToken,
        accountBalance: {
            playerId: playerId,
            accountId: "",
            currencyCode: currency,
            balanceArray: [
                {
                    balanceType: "cashable",
                    balanceAmt: settings.amount
                }
            ]
        }
    };
};

export const playerInfoResponder = (req: Request & MerchantHolder): PlayerInfoResponse => {
    const { playerId, secureToken } = req.body;
    const [currency] = getDataFromTemplateTicket(secureToken);

    return {
        playerId,
        currencyCode: currency,
        countryCode: "DE"
    };
};

export const getPlayerBalanceResponder = (req: Request & MerchantHolder): PlayerBalanceResponse => {
    const [custId, currency] = getDataFromTemplateTicket(req.body.secureToken);

    return {
        accountBalance: {
            playerId: custId,
            accountId: "",
            currencyCode: currency,
            balanceArray: [
                {
                    balanceType: "cashable",
                    balanceAmt: settings.amount
                }
            ]
        }
    };
};

export const moneyTransactionResponder = (req: Request & MerchantHolder): MoneyTransactionResponse => {
    const { secureToken, moneyTransArray, playerId, gameCycleId, currencyCode } = req.body;
    const [custId] = getDataFromTemplateTicket(secureToken);

    return {
        gameCycleId,
        playerId,
        accountBalance: {
            playerId: custId,
            accountId: "",
            currencyCode: currencyCode,
            balanceArray: [
                {
                    balanceType: "cashable",
                    balanceAmt: settings.amount
                }
            ]
        },
        logout: false,
        moneyAckArray: [{
            ...moneyTransArray[0],
            moneyDetailArray: []
        }],
        errorCode: "",
        errorMsg: ""
    };
};

export const cancelTrxResponder =
    (req: Request & MerchantHolder): CancelTransactionsResponse => {
    const { secureToken } = req.body;
    const [custId] = getDataFromTemplateTicket(secureToken);

    return {
        accountBalance: {
            playerId: custId,
            accountId: "",
            currencyCode: "EUR", // Currency is used only to format balance response
            balanceArray: [
                {
                    balanceType: "cashable",
                    balanceAmt: settings.amount
                }
            ]
        },
        errorCode: "",
        errorMsg: ""
    };
};

export const getGameConfigurationResponder = (req: Request & MerchantHolder): any => {
    return {
        currency: "EUR",
        data: {
            currencyMultiplier: 50,
            baseCurrency: "EUR",
            configData: {
                bonus: {
                    freeSpinCoinSize: 1
                }
            }
        }
    };
};

export const createBrokenGamesResponder = (req: Request & MerchantHolder): any => {
    const { brokenGameId } = req.body;
    return {
        brokenGameId
    };
};
