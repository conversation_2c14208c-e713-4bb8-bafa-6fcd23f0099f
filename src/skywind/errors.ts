export interface ExtendedErrorInfo extends Error {
    code: string | number;
}

export interface ErrorInfoToLog<T = ExtendedErrorInfo> {
    err: T;
}

export interface BaseErrorToLog {
    toLog?(): ErrorInfoToLog;
}

export class SeamlessAPIError extends Error implements BaseErrorToLog {
    public readonly responseStatus: number;
    public readonly error_code: number;
    public readonly error_msg: string;
    public readonly extraData: string;

    constructor(status: number, message: string, code?: number, extraData?: any) {
        super(message);
        this.responseStatus = status;
        this.error_code = code;
        this.error_msg = message;
        this.extraData = extraData;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.error_code,
                message: this.error_msg,
                stack: this.stack
            }
        };
    }
}

export class ValueIsMissing extends SeamlessAPIError {
    constructor(value: string) {
        super(200, `${value} is missing`, -1);
    }
}

export class EntityNotFound extends SeamlessAPIError {
    constructor(entityType: string, entityId: string) {
        super(200, `cannot find ${entityType} ${entityId}`, -1);
    }
}

export class DuplicateTransaction extends SeamlessAPIError {
    constructor(extraData?) {
        super(200, "Transaction already exists", 1, extraData);
    }
}

export class DuplicateTrxIdTransaction extends SeamlessAPIError {
    constructor(extraData?) {
        super(200, "TrxId already exists", -1, extraData);
    }
}

export class TransactionNotFound extends SeamlessAPIError {
    constructor(extraData: any) {
        super(200, "Bet transaction not found", -7, extraData);
    }
}

export class ValueNotFound extends SeamlessAPIError {
    constructor(value: string) {
        super(200, `${value} not found`, -1);
    }
}

export class MerchantMismatch extends SeamlessAPIError {
    constructor() {
        super(200, "Merchant mismatch", -1);
    }
}

export class PlayerNotFound extends SeamlessAPIError {
    constructor(customerId: string = "") {
        super(200, `Customer ${customerId} not found`, -2);
    }
}

export class GameTokenExpired extends SeamlessAPIError {
    constructor(message) {
        super(200, message || "Customer ticket or session not found", -3);
    }
}

export class PlayerIsSuspended extends SeamlessAPIError {
    constructor() {
        super(200, "Player is suspended", -301);
    }
}

export class BetLimitExceeded extends SeamlessAPIError {
    constructor() {
        super(200, "Bet limit Exceeded", -302);
    }
}

export class InsufficientBalance extends SeamlessAPIError {
    constructor() {
        super(200, "Insufficient balance", -4);
    }
}

export class InsufficientFreebets extends SeamlessAPIError {
    constructor() {
        super(200, "Insufficient freebets", -5);
    }
}

export class InvalidFreebet extends SeamlessAPIError {
    constructor() {
        super(200, "Invalid freebet", -6);
    }
}

export class RoundNotFound extends SeamlessAPIError {
    constructor() {
        super(200, "Round not found", -7);
    }
}

export class MethodWorksOnlyWithJSON extends SeamlessAPIError {
    constructor() {
        super(400, "Method works only with JSON");
    }
}

export class Forbidden extends SeamlessAPIError {
    constructor(message?: string) {
        super(403, message || "Operation forbidden");
    }
}

export class InvalidRequest extends SeamlessAPIError {
    constructor(message?: string) {
        super(200, message || "Invalid request", -1);
    }
}

export class EmailAlreadyExists extends SeamlessAPIError {
    constructor(email: string) {
        super(400, `Email is already used - ${email}`);

    }
}
