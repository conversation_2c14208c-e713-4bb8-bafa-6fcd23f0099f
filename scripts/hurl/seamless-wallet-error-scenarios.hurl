# Seamless Wallet Error <PERSON>s and Edge Cases Test
# This script tests various error conditions and edge cases in the seamless wallet integration

## Setup: Authentication and Basic Setup
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"

POST {{MAPI_URL}}/v1/entities/MERCHANT_ENTITY/terminals/token/generate
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{}
HTTP 200
[Captures]
terminalToken: jsonpath "$['terminalToken']"

## Test 1: Invalid Merchant Configuration
POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "invalid_merchant",
  "merch_pwd": "",
  "isPromoInternal": false,
  "multiple_session": false
}
HTTP 400
[Asserts]
jsonpath "$.error" exists

## Test 2: Duplicate Merchant Creation
POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "test_merchant_dup",
  "merch_pwd": "ValidPassword123!",
  "isPromoInternal": false,
  "multiple_session": false
}
HTTP 200

POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "test_merchant_dup",
  "merch_pwd": "ValidPassword123!",
  "isPromoInternal": false,
  "multiple_session": false
}
HTTP 409
[Asserts]
jsonpath "$.error" contains "already exists"

## Test 3: Invalid Customer Data
POST {{MOCK_URL}}/v1/merchant/test_merchant_dup/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "",
  "cust_login": "INVALID_PLAYER",
  "currency_code": "INVALID_CURRENCY",
  "language": "en",
  "country": "US"
}
HTTP 400
[Asserts]
jsonpath "$.error" exists

## Test 4: Customer with Insufficient Balance
POST {{MOCK_URL}}/v1/merchant/test_merchant_dup/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "poor_player",
  "cust_login": "POORPLAYER",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "balance": {
    "amount": 0,
    "currency_code": "USD"
  }
}
HTTP 200

## Test 5: Blocked Customer
POST {{MOCK_URL}}/v1/merchant/test_merchant_dup/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "blocked_player",
  "cust_login": "BLOCKEDPLAYER",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "blocked",
  "balance": {
    "amount": 10000,
    "currency_code": "USD"
  }
}
HTTP 200

## Test 6: Invalid Ticket Request
GET {{MOCK_URL}}/v1/merchant/test_merchant_dup/customer/nonexistent_player/ticket
accept: text/plain
HTTP 404
[Asserts]
body contains "not found"

## Test 7: Expired Ticket Handling
GET {{MOCK_URL}}/v1/merchant/test_merchant_dup/customer/poor_player/ticket
accept: text/plain
HTTP 200
[Captures]
expiredTicket: body

# Wait for ticket expiration (simulated by using invalid ticket format)
POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "expired_ticket_simulation"
}
HTTP 401
[Asserts]
jsonpath "$.error" exists

## Test 8: Invalid Terminal Token
POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: "invalid_terminal_token"
{
  "ticket": "{{expiredTicket}}"
}
HTTP 401
[Asserts]
jsonpath "$.error" exists

## Test 9: Wallet Service with Invalid Merchant Info
POST {{WALLET_URL}}/v1/balances/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "nonexistent_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "WrongPassword"
    }
  },
  "gameTokenData": {
    "playerId": "test_player",
    "currency": "USD",
    "brandId": 1
  },
  "regulation": "default"
}
HTTP 400
[Asserts]
jsonpath "$.error" exists

## Test 10: Bet Exceeding Balance
# First, create a valid customer and get ticket
POST {{MOCK_URL}}/v1/merchant/test_merchant_dup/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "limited_player",
  "cust_login": "LIMITEDPLAYER",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "balance": {
    "amount": 500,
    "currency_code": "USD"
  }
}
HTTP 200

GET {{MOCK_URL}}/v1/merchant/test_merchant_dup/customer/limited_player/ticket
accept: text/plain
HTTP 200
[Captures]
limitedPlayerTicket: body

POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "{{limitedPlayerTicket}}"
}
HTTP 200
[Captures]
limitedPlayerId: jsonpath "$['playerId']"

# Try to bet more than available balance
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "test_merchant_dup",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "ValidPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{limitedPlayerId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "excessive_bet_001",
    "roundId": "test_round_excessive",
    "gameCode": "sw_mrmnky",
    "bet": 1000.00,
    "win": 0,
    "currency": "USD",
    "operationType": "bet"
  },
  "regulation": "default"
}
HTTP 400
[Asserts]
jsonpath "$.error" contains "insufficient"

## Test 11: Duplicate Transaction ID
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "test_merchant_dup",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "ValidPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{limitedPlayerId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "duplicate_txn_001",
    "roundId": "test_round_dup",
    "gameCode": "sw_mrmnky",
    "bet": 1.00,
    "win": 0,
    "currency": "USD",
    "operationType": "bet"
  },
  "regulation": "default"
}
HTTP 200

POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "test_merchant_dup",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "ValidPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{limitedPlayerId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "duplicate_txn_001",
    "roundId": "test_round_dup",
    "gameCode": "sw_mrmnky",
    "bet": 1.00,
    "win": 0,
    "currency": "USD",
    "operationType": "bet"
  },
  "regulation": "default"
}
HTTP 409
[Asserts]
jsonpath "$.error" contains "duplicate"

## Test 12: Invalid Game Code
POST {{MAPI_URL}}/v1/game/url
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "merchantType": "seamless",
  "merchantCode": "test_merchant_dup",
  "gameCode": "invalid_game_code",
  "playmode": "real",
  "language": "en",
  "ip": "127.0.0.1",
  "lobby": "http://localhost:3000/lobby",
  "cashier": "http://localhost:3000/cashier",
  "ticket": "{{limitedPlayerTicket}}",
  "currency": "USD"
}
HTTP 404
[Asserts]
jsonpath "$.error" contains "game not found"

## Test 13: Currency Mismatch
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "test_merchant_dup",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "ValidPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{limitedPlayerId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "currency_mismatch_001",
    "roundId": "test_round_currency",
    "gameCode": "sw_mrmnky",
    "bet": 1.00,
    "win": 0,
    "currency": "EUR",
    "operationType": "bet"
  },
  "regulation": "default"
}
HTTP 400
[Asserts]
jsonpath "$.error" contains "currency"

## Test 14: Session Timeout Simulation
POST {{WALLET_URL}}/v1/games/keep-alive
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "test_merchant_dup",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "ValidPassword123!"
    }
  },
  "gameToken": {
    "playerId": "invalid_session_player",
    "currency": "USD",
    "brandId": 1,
    "sessionId": "expired_session_id"
  }
}
HTTP 401
[Asserts]
jsonpath "$.error" exists
