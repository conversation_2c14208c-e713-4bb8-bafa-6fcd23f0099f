import { Customer, Merchant } from "../models/merchant";
import { Ticket } from "../models/ticket";

export interface GVCAuthenticateUserRequest {
    partnerId: string; // provider ID at GVC
    accountId: string; // represented as <merch_id>_<cust_id> (GVC type is number)
    gameContext: string;
    currencyCode: string;
    secureToken: string; // token for authentication
    gameCode: string;
    gameSessionId?: string;
    clientChannel: string;
}

export interface GVCPlayerBalanceRequest extends GVCAuthenticateUserRequest {
}

export interface GVCFundsTransferRequest extends GVCAuthenticateUserRequest {
    gameRoundState: GameRoundState;
    gameRoundId: string;
    transactionId: string;
    transactionDetails: GVCTransactionDetail[];
}

export interface GVCVoidTransactionRequest {
    partnerId: string;
    accountId: string;
    transactionId: string; // rollback transaction id
    cancelledTransactionId: string; // bet transaction id
    gameSessionId: string;
    reason?: string;
    clientChannel: string;
}

export interface GVCTransactionDetail {
    transactionType?: GVC_TRANSACTION_TYPE;
    amount: number;
    transactionSequence?: number;
    transactionTime?: Date; // GMT
}

export interface GVCPlayerCommonResponse {
    partnerId: string;
    accountId: string;
    currencyCode: string;
    balance: number;
}

export interface GVCResponseStatusData {
    status?: {
        statusCode: string;
        statusMessage: string;
    };
    extendedParams?: [{ key: string, value: string }];
}

export interface GVCVerifyTokenRequest {
    partnerId: string;
    accountId: string;
    secureToken: string;
    gameContext: string;
    gameCode: string;
    currencyCode: string;
    channelId: string;
}

export interface GVCVerifyTokenResponse extends GVCResponseStatusData {
    partnerId: string;
    currencyCode: string;
    balance: number;
    renewedSecureToken: string;
    bioCountryCode?: string;
    scalableParams?: { key: string, value: string }[];
}

export interface GVCPlayerBalanceResponse extends GVCPlayerCommonResponse, GVCResponseStatusData {
    gameCode: string;
}

export interface GVCFundsTransferResponse extends GVCPlayerCommonResponse, GVCResponseStatusData {
    gameCode: string;
    gameContext: string;
    transactionId: string;
    bpGameRoundId?: string;
    bpTransactionId?: string; // (real type is number)
}

export interface GVCVoidTransactionResponse extends GVCPlayerCommonResponse, GVCResponseStatusData {
    transactionId: string;
    bpGameRoundId?: string;
    bpTransactionId?: string; // (real type is number)
}

export interface GVCExtraRequestData {
    merchant?: Merchant;
    customer?: Customer;
    tokenData?: Ticket;
}

export enum GVC_TRANSACTION_TYPE {
    WIN = "win",
    BET = "bet",
}

export enum GameRoundState {
    INPROGRESS = "inprogress",
    FINISHED = "finished",
}

export const FAKE_GAME_ROUND_ID = "GRID";
export const SKYWIND_PARTNER_ID = "SKYWIND";
