import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { EvereMatrixSimpleEngineService } from "../../skywind/integrations/everyMatrix/servicePredefined";
import { EVERY_MATRIX_STATUS } from "../../skywind/integrations/everyMatrix/errors";
import { BetRequest, EMAuthenticateResponse, WinRequest } from "../../skywind/integrations/everyMatrix/entities";
import { buildMethodHash } from "../../skywind/integrations/everyMatrix/middleware";
import config from "../../skywind/config";

@suite
class EveryMatrixSpec {
    private everyMatrixService: EvereMatrixSimpleEngineService;

    public async before() {
        this.everyMatrixService = new EvereMatrixSimpleEngineService();
    }

    @test
    public baseFunctionalityTest() {
        const authRequest = {
            "LaunchToken": "SimpleEngine:LaunchToken:Olga:EUR:UK",
            "RequestScope": "country, age"
        };

        const authResult = this.everyMatrixService.authenticatePlayer(authRequest) as EMAuthenticateResponse;

        expect(authResult.Status).eq(EVERY_MATRIX_STATUS.OK);

        const startBalance = this.everyMatrixService.getBalance({
            "Token": authResult.Token,
            "Hash": buildMethodHash("GetBalance"),
            "Currency": null});

        const externalId = "ExternalId1";
        const roundId = "1";
        const gameId = "sw_ss";
        const currency = "EUR";

        const betRequest: BetRequest = {
            "Token": authResult.Token,
            "Hash": buildMethodHash("Bet"),
            "Amount": 5,
            "Currency": currency,
            "ExternalId": externalId,
            "GameId": gameId,
            "RoundId": roundId,
            "JackpotContribution": null
        };

        this.everyMatrixService.bet(authResult.UserId, betRequest);

        const winRequest: WinRequest = {
            "Token": authResult.Token,
            "Hash": buildMethodHash("Win"),
            "Amount": 1,
            "Currency": currency,
            "ExternalId": externalId,
            "BetExternalId": externalId,
            "GameId": gameId,
            "RoundId": roundId,
            JackpotPayout: null,
            BonusId: null,
            RoundEnd: true };

        const winResult = this.everyMatrixService.win(authResult.UserId, winRequest);

        expect(winResult.TotalBalance).eq((+startBalance.TotalBalance - 4).toString());

        this.everyMatrixService.clearPlayerStatistic({
            "Token": authResult.Token
            });

        const endBalance = this.everyMatrixService.getBalance({
            "Token": authResult.Token,
            "Hash": buildMethodHash("GetBalance"),
            "Currency": null});

        expect(endBalance.TotalBalance).eq(config.defaultSettings.amount.toString());
    }

    @test
    public cancelTest() {
        const authRequest = {
            "LaunchToken": "SimpleEngine:LaunchToken:Ivan:EUR:MT",
            "RequestScope": "country, age"
        };

        const authResult = this.everyMatrixService.authenticatePlayer(authRequest) as EMAuthenticateResponse;

        expect(authResult.Status).eq(EVERY_MATRIX_STATUS.OK);

        const startBalance = this.everyMatrixService.getBalance({
            "Token": authResult.Token,
            "Hash": buildMethodHash("GetBalance"),
            "Currency": null});

        const externalId = "ExternalId1";
        const roundId = "1";
        const gameId = "sw_ss";
        const currency = "EUR";

        const betRequest: BetRequest = {
            "Token": authResult.Token,
            "Hash": buildMethodHash("Bet"),
            "Amount": 5,
            "Currency": currency,
            "ExternalId": externalId,
            "GameId": gameId,
            "RoundId": roundId,
            "JackpotContribution": null
        };

        this.everyMatrixService.bet(authResult.UserId, betRequest);

        const winRequest: WinRequest = {
            "Token": authResult.Token,
            "Hash": buildMethodHash("Win"),
            "Amount": 1,
            "Currency": currency,
            "ExternalId": externalId,
            "BetExternalId": externalId,
            "GameId": gameId,
            "RoundId": roundId,
            JackpotPayout: null,
            BonusId: null,
            RoundEnd: true };

        const winResult = this.everyMatrixService.win(authResult.UserId, winRequest);

        expect(winResult.TotalBalance).eq((+startBalance.TotalBalance - 4).toString());

        this.everyMatrixService.cancel(authResult.UserId, {
                Token: authResult.Token,
                ExternalId: "CancelId",
                CanceledExternalId: externalId,
                Hash: buildMethodHash("Cancel")
            });

        const endBalance = this.everyMatrixService.getBalance({
            "Token": authResult.Token,
            "Hash": buildMethodHash("GetBalance"),
            "Currency": null});

        expect(endBalance.TotalBalance).eq(config.defaultSettings.amount.toString());
    }
}
