import { expect, use } from "chai";
import { getApplication } from "../../skywind/server";
import logger from "../../skywind/utils/logger";
import * as MerchantModel from "../../skywind/models/merchant";
import * as CustomErrorModel from "../../skywind/models/customError";
import {
    addFreebets,
    addMoneyForCustomer, clearFreebets,
    createCustomer,
    createError,
    createMerchant,
    createTicketForCustomer
} from "./helper";
import { clearAll, settings } from "../../skywind/models/settings";
import * as TransactionModel from "../../skywind/models/transaction";
import * as SessionModel from "../../skywind/models/session";
import * as RoundModel from "../../skywind/models/round";
import { SpecialTicket } from "../../skywind/service/ticket";
import config from "../../skywind/config";
import { playerInfoModel } from "../../skywind/models/playerInfo";
import { stub } from "sinon";
import { defaultCustomerCurrency, defaultCustomerId } from "../../skywind/service/customer";
import { Application } from "express";

const properties = require("properties");
const uuid = require("uuid");
use(require("chai-shallow-deep-equal"));
const log = logger("ipm-mock:tests");

const request = require("supertest");

describe("IPM-mock test API", async () => {
    let server: Application;

    let merchantId: string;
    let merchantPassword: string;
    let customerId: string;
    let balance: number;

    const customError = {
        "error_code": -1501,
        "http_response_status": 200,
        "error_msg": "My custom error",
        "custom_field": "some custom data",
    };

    const extraData = {
        "custom_extra_field": "some extra data",
        "extra_data_object": {
            "extra1": "data1",
        },
    };

    const createExtraData = async (customAction) => {
        log.debug("Create extra data : " + customAction + " for customer " + customerId);

        await request(server)
            .post(`/v1/merchant/${merchantId}/customer/${customerId}/extra_data/${customAction}`)
            .send(extraData)
            .expect(201)
            .expect({
                "message": "Extra data created",
                "extra_data": extraData,
            });
    };

    before(async () => {
        server = await getApplication();
    });

    beforeEach(async () => {
        clearAll();
        MerchantModel.setAll({});

        SessionModel.clearAll();
        TransactionModel.setAll({});

        CustomErrorModel.setAll({});

        merchantId = "merchant_" + uuid.v4().substring(0, 8);
        merchantPassword = "pass" + uuid.v4().substring(0, 8);
        customerId = "customer_" + uuid.v4().substring(0, 8);
        balance = Math.floor(Math.random() * 1000) + 10;
    });

    it("Create merchant", async () => {
        await createMerchant(server, merchantId, merchantPassword);

        log.debug("Get merchant: " + merchantId);
        await request(server)
            .get("/v1/merchant")
            .expect(200)
            .then((res) => {
                expect(res.body).deep.equal({
                    [merchantId]:
                        {
                            merch_id: merchantId,
                            merch_pwd: merchantPassword,
                            customers: {},
                            isPromoInternal: false,
                            multiple_session: true,
                        }
                });
            });
    });

    it("Create customer", async () => {
        await createMerchant(server, merchantId, merchantPassword);
        await createCustomer(server, merchantId, customerId);

        log.debug("Get customer: " + customerId);
        await request(server)
            .get(`/v1/merchant/${merchantId}/customer/${customerId}`)
            .expect(200)
            .then(response => {
                expect(response.body).to.have.property("cust_id", customerId);
            });
    });

    describe("Add money to balance", () => {
        const balanceFixed = 100;
        const addBalance = 0.057;
        let tests = [];
        const args = [
            [(Math.random() * 100), (Math.random() * 10)],
        ];
        tests = [
            {
                args: { around: false, balance: balanceFixed, addBalance },
                expected: (balanceFixed + addBalance)
            },
            {
                args: { around: true, balance: balanceFixed, addBalance },
                expected: +(balanceFixed + addBalance).toFixed(2)
            },
            {
                args: { around: false, balance: 100, addBalance: 5 },
                expected: 105
            },
            {
                args: { around: true, balance: 100, addBalance: 5 },
                expected: 105
            },
            {
                args: { around: false, balance: args[0][0], addBalance: args[0][1] },
                expected: args[0][0] + args[0][1]

            },
        ];

        beforeEach(async () => {
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);
        });

        tests.forEach(function(test) {
            it(
                `Around:${test.args.around} with new balance:
                ${test.args.balance}+${test.args.addBalance}=${test.expected}`,
                async () => {
                    settings.aroundAmount = test.args.around;
                    await addMoneyForCustomer(server, merchantId, customerId, balance, test.args.balance);

                    log.debug(`Get customer: ${customerId} with balance: ${balance}`);
                    await request(server)
                        .get(`/v1/merchant/${merchantId}/customer/${customerId}`)
                        .expect(200)
                        .then(response => {
                            expect(response.body).to.have.property("cust_id", customerId);
                            expect(response.body).to.have.property("balance");
                            expect(response.body.balance).to.have.property("amount", test.args.balance);
                            expect(response.body.balance).to.have.property("currency_code", "USD");
                        });
                    log.debug(`Get customer: ${customerId} with new balance:
                    ${test.args.balance} + ${test.args.addBalance} = ${test.expected}`);
                    await addMoneyForCustomer(server, merchantId, customerId, balance, test.args.addBalance);
                    await request(server)
                        .get(`/v1/merchant/${merchantId}/customer/${customerId}`)
                        .expect(200)
                        .then(response => {
                            expect(response.body).to.have.property("cust_id", customerId);
                            expect(response.body).to.have.property("balance");
                            expect(response.body.balance).to.have.property("amount", test.expected);
                            expect(response.body.balance).to.have.property("currency_code", "USD");
                        });
                });
        });
    });

    describe("Plain text", () => {
        let playerInfoCreateMock;
        let playerInfoFindOne;

        const trxId = uuid.v4();
        const roundId = Math.floor(Math.random() * 9999) + 10;
        let customerSessionId;

        before(() => {
            playerInfoCreateMock = stub(playerInfoModel.get(), "create");
            playerInfoFindOne = stub(playerInfoModel.get(), "findOne");
        });

        after(() => {
            playerInfoCreateMock.restore();
            playerInfoFindOne.restore();
        });

        beforeEach(async () => {
            config.specialFeatures.phantomCompanyTournamentTicket = false;

            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);
            await addMoneyForCustomer(server, merchantId, customerId, balance);
            const ticket = await createTicketForCustomer(server, merchantId, customerId);

            log.debug("Validate ticket: " + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")
                .then(response => {
                    expect(response.text).to.include("cust_id=" + customerId);
                    expect(response.text).to.include("cust_session_id=");
                    expect(response.text).to.include(
                        "error_code=0\n" +
                        `cust_id=${customerId}\n` +
                        `cust_login=login4${customerId}\n` +
                        "currency_code=USD\n" +
                        "language=en\n" +
                        "country=CN\n" +
                        "test_cust=true\n" +
                        "first_name=\n" +
                        "last_name=\n" +
                        "date_of_birth=");
                    const data = properties.parse(response.text);
                    expect(data).to.have.property("cust_session_id");
                    customerSessionId = data.cust_session_id;
                });

        });

        it("Validate phantom ticket turn on", async () => {
            config.specialFeatures.phantomCompanyTournamentTicket = true;

            playerInfoFindOne.returns(
                Promise.resolve(undefined));
            playerInfoCreateMock.returns(
                Promise.resolve({ toJSON: () => ({ id: 1, playerCode: "chebureck" }) }));

            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .send({
                    ticket: `${SpecialTicket.PHANTOM}<EMAIL>`,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")
                .then(response => {
                    expect(response.text).to.include("cust_session_id=");
                    expect(response.text).to.include("currency_code=CNY");
                    const data = properties.parse(response.text);
                    expect(data).to.have.property("cust_session_id");
                    customerSessionId = data.cust_session_id;
                });
        });

        it("Validate phantom ticket turn off", async () => {
            config.specialFeatures.phantomCompanyTournamentTicket = false;
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .send({
                    ticket: SpecialTicket.PHANTOM,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")
                .then(response => {
                    expect(response.text).to.include("error_code=-3");
                    expect(response.text).to.include("error_msg=Ticket not found");
                });
        });

        it("Get balance with cust_session_id", async () => {
            log.debug("Get balance with cust_session_id: " + customerSessionId);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")
                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=0\n" +
                        `balance=${balance}\n` +
                        "currency_code=USD");
                });
        });

        it("Get balance with bad cust_session_id - negative", async () => {
            await request(server)
                .post("/api/get_balance")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: uuid.v4(),
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=-3\nerror_msg=cust_session_id is expired");
                });
        });

        it("Get balance without cust_session_id", async () => {

            log.debug("Get balance without cust_session_id: " + customerSessionId);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .send({
                    cust_id: customerId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ncurrency_code=USD`);
                });
        });

        it("Get player with cust_session_id", async () => {

            log.debug("Get player with cust_session_id");

            await request(server)
                .post("/api/get_player")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.include("cust_id=" + customerId);
                    expect(response.text).to.include("cust_login=login4" + customerId);
                });
        });

        it("Get player with bad cust_session_id - negative", async () => {

            log.debug("Get player with cust_session_id");

            await request(server)
                .post("/api/get_player")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: "bad-token",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                // .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=-3\nerror_msg=cust_session_id is expired");
                });
        });

        it("Get player without cust_session_id", async () => {

            log.debug("Get player without cust_session_id");

            await request(server)
                .post("/api/get_player")
                .type("form")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.include("cust_id=" + customerId);
                    expect(response.text).to.include("cust_login=login4" + customerId);
                });
        });

        it("Debit Customer", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });

        });

        it("Debit suspended Customer - negative", async () => {
            log.debug("Update customer: " + customerId);

            const data = {
                "cust_id": customerId,
                "cust_login": "login4" + customerId,
                "currency_code": "USD",
                "language": "ru",
                "country": "US",
                "test_cust": true,
                "first_name": "",
                "last_name": "",
                "date_of_birth": new Date().toJSON(),
                "status": "suspended",
                "bet_limit": null,
            };
            await request(server)
                .patch(`/v1/merchant/${merchantId}/customer/${customerId}`)
                .send(data)
                .expect(200)
                .then(response => {
                    expect(response.body).to.have.property("cust_id");
                });

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        "error_code=-301\nerror_msg=Player is suspended");
                });

        });

        it("Debit Customer with amount more then Bet limit - negative", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 15000;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal("error_code=-302\nerror_msg=Bet limit Exceeded");
                });

        });

        it("Duplicate debit Customer - negative", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        `error_code=1\nerror_msg=Transaction already exists\nbalance=${balance}\ntrx_id=${trxId}`);
                });

        });

        it("Credit Customer", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });

            const win = Math.floor(Math.random() * 10);
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${(balance + win)}\ntrx_id=${trxId}`);
                });

            const roundInfo = RoundModel.findOne(merchantId, customerId, roundId.toString());

            expect(roundInfo.totalBet).eq(bet);
            expect(roundInfo.totalWin).eq(win);
        });

        it("Credit Customer without Token ", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });

            const win = Math.floor(Math.random() * 10);
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${(balance + win)}\ntrx_id=${trxId}`);

                });

            const roundInfo = RoundModel.findOne(merchantId, customerId, roundId.toString());

            expect(roundInfo.totalBet).eq(bet);
            expect(roundInfo.totalWin).eq(win);
        });

        it("Credit Customer with wrong amount ", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });

            const win = 10000.475;
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=-1\nerror_msg=Amount has invalid number of decimals");
                });
        });

        it("Credit Customer with problematic amount", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });

            const win = 10000.47;
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${(balance + win)}\ntrx_id=${trxId}`);
                });

            const roundInfo = RoundModel.findOne(merchantId, customerId, roundId.toString());

            expect(roundInfo.totalBet).eq(bet);
            expect(roundInfo.totalWin).eq(win);
        });

        it("Rollback without token", async () => {
            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });

            log.debug(`Send Rollback without token for trxId: ${trxId}`);
            await request(server)
                .post("/api/rollback")
                .type("form")
                .send({
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "rollback",
                    timestamp: Date.now(),
                    event_id: 0
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${(balance + bet)}\ntrx_id=${trxId}`);
                });

            const roundInfo = RoundModel.findOne(merchantId, customerId, roundId.toString());

            expect(roundInfo.totalBet).eq(0);
        });

        it("Credit customer with expired session", async () => {
            const expirationTimeBackup = config.expirationTime.customerSession;

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });


            config.expirationTime.customerSession = 1;

            const win = Math.floor(Math.random() * 10);
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=-3\nerror_msg=cust_session_id is expired");
                });

            const roundInfo = RoundModel.findOne(merchantId, customerId, roundId.toString());

            expect(roundInfo.totalBet).eq(bet);
            expect(roundInfo.totalWin).eq(0);
            config.expirationTime.customerSession = expirationTimeBackup;
        });

        it("Credit customer with expired session and finalization: true", async () => {
            const expirationTimeBackup = config.expirationTime.customerSession;

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${balance}\ntrx_id=${trxId}`);
                });


            config.expirationTime.customerSession = 1;

            const win = Math.floor(Math.random() * 10);
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId,
                    is_finalization_payment: true,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        `error_code=0\nbalance=${(balance + win)}\ntrx_id=${trxId}`);
                });

            const roundInfo = RoundModel.findOne(merchantId, customerId, roundId.toString());

            expect(roundInfo.totalBet).eq(bet);
            expect(roundInfo.totalWin).eq(win);
            config.expirationTime.customerSession = expirationTimeBackup;
        });
    });

    it("Update customer", async () => {
        await createMerchant(server, merchantId, merchantPassword);
        await createCustomer(server, merchantId, customerId);

        log.debug("Update customer: " + customerId);

        const data = {
            "cust_id": customerId,
            "cust_login": "login4" + customerId,
            "currency_code": "USD",
            "language": "ru",
            "country": "US",
            "test_cust": true,
            "first_name": "",
            "last_name": "",
            "date_of_birth": new Date().toJSON(),
            "status": "suspended",
            "bet_limit": null,
        };
        await request(server)
            .patch(`/v1/merchant/${merchantId}/customer/${customerId}`)
            .send(data)
            .expect(200)
            .then(response => {
                data["balance"] = {
                    "amount": 0,
                    "currency_code": "USD",
                };
                data["bonusBalance"] = 0;
                expect(response.body).to.deep.equal(data);
            });

        log.debug("Update customer 2: " + customerId);

        await addMoneyForCustomer(server, merchantId, customerId, balance, 200);

        const data2 = {
            "cust_id": customerId,
            "cust_login": "login4 new" + customerId,
            "currency_code": "CNY",
            "language": "by",
            "country": "US",
            "test_cust": false,
            "first_name": "",
            "last_name": "",
            "date_of_birth": new Date().toJSON(),
            "status": "normal",
            "bet_limit": 5000,
        };
        await request(server)
            .patch(`/v1/merchant/${merchantId}/customer/${customerId}`)
            .send(data2)
            .expect(200)
            .then(response => {
                data2["balance"] = {
                    "amount": 200,
                    "currency_code": "CNY",
                };
                data2["bonusBalance"] = 0;
                expect(response.body).to.deep.equal(data2);
            });

    });

    describe("Get page operations", () => {
        beforeEach(async () => {
            server = await getApplication();
        });

        it("Get page fails on missing params", async () => {
            await createMerchant(server, merchantId, merchantPassword);

            await request(server)
                .post("/api/get_page")
                .type("form")
                .send({
                    token: undefined,
                    lobby_id: "lobby_id",
                    page_type: "playerinfo",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=-1\nerror_msg=token is missing");
                });

            await request(server)
                .post("/api/get_page")
                .type("form")
                .send({
                    token: "token",
                    lobby_id: undefined,
                    page_type: "login",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=-1\nerror_msg=lobby_id is missing");
                });

            await request(server)
                .post("/api/get_page")
                .type("form")
                .send({
                    token: "token",
                    lobby_id: "lobby_id",
                    page_type: undefined,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "text/html; charset=utf-8")
                .expect(200)
                .expect("Content-Type", "text/html; charset=utf-8")

                .then(response => {
                    expect(response.text).to.be.equal(
                        "error_code=-1\nerror_msg=page_type is missing");
                });
        });

        it("Get page", async () => {
            await createMerchant(server, merchantId, merchantPassword);

            await request(server)
                .post("/api/get_page")
                .type("form")
                .send({
                    token: "token",
                    lobby_id: "lobby_id",
                    page_type: "registration",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body.error_code).to.eq(0);
                    expect(response.body.size).to.eq(1024);
                    expect(response.body.url).to.include("/api/get_test_lobby_page?type=registration");
                });

            await request(server)
                .get("/api/get_test_lobby_page?type=password")
                .expect(200)
                .expect("Content-Type", "text/html; charset=UTF-8")

                .then(response => {
                    expect(response).to.exist;
                });
        });
    });

    describe("JSON", () => {

        let trxId = uuid.v4();
        const trxId2 = uuid.v4();
        let ticket: string;
        const roundId = Math.floor(Math.random() * 9999) + 10;
        let customerSessionId;

        beforeEach(async () => {
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);
            await addMoneyForCustomer(server, merchantId, customerId, balance);
            ticket = await await createTicketForCustomer(server, merchantId, customerId);

            log.debug("Validate ticket: " + ticket);

            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_login", `login4${customerId}`);
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId = response.body.cust_session_id;
                });

        });

        it("Get custom Error for validate_ticket", async () => {
            ticket = await createTicketForCustomer(server, merchantId, customerId);
            await createError(server, merchantId, customerId, "validate_ticket", customError);

            log.debug("Get error for: validate_ticket" + ticket);

            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect(customError);

            log.debug("Get normal response fro second validate_ticket" + ticket);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            currency_code: "USD"
                        });
                });
        });

        it("Get balance", async () => {

            log.debug("Get balance with cust_session_id: " + customerSessionId);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            currency_code: "USD"
                        });
                });
        });

        it("Get custom Error for Get balance", async () => {
            await createError(server, merchantId, customerId, "get_balance", customError);

            log.debug("Get error for: Get balance with cust_session_id: " + customerSessionId);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect(customError);

            log.debug("Get normal response for: Get balance with cust_session_id: " + customerSessionId);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            currency_code: "USD"
                        });
                });
        });

        it("Get balance with bad cust_session_id - negative", async () => {

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .send({
                    cust_id: customerId + 1,
                    cust_session_id: uuid.v4(),
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect({
                    error_code: -3,
                    error_msg: "cust_session_id is expired",
                });
        });

        it("Get player", async () => {

            log.debug("Get player without cust_session_id");

            await request(server)
                .post("/api/get_player")
                .type("form")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_login", `login4${customerId}`);
                    expect(response.body).to.have.property("currency_code", "USD");
                    expect(response.body).to.have.property("language", "en");
                    expect(response.body).to.have.property("country", "CN");
                    expect(response.body).to.have.property("test_cust", true);
                });
        });

        it("Debit Customer", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId,
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                        });
                });

        });

        it("Debit Customer with free bets", async () => {
            const bet = Math.floor(Math.random() * (balance - 10)) + 10;
            await addFreebets(server, merchantId, customerId);

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "free-bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    free_bet_coin: 0.1,
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                            "free_bet_count": 99
                        });
                });
            await clearFreebets(server, merchantId, customerId);
        });

        it("Free bets zero debit for normal games", async () => {
            const bet = Math.floor(Math.random() * (balance - 10)) + 10;
            await addFreebets(server, merchantId, customerId);

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: 0,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "free-bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "bonusgame",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                            "free_bet_count": 100
                        });
                });
            await clearFreebets(server, merchantId, customerId);
        });

        it("Free bets zero debit for legacy games", async () => {
            await addFreebets(server, merchantId, customerId);

            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: 0,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "free-bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                            "free_bet_count": 100
                        });
                });
            await clearFreebets(server, merchantId, customerId);
        });

        it("Duplicate debit Customer - negative", async () => {
            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send first BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                        });
                });

            log.debug(`Send the same BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .expect({
                    error_code: 1,
                    error_msg: "Transaction already exists",
                    balance: balance,
                    trx_id: trxId,
                });

        });

        it("Check decreaseLoad:true, duplicate debit- check for duplicate disabled", async () => {
            settings.decreaseLoad = true;

            let bet = Math.floor(Math.random() * (balance / 2 - 10)) + 4;

            log.debug(`Send first BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                        });
                });

            bet = Math.floor(Math.random() * (balance - 10)) + 1;
            balance = balance - bet;
            log.debug(`Send the same BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .expect({
                    error_code: 0,
                    balance: balance,
                    trx_id: trxId,
                });

        });

        it("Credit Customer", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                        });
                });

            const win = Math.floor(Math.random() * 10);
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance + win,
                            trx_id: trxId,
                        });
                });
        });

        it("Custom Error: Return error for Debit - negative", async () => {
            await createError(server, merchantId, customerId, "debit", customError);
            const bet = Math.floor(Math.random() * (balance / 2));

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet} - and Get ERROR`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect(customError);

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet} - and Get answer without error`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId + 2,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    balance = balance - bet;
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId + 2,
                        });
                });
        });

        it("Extra Data: Return Extra Data for Debit", async () => {
            await createExtraData("debit");
            let bet = Math.floor(Math.random() * (balance - 10)) + 2;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet} - and Get Extra Data`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                            custom_extra_field: "some extra data",
                            extra_data_object: {
                                "extra1": "data1",
                            },
                        });
                });

            bet = Math.floor(Math.random() * (balance - 10)) + 1;
            trxId = uuid.v4();
            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet} - and Get answer without Extra data`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,

                        });
                });
        });

    });

    describe("Test methods", () => {

        let trxId = uuid.v4();
        let ticket: string;
        let currencyCode: string;
        const roundId = Math.floor(Math.random() * 9999) + 10;

        beforeEach(async () => {
            currencyCode = defaultCustomerCurrency;
            await createMerchant(server, merchantId, merchantPassword);
        });

        const runChecks = async (customerSessionId) => {

            log.debug("Get balance with cust_session_id: " + customerSessionId);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.have.property("balance");
                    expect(response.body).to.have.property("currency_code", currencyCode);
                    expect(response.body).to.have.property("error_code", 0);

                    balance = response.body.balance;
                });

            log.debug("Get player with cust_session_id");

            await request(server)
                .post("/api/get_player")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("currency_code", currencyCode);
                    expect(response.body).to.have.property("language", "en");
                    expect(response.body).to.have.property("country", "CN");
                    expect(response.body).to.have.property("test_cust", true);
                });

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: currencyCode,
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId,
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    balance = balance - bet;
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance,
                            trx_id: trxId,
                        });
                });

            const win = Math.floor(Math.random() * 10);
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: currencyCode,
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: balance + win,
                            trx_id: trxId,
                        });
                });
        };

        it("Get ticket for default customer", async () => {
            let customerSessionId: string;

            log.debug("Get ticket for test customer:" + defaultCustomerId);

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                    ticket = response.body.ticket;
                });

            log.debug("Validate  ticket:" + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", defaultCustomerId);
                    customerId = defaultCustomerId;
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId = response.body.cust_session_id;
                });

            await runChecks(customerSessionId);
        });

        it("Get ticket for custom customer", async () => {
            let customerSessionId: string;
            log.debug("Get ticket for test customer:" + customerId);

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                    ticket = response.body.ticket;
                });

            log.debug("Validate  ticket:" + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId = response.body.cust_session_id;
                });

            await runChecks(customerSessionId);
        });

        it("Get ticket for custom currency", async () => {
            let customerSessionId: string;
            const currency = ["BYR", "BYN", "CNY", "NOK"];
            currencyCode = currency[Math.floor(Math.random() * currency.length)];

            log.debug("Get ticket for currency");

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    currency_code: currencyCode,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                    ticket = response.body.ticket;
                });

            log.debug("Validate  ticket:" + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId = response.body.cust_session_id;
                });

            await runChecks(customerSessionId);
        });

        it("Get ticket for multiple session", async () => {
            let customerSessionId1: string;
            let customerSessionId2: string;
            log.debug("Get ticket 1 single_session=false for test customer :" + customerId);

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    //      single_session: false,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                    ticket = response.body.ticket;
                });

            log.debug("Validate  ticket1:" + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId1 = response.body.cust_session_id;
                });

            log.debug("First check customerSessionId1 :" + customerSessionId1);
            await runChecks(customerSessionId1);

            log.debug("Get ticket 2 single_session=false for test customer :" + customerId);

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    single_session: false,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                    ticket = response.body.ticket;
                });

            log.debug("Validate  ticket 2:" + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId2 = response.body.cust_session_id;
                });

            trxId = uuid.v4();
            log.debug("Second check customerSessionId1 :" + customerSessionId1);
            await runChecks(customerSessionId1);

            trxId = uuid.v4();
            log.debug("First check customerSessionId2 :" + customerSessionId2);
            await runChecks(customerSessionId2);
        });

        it("Get ticket for single session", async () => {
            let customerSessionId1: string;
            let customerSessionId2: string;
            log.debug("Get ticket 1 single_session=false for test customer :" + customerId);

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    //      single_session: false,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                    ticket = response.body.ticket;
                });

            log.debug("Validate  ticket1:" + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId1 = response.body.cust_session_id;
                });

            log.debug("First check customerSessionId1 :" + customerSessionId1);
            await runChecks(customerSessionId1);

            log.debug("Get ticket 2 single_session=true for test customer :" + customerId);

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    single_session: true,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                    ticket = response.body.ticket;
                });

            log.debug("Validate  ticket 2:" + ticket);
            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", customerId);
                    expect(response.body).to.have.property("cust_session_id");
                    customerSessionId2 = response.body.cust_session_id;
                });

            log.debug("Second check customerSessionId1 :" + customerSessionId1);

            log.debug("Get balance with NOT valid cust_session_id: " + customerSessionId1);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    cust_session_id: customerSessionId1,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .expect({
                    error_code: -3,
                    error_msg: "cust_session_id is expired",
                });

            trxId = uuid.v4();
            log.debug("First check customerSessionId2 :" + customerSessionId2);
            await runChecks(customerSessionId2);
        });

        it("Get test customers", async () => {

            log.debug("Get ticket for test customers");

            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                });

            currencyCode = "ILS";
            await request(server)
                .post("/api/get_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: customerId,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    currency_code: currencyCode,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("ticket");
                });

            log.debug("Get list of test customers");
            await request(server)
                .post("/api/get_test_customers")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .expect([
                    {
                        "balance": {
                            "amount": settings.amount,
                            "currency_code": defaultCustomerCurrency,
                        },
                        "bet_limit": null,
                        "country": "CN",
                        "currency_code": defaultCustomerCurrency,
                        "cust_id": defaultCustomerId,
                        "cust_login": "",
                        "language": "en",
                        "status": "normal",
                        "test_cust": true,
                    },
                    {
                        "balance": {
                            "amount": settings.amount,
                            "currency_code": currencyCode,
                        },
                        "bet_limit": null,
                        "country": "CN",
                        "currency_code": currencyCode,
                        "cust_id": customerId,
                        "cust_login": "",
                        "language": "en",
                        "status": "normal",
                        "test_cust": true,
                    },
                ]);
        });
    });

    describe("Custom Error", () => {

        const action = "credit";

        beforeEach(async () => {
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);

            await createError(server, merchantId, customerId, "credit", customError);
        });

        it("Create and Get error", async () => {

            log.debug("Get error for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/customer/${customerId}/error/${action}`)
                .expect(200)
                .expect({ "AFTER": [customError] });
        });

        it("Get list of errors for customer", async () => {

            await createError(server, merchantId, customerId, "debit", customError);
            log.debug("Get list of errors for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/customer/${customerId}/error/`)
                .expect(200)
                .expect({
                    "credit": { "AFTER": [customError] },
                    "debit": { "AFTER": [customError] },
                });
        });

        it("Get list of all errors", async () => {

            await createError(server, merchantId, customerId, "debit", customError);
            await createError(server, merchantId, customerId, "get_balance", customError);
            log.debug("Get list of errors for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/error/`)
                .expect(200)
                .expect({
                    [customerId]: {
                        "credit": { "AFTER": [customError] },
                        "get_balance": { "AFTER": [customError] },
                        "debit": { "AFTER": [customError] },
                    }
                });
        });

        it("Delete error", async () => {

            log.debug("Delete error for customer" + customerId);

            await request(server)
                .delete(`/v1/merchant/${merchantId}/customer/${customerId}/error/${action}`)
                .expect(204);

            log.debug("Get error for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/customer/${customerId}/error/${action}`)
                .expect(200)
                .expect({});
        });

        it("Delete error by action and raiseType", async () => {

            log.debug("Delete error for customer" + customerId);

            await request(server)
                .delete(`/v1/merchant/${merchantId}/customer/${customerId}/error/${action}/raiseType/before`)
                .expect(400);

            await request(server)
                .delete(`/v1/merchant/${merchantId}/customer/${customerId}/error/${action}/raiseType/after`)
                .expect(204);

            log.debug("Get error for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/customer/${customerId}/error/${action}`)
                .expect(200)
                .expect({});
        });

    });

    describe("Extra Data", () => {

        const action = "credit";

        beforeEach(async () => {
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);

            await createExtraData("credit");
        });

        it("Create and Get extra data", async () => {

            log.debug("Get Extra Data for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/customer/${customerId}/extra_data/${action}`)
                .expect(200)
                .expect(extraData);
        });

        it("Get list of Extra Data for customer", async () => {

            await createExtraData("debit");
            log.debug("Get list of Extra Data for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/customer/${customerId}/extra_data/`)
                .expect(200)
                .expect({
                    "credit": extraData,
                    "debit": extraData,
                });
        });

        it("Get list of all Extra Data", async () => {

            await createExtraData("debit");
            await createExtraData("get_balance");
            log.debug("Get list of Extra Data for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/extra_data/`)
                .expect(200)
                .expect({
                    [customerId]: {
                        "credit": extraData,
                        "get_balance": extraData,
                        "debit": extraData,
                    }
                });
        });

        it("Delete Extra Data", async () => {

            log.debug("Delete Extra Data for customer" + customerId);

            await request(server)
                .delete(`/v1/merchant/${merchantId}/customer/${customerId}/extra_data/${action}`)
                .expect(204);

            log.debug("Get error for " + customerId);
            await request(server)
                .get(`/v1/merchant/${merchantId}/customer/${customerId}/extra_data/${action}`)
                .expect(200)
                .expect({});
        });

    });

    describe("Not save anything", () => {

        const trxId = uuid.v4();
        const roundId = Math.floor(Math.random() * 9999) + 10;
        let ticket;

        beforeEach(async () => {
            settings.notSaveAnyData = true;
            ticket = uuid.v4();

        });

        it("Send validate ticket", async () => {
            log.debug("Validate ticket: " + ticket);

            await request(server)
                .post("/api/validate_ticket")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    ticket: ticket,
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.have.property("cust_id", ticket);
                    expect(response.body).to.have.property("cust_session_id", ticket);
                });
        });

        describe("Using template for customer's name and currency", () => {
            beforeEach(async () => {
                ticket = `${customerId}__BYN__${uuid.v4()}`;

            });

            it("Send validate ticket with ", async () => {
                log.debug("Validate ticket: " + ticket);

                await request(server)
                    .post("/api/validate_ticket")
                    .type("form")
                    .set("Accept", "application/json")
                    .send({
                        ticket: ticket,
                        merch_id: merchantId,
                        merch_pwd: merchantPassword,
                    })
                    .expect(200)
                    .expect("Content-Type", "application/json; charset=utf-8")

                    .then(response => {
                        expect(response.body).to.deep.equal(
                            {
                                "cust_id": customerId,
                                "cust_login": "",
                                "currency_code": "BYN",
                                "status": "normal",
                                "bet_limit": null,
                                "test_cust": false,
                                "country": "CN",
                                "language": "en",
                                "balance": {
                                    "amount": settings.amount,
                                    "currency_code": "BYN"
                                },
                                "error_code": 0,
                                "cust_session_id": ticket,
                            });
                    });
            });

            it("Get balance", async () => {

                log.debug("Get balance with cust_session_id: " + ticket);

                await request(server)
                    .post("/api/get_balance")
                    .type("form")
                    .set("Accept", "application/json")
                    .send({
                        cust_session_id: ticket,
                        merch_id: merchantId,
                        merch_pwd: merchantPassword,
                    })
                    .expect(200)
                    .expect("Content-Type", "application/json; charset=utf-8")
                    .then(response => {
                        expect(response.body).to.deep.equal(
                            {
                                error_code: 0,
                                balance: settings.amount,
                                currency_code: "BYN"
                            });
                    });
            });
        });

        it("Get balance", async () => {

            log.debug("Get balance with cust_session_id: " + ticket);

            await request(server)
                .post("/api/get_balance")
                .type("form")
                .set("Accept", "application/json")
                .send({
                    cust_id: "customer__" + ticket,
                    cust_session_id: ticket,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                })
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")
                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: settings.amount,
                            currency_code: defaultCustomerCurrency
                        });
                });
        });

        it("Debit Customer", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: ticket,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: settings.amount,
                            trx_id: trxId,
                        });
                });

        });

        it("Credit Customer", async () => {

            const bet = Math.floor(Math.random() * (balance - 10)) + 10;

            log.debug(`Send BET (debit) for trxId: ${trxId} bet: ${bet}`);
            await request(server)
                .post("/api/debit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: ticket,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: bet,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "bet",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: settings.amount,
                            trx_id: trxId,
                        });
                });

            const win = Math.floor(Math.random() * 10);
            log.debug(`Send WIN (credit) for trxId: ${trxId} win: ${win}`);
            await request(server)
                .post("/api/credit")
                .type("form")
                .send({
                    cust_id: customerId,
                    cust_session_id: ticket,
                    game_code: "sw_9s1k",
                    merch_id: merchantId,
                    merch_pwd: merchantPassword,
                    amount: win,
                    currency_code: "USD",
                    trx_id: trxId,
                    game_id: roundId,
                    event_type: "win",
                    timestamp: Date.now(),
                    event_id: 0,
                    platform: "web",
                    game_type: "normal",
                    game_status: "settled",
                    round_id: roundId
                })
                .set("Accept", "application/json")
                .expect(200)
                .expect("Content-Type", "application/json; charset=utf-8")

                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            error_code: 0,
                            balance: settings.amount,
                            trx_id: trxId,
                        });
                });
        });

    });
});
