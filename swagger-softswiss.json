{"/play": {"post": {"description": "", "tags": ["Api"], "parameters": [{"name": "payload", "in": "body", "required": true, "schema": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User's id in Wallet side", "example": "Customer123"}, "currency": {"type": "string", "description": "3 letters identifier of currency in upper case", "example": "USD"}, "game": {"type": "string", "description": "Game's identifier. In case there is no optional actions were provided in request, GCP should always respond to such request regardless of game value"}, "actions": {"type": "array", "description": "array of objects of type either bet or win", "items": {"type": "object", "properties": {"action": {"type": "string", "description": "bet or win"}, "amount": {"type": "number", "description": "not negative amount in subunits"}, "action_id": {"type": "string", "description": "id of action on GCP side. Wallet shouldn't process actions with same action_id twice"}}}}}}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Merchant"}}}}}, "/rollback": {"post": {"description": "", "tags": ["Api"], "parameters": [{"name": "payload", "in": "body", "required": true, "schema": {"type": "object", "properties": {"user_id": {"type": "string", "description": "User's id in casino", "example": "Customer123"}, "currency": {"type": "string", "description": "3 letters identifier of currency in upper case", "example": "USD"}, "game": {"type": "string", "description": "Game's identifier"}, "actions": {"type": "array", "items": {"type": "object", "properties": {"action": {"type": "string", "description": "rollback"}, "action_id": {"type": "string", "description": "id of the rollback action on GCP side. Wallet shouldn't process rollback with same action_id twice"}, "original_action_id": {"type": "string", "description": "id (on GCP side) of the action to rollback"}}}}}}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Merchant"}}}}}, "/freespins": {"post": {"description": "", "tags": ["Api"], "parameters": [{"name": "payload", "in": "body", "required": true, "schema": {"type": "object", "properties": {"issue_id": {"type": "string", "description": "freespin issue id in casino"}, "status": {"type": "string", "description": "active\nexpired\nplayed - all freespins are performed"}, "total_amount": {"type": "number", "description": "Total win during freespins"}}}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/Merchant"}}}}}}