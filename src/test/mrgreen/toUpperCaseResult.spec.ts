import { suite, test } from "mocha-typescript";
import { toUpperCaseResult } from "../../skywind/integrations/mrGreen/api";
import { expect } from "chai";

@suite
class ToUpperCaseResultSpec {
    @test
    public test() {
        const a = {
            "alreadyProcessed": true,
            "balance": 1460,
            "details": {
                "baseCurrency": "SEK",
                "baseCurrencyRate": 1,
                "bonusBalance": 0,
                "bonusMoneyBet": 0,
                "realBalance": 1460,
                "realMoneyBet": 10
            },
            "msg": "",
            "rc": 0,
            "request": "wager",
            "sessionId": "TICKET-1580992395535-201452",
            "walletTransactionId": 18971630
        };

        const result = toUpperCaseResult(a);

        expect(result).deep.eq({
  "ALREADYPROCESSED": true,
  "BALANCE": 1460,
  "DETAILS": {
    "BASECURRENCY": "SEK",
    "BASECURRENCYRATE": 1,
    "BONUSBALANCE": 0,
    "BONUSMONEYBET": 0,
    "REALBALANCE": 1460,
    "REALMONEYBET": 10
  },
  "MSG": "",
  "RC": 0,
  "REQUEST": "wager",
  "SESSIONID": "TICKET-1580992395535-201452",
  "WALLETTRANSACTIONID": 18971630
});

    }
}
