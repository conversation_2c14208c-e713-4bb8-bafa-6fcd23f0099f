import { NextFunction, Request, Response, Router } from "express";
import {
    authResponder,
    balanceResponder,
    notificationResponder,
    paymentResponder
} from "../service/pariplay/responders";
import { authenticate, authenticateCustomer } from "./config/pariplayMiddleware";
import { CustomerHolder, MerchantHolder } from "../entities/common";
import { getPariplayService } from "../service/pariplay/service";
import { notSaveAnyDataMiddleware } from "./notSaveAnyData/not-save-any-data.middleware";

const router: Router = Router();

router.post("/Authenticate",
    notSaveAnyDataMiddleware(authResponder),
    authenticate,
    async (req: Request & MerchantHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getPariplayService(req.merchant).authenticate(req.body);
            return res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/GetBalance",
    notSaveAnyDataMiddleware(balanceResponder),
    authenticate,
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getPariplayService(req.merchant, req.customer).getBalance(req.body);
            return res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/Debit",
    notSaveAnyDataMiddleware(paymentResponder),
    authenticate,
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getPariplayService(req.merchant, req.customer).debit(req.body);
            return res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/Credit",
    notSaveAnyDataMiddleware(paymentResponder),
    authenticate,
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getPariplayService(req.merchant, req.customer).credit(req.body);
            return res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/CancelBet",
    notSaveAnyDataMiddleware(paymentResponder),
    authenticate,
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getPariplayService(req.merchant, req.customer).cancelBet(req.body);
            return res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/EndRound",
    notSaveAnyDataMiddleware(paymentResponder),
    authenticate,
    authenticateCustomer,
    (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = getPariplayService(req.merchant, req.customer).credit(
                { ...req.body, Amount: 0, IsRoundEnded: true });
            return res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/RegulationNotification",
    notSaveAnyDataMiddleware(notificationResponder),
    authenticate,
    (req: Request & MerchantHolder, res: Response, next: NextFunction) => {
        return res.send(getPariplayService(req.merchant).getNotification());
    });

export default router;
