import { Request } from "express";
import {
    SBTechInitSessionResponse,
    SBTechDebitResponse,
    SBTechCreditResponse,
    SBTechGetBalanceResponse,
    SBTechEndRoundResponse,
    SBTechRollbackTransactionResponse,
    SBTechExtraRequestData,
    WALLET_TYPE
} from "../../entities/sbtech";

export function initSession(req: Request & SBTechExtraRequestData): SBTechInitSessionResponse {
    return {
        playerId: "player_1",
        brandId: "brand_1",
        isTestPlayer: false,
        currencyCode: "USD",
        countryCode: "US",
        sessionId: "session_1",
        jurisdiction: "",
        balance: 100,
        timestamp: 123456
    } as SBTechInitSessionResponse;
}

export function debit(req: Request & SBTechExtraRequestData): SBTechDebitResponse {
    return {
        trxId: "transaction_1",
        walletType: WALLET_TYPE.REAL,
        sessionId: "session_1",
        balance: 100,
        timestamp: 123456,
    } as SBTechDebitResponse;
}

export function credit(req: Request & SBTechExtraRequestData): SBTechCreditResponse {
    return {
        trxId: "transaction_2",
        sessionId: "session_1",
        balance: 100,
        timestamp: 123456,
    } as SBTechCreditResponse;
}

export function rollbackTransaction(req: Request & SBTechExtraRequestData): SBTechRollbackTransactionResponse {
    return {
        trxId: "transaction_3",
        sessionId: "session_1",
        balance: 100,
        timestamp: 123456,
    } as SBTechRollbackTransactionResponse;
}

export function getBalance(req: Request & SBTechExtraRequestData): SBTechGetBalanceResponse {
    return {
        sessionId: "session_1",
        balance: 100,
        timestamp: 123456,
    } as SBTechGetBalanceResponse;
}

export function endRound(req: Request & SBTechExtraRequestData): SBTechEndRoundResponse {
    return {
        trxId: "transaction_4",
        sessionId: "session_1",
        balance: 100,
        timestamp: 123456,
    } as SBTechEndRoundResponse;
}
