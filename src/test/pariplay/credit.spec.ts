import { suite, test } from "mocha-typescript";
import * as uuid from "uuid";
import { expect } from "chai";
import * as Transaction from "../../skywind/models/transaction";
import { getTransaction<PERSON>ey, WALLET_ACTION } from "../../skywind/models/transaction";
import config from "../../skywind/config";
import { PariplayBaseSpec } from "./auth.spec";
import { PariplayPaymentResponse } from "../../skywind/entities/pariplay";
import { ACTION } from "../../skywind/service/pariplay/actions";

const request = require("supertest");

@suite
class PariplayCreditSpec extends PariplayBaseSpec {
    @test
    public async testCreditSuccess() {
        Transaction.setById(this.trxId, WALLET_ACTION.debit, Math.abs(this.debitRequest.Amount), this.playerCode);
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Credit")
            .send(this.creditRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount + 100);
        expect(response.body.TransactionId).to.be.equal(this.creditTrxId);
    }

    @test
    public async testMerchantNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Credit")
            .send({
                ...this.creditRequest,
                Account: { Username: "invalidMerch", Password: "SomePwd"}
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(2);
    }

    @test
    public async testPlayerNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Credit")
            .send({
                ...this.creditRequest,
                PlayerId: "invalidPlayer"
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async testCreditSessionNotFound() {
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Credit")
            .send({
                ...this.creditRequest,
                Token: "invalidSession"
            })
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async testCreditShouldBeIdempotency() {
        Transaction.setAll({
            [getTransactionKey(this.trxId, WALLET_ACTION.debit)]: [this.trxId, -100, uuid.v4(), true],
            [getTransactionKey(this.creditTrxId, WALLET_ACTION.credit)]: [this.creditTrxId, 100, uuid.v4(), true]
        });

        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Credit")
            .send(this.creditRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount);
        expect(response.body.TransactionId).to.be.equal(this.creditTrxId);
    }

    @test
    public async testCustomError() {
        Transaction.setAll({
            [getTransactionKey(this.trxId, WALLET_ACTION.debit)]: [this.trxId, -100, uuid.v4(), true],
        });
        this.createCustomError(900, ACTION.CREDIT);
        const response: { body: PariplayPaymentResponse } = await request(this.server)
            .post("/EVI/Credit")
            .send(this.creditRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(900);
    }
}
