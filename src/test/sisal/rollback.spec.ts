import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");

import { SisalBaseSpec } from "./base.spec";
import { WALLET_ACTION } from "../../skywind/models/transaction";
import * as TransactionModel from "../../skywind/models/transaction";
import { Ticket } from "../../skywind/models/ticket";
import { createTicket } from "../../skywind/service/ticket";
import { SISAL_ERROR_CODE } from "../../skywind/sisal/sisal.errors";
import { createActionError, RaiseType, sisalActionList } from "../../skywind/models/customError";

@suite("Sisal.rollback")
class SisalRollbackSpec extends SisalBaseSpec {

    @test("should rollback DEBIT transaction")
    public async rollback() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse = {
            gamePhaseId: "round1",
            sessionBalance: {
                "balance": 10000,
                bonusBalance: 0,
                playerId: this.fixtures.playerCode
            }
        };

        await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .post(this.urlPathToCancelWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.rollbackRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.rollbackRequest)
            .expect(200);

        delete actualResponse.body.inPhaseSeqNumber;
        delete actualResponse.body.playerGamePhaseId;
        delete actualResponse.body.playerGamePhaseWagerId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should return the same balance when rollback has already been processed")
    public async testNonProcessedRollback() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse = {
            gamePhaseId: "round1",
            sessionBalance: {
                "balance": 10000,
                bonusBalance: 0,
                playerId: this.fixtures.playerCode
            }
        };

        await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(200);

        await request(this.server)
            .post(this.urlPathToCancelWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.rollbackRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.rollbackRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .post(this.urlPathToCancelWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.rollbackRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.rollbackRequest)
            .expect(200);

        delete actualResponse.body.inPhaseSeqNumber;
        delete actualResponse.body.playerGamePhaseId;
        delete actualResponse.body.playerGamePhaseWagerId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("throw error when DEBIT transaction not found")
    public async throwErrorWhenDebitTransactionNotFound() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);

        const actualResponse = await request(this.server)
            .post(this.urlPathToCancelWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.rollbackRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.rollbackRequest)
            .expect(500);

        expect(actualResponse.body.code).deep.equal(SISAL_ERROR_CODE.INVALID_CANCEL_WAGER_ERROR);
    }

    @test("should throw TransactionAlreadyCancelledError")
    public async testTransactionAlreadyCancelled() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(200);

        TransactionModel.rollbackById("trxId_bet", WALLET_ACTION.debit);

        const actualResponse = await request(this.server)
            .post(this.urlPathToCancelWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.rollbackRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.rollbackRequest)
            .expect(500);

        expect(actualResponse.body.code).deep.equal(SISAL_ERROR_CODE.INVALID_CANCEL_WAGER_ERROR);
    }

    @test("throw custom error on CANCEL BET request")
    public async throwCustomErrorOnCancelBet() {
        createActionError(
            this.fixtures.merchantCode,
            this.fixtures.playerCode,
            "cancelwager",
            RaiseType.AFTER,
            [{
                "error_code": 9900,
                "error_msg": "Not permitted",
            }]
        );

        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .post(this.urlPathToCancelWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.rollbackRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.rollbackRequest)
            .expect(500);

        expect(actualResponse.body.code).deep.equal(SISAL_ERROR_CODE.UNAUTHORIZED_ERROR);
        expect(actualResponse.body.message).deep.equal("Not permitted");
    }
}
