import { NextFunction, Request, Response, Router } from "express";
import logger from "../utils/logger";
import { getSisalService } from "./sisal.service";
import * as responder from "./sisal.responder";
import {
    SisalExtraRequestData,
    SisalBalanceResponse,
    SisalWagerResponse,
    SisalWagerRequest,
    SisalEndWagerRequest,
    SisalCancelWagerRequest
} from "./sisal.entities";
import {
    authenticate,
    wrapApiControllers,
    notSaveAnyData,
} from "./sisal.middleware";
import { INTEGRATION_TYPE } from "../../config/integrationType";

const log = logger(`${INTEGRATION_TYPE.SISAL}:api`);
const router: Router = Router();

/**
 *  Used for Spain/Portugal jurisdiction
 */
router.get("/balance/:gameSessionId",
    notSaveAnyData(responder.sessionBalance),
    wrapApiControllers([
        authenticate,
        balance
    ]));

router.get("/:gameSessionId/sessionBalance",
    notSaveAnyData(responder.sessionBalance),
    wrapApiControllers([
        authenticate,
        sessionBalance
    ]));

router.post("/:gameSessionId/wager",
    notSaveAnyData(responder.wager),
    wrapApiControllers([
        authenticate,
        wager
    ]));

router.post("/:gameSessionId/endwager",
    notSaveAnyData(responder.endWager),
    wrapApiControllers([
        authenticate,
        endWager
    ]));

router.post("/:gameSessionId/cancelwager",
    notSaveAnyData(responder.cancelWager),
    wrapApiControllers([
        authenticate,
        cancelWager
    ]));

async function sessionBalance(req: Request & SisalExtraRequestData, res: Response, next: NextFunction) {
    const response: SisalBalanceResponse = await getSisalService(req.merchant, req.customer).sessionBalance();

    res.send(response);
}

async function balance(req: Request & SisalExtraRequestData, res: Response, next: NextFunction) {
    const response: SisalBalanceResponse = await getSisalService(req.merchant, req.customer).balance();

    res.send(response);
}

async function wager(req: Request & SisalExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SisalWagerRequest;
    const response: SisalWagerResponse = await getSisalService(req.merchant, req.customer).wager(data);

    res.send(response);
}

async function endWager(req: Request & SisalExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SisalEndWagerRequest;
    const response: SisalWagerResponse = await getSisalService(req.merchant, req.customer).endWager(data);

    res.send(response);
}

async function cancelWager(req: Request & SisalExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SisalCancelWagerRequest;
    const response: SisalWagerResponse = await getSisalService(req.merchant, req.customer).cancelWager(data);

    res.send(response);
}

export default router;
