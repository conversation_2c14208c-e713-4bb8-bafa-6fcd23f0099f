import {
    MessageContainer,
    PariplayBalanceRequest,
    PariplayBalanceResponse, PariplayDebitTransactionContainer,
    PariplayPaymentRequest,
    PariplayPaymentResponse,
    PariplayRollbackRequest,
    PariplayVerifyTokenRequest,
    PariplayVerifyTokenResponse,
    PariplayWinPaymentRequest
} from "../../entities/pariplay";
import { getTicket } from "../ticket";
import config from "../../config";
import {
    AmountNegativeError,
    InsufficientBalanceError,
    SessionExpiredError,
    UnknownTransactionError
} from "../../errorsPariplay";
import { Customer, Merchant } from "../../models/merchant";
import { throwCustomError, throwCustomErrorIfExists } from "../customError";
import { createCustomerSession } from "../session";
import { ACTION } from "./actions";
import * as ExtraDataService from "../extraData";
import { extraDataHandler } from "../extraData";
import * as Transaction from "../../models/transaction";
import { WALLET_ACTION } from "../../models/transaction";

export interface PariplayService {
    authenticate(data: PariplayVerifyTokenRequest): Promise<PariplayVerifyTokenResponse>;
    getBalance(data: PariplayBalanceRequest): Promise<PariplayBalanceResponse>;
    debit(data: PariplayPaymentRequest): Promise<PariplayPaymentResponse>;
    credit(data: PariplayWinPaymentRequest): Promise<PariplayPaymentResponse>;
    cancelBet(data: PariplayRollbackRequest): Promise<PariplayPaymentResponse>;
    getNotification(): MessageContainer;
}

export class PariplayInMemoryService implements PariplayService {
    constructor(public merchant: Merchant, public customer?: Customer) {
    }

    public async authenticate(data: PariplayVerifyTokenRequest): Promise<PariplayVerifyTokenResponse> {
        const ticket = getTicket(data.ClientToken, config.expirationTime.ticket, SessionExpiredError);
        const customer = this.merchant.customers[ticket.custId];

        if (!customer) {
            throw new SessionExpiredError();
        }

        await throwCustomErrorIfExists(ACTION.AUTHENTICATE, this.merchant, customer);
        this.updateCustomerSession(customer);

        const response: PariplayVerifyTokenResponse = {
            PlayerId: customer.cust_id,
            Token: customer.cust_session_id,
            Balance: customer.balance.amount,
            CurrencyCode: customer.balance.currency_code,
            CountryCode: customer.country,
            OperatorCode: this.merchant.merch_id,
            ErrorCode: 0
        };

        if (customer.bet_limit) {
            response.UserSettings = { MaxBet: +customer.bet_limit };
        }

        return ExtraDataService.getExtraData(response, ACTION.AUTHENTICATE, this.merchant, customer);
    }

    private updateCustomerSession(customer: Customer): void {
        createCustomerSession(this.merchant, customer);
    }

    @throwCustomError()
    @extraDataHandler()
    public async getBalance(data: PariplayBalanceRequest): Promise<PariplayBalanceResponse> {
        return {
            Balance: this.customer.balance.amount,
            ErrorCode: 0
        };
    }

    @throwCustomError()
    @extraDataHandler()
    public async debit(data: PariplayPaymentRequest): Promise<PariplayPaymentResponse> {
        const { Amount: amount } = data;
        if (amount <= 0) {
            throw new AmountNegativeError();
        }

        this.checkInsufficientBalance(data);
        return this.makePayment(data, Transaction.WALLET_ACTION.debit);
    }

    @throwCustomError()
    @extraDataHandler()
    public async credit(data: PariplayWinPaymentRequest): Promise<PariplayPaymentResponse> {
        return this.makePayment(data, Transaction.WALLET_ACTION.credit);
    }

    private makePayment(data: PariplayPaymentRequest, action: Transaction.WALLET_ACTION): PariplayPaymentResponse {
        const { Amount: amount, TransactionId: trxId } = data;

        const transaction = Transaction.getById(trxId, action);
        if (!transaction) {
            Transaction.setById(trxId, action, Math.abs(amount), this.customer.cust_id);

            if (!this.isFreebet(data, action)) {
                this.customer.balance.amount = this.customer.balance.amount + Math.abs(amount) * action;
            }
        }

        return {
            Balance: this.customer.balance.amount,
            TransactionId: trxId,
            ErrorCode: 0
        };
    }

    private isFreebet(data: PariplayPaymentRequest, action: WALLET_ACTION): boolean {
        return !!(action === Transaction.WALLET_ACTION.debit && data.BonusId);
    }

    @throwCustomError()
    @extraDataHandler()
    public async cancelBet(data: PariplayRollbackRequest): Promise<PariplayPaymentResponse> {
        this.checkDebitTransaction(data);

        const { TransactionId: rollbackTrxId, DebitTransactionId: debitTrxId } = data;

        if (!Transaction.getById(rollbackTrxId, WALLET_ACTION.rollback)) {
            const originalTransaction = Transaction.getById(debitTrxId, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new UnknownTransactionError();
            }

            const [_, amount, __, isRollback] = originalTransaction;
            if (!isRollback) {
                Transaction.rollbackById(debitTrxId, WALLET_ACTION.debit);
                this.customer.balance.amount = this.customer.balance.amount - amount;
            }

            Transaction.setById(rollbackTrxId, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        }

        return {
            TransactionId: rollbackTrxId,
            Balance: this.customer.balance.amount,
            ErrorCode: 0
        };
    }

    private checkInsufficientBalance(data: PariplayPaymentRequest): void {
        if ((this.customer.balance.amount + Math.abs(data.Amount) * Transaction.WALLET_ACTION.debit) < 0) {
            throw new InsufficientBalanceError();
        }
    }

    private checkDebitTransaction(data: PariplayDebitTransactionContainer): void {
        const debitTransaction = Transaction.getById(data.DebitTransactionId, Transaction.WALLET_ACTION.debit);
        if (!debitTransaction) {
            throw new UnknownTransactionError(data.DebitTransactionId);
        }
    }

    public getNotification(): MessageContainer {
        return {
            Message: [{
                Title: "title",
                Text: "message",
                DisplayType: 2,
                Buttons: [
                    {
                        Text: "Quit Game",
                        Action: "quit"
                    },
                    {
                        Text: "Ok",
                        Action: "continue"
                    },
                    {
                        Text: "View History",
                        Action: "link",
                        Link: "https://www.casino.com/myaccount/history"
                    }
                ]
            }]
        };
    }
}

export function getPariplayService(merchant: Merchant, customer?: Customer): PariplayService {
    return new PariplayInMemoryService(merchant, customer);
}
