# IPM Mock

## Environment variables

### Latency

| Variable                                 | Description                                              |
|------------------------------------------|----------------------------------------------------------|
|`IPM_MOCK_LATENCY`                        | Emulate network latency in ms (default: 0)               |
|`IMP_MOCK_LATENCY_SPREADING`              | Latency spreading in ms (default: 10% from latency)      |
|`IMP_MOCK_LATENCY_PATH_MASK`              | API mask (default /api/*)                                |
|`IPM_MOCK_TICKET_EXPIRATION_MS`           | Ticket expiration time in ms (default: 5 min)            |

### Expiration time

| Variable                                 | Description                                              |
|------------------------------------------|----------------------------------------------------------|
|`IPM_MOCK_TERMINAL_TICKET_EXPIRATION_MS`  | Terminal ticket expiration time in ms (default: 60 min)  |
|`IPM_MOCK_SESSION_EXPIRATION_MS`          | Customer session expiration time in ms (default: 5 min)  |
|`IPM_MOCK_TERMINAL_SESSION_EXPIRATION_MS` | Terminal session expiration time in ms (default: 60 min) |

### Other Settings

| Variable          | Description                                                                                             |
|-------------------|---------------------------------------------------------------------------------------------------------|
|`DECREASE_LOAD`    | If "true" we decreasing load on IPM-mock for Performance Test, we disable some checks (default: false)  |
|`NOT_SAVE_ANY_DATA`| If "true" we strong decreasing load on IPM-mock for Performance Test, we do not save any data (default: false)  |
|`LOG_LEVEL`        | Output log level (default: info)                                                                        |
|`MOCK_SETTINGS_CUSTOMER_AMOUNT`        | Customer default amount value (default: 1000000)                                    |

### Graylog

| Variable                        | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
|`GRAYLOG_HOST`                   | Hostname of Graylog server (optional)                          |
|`GRAYLOG_PORT`                   | Graylog port               (optional)                          |
