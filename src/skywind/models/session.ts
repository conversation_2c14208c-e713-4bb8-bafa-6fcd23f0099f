import { TICKET_TYPE_PLAYER } from "../utils/helper";

export interface CustomerSessionInfo {
    ts: number;
    type?: string;
    merchantId?: string;
    customerId?: string;
}

let listPlayerSession = {};

const customerSessions: { [session: string]: CustomerSessionInfo } = {};

export const deleteCustomerSession = (custId: string) => {
    const oldCustomerSessionId = listPlayerSession[custId];
    if (oldCustomerSessionId) {
        delete customerSessions[oldCustomerSessionId];
    }
};

export const deleteById = (sessionId: string) => delete customerSessions[sessionId];

export const getById = (sessionId: string) => customerSessions[sessionId];

export const clearAll = () => listPlayerSession = {};
/**
 * Save customer session to sessions storage with creation time.
 * @param customerSessionId
 * @param type
 * @param merchantId
 * @param customerId
 */
export function storeCustomerSession(customerSessionId: string,
                                     type: string = TICKET_TYPE_PLAYER,
                                     merchantId?: string,
                                     customerId?: string) {
    listPlayerSession[customerId] = customerSessionId;
    customerSessions[customerSessionId] = {
        ts: new Date().getTime(),
        type: type || TICKET_TYPE_PLAYER,
        merchantId,
        customerId
    };
}
