import { Request } from "express";
import {
    SisalExtraRequestData,
    SisalBalanceResponse,
    SisalWagerResponse
} from "./sisal.entities";

export function sessionBalance(req: Request & SisalExtraRequestData): SisalBalanceResponse {
    return {
        playerId: "sisal_cust",
        bonusBalance: 0,
        balance: 10000
    } as SisalBalanceResponse;
}

export function wager(req: Request & SisalExtraRequestData): SisalWagerResponse {
    return {
        gamePhaseId: 24, // sisal round id
        inPhaseSeqNumber: 42,
        playerGamePhaseId: 2121,
        playerGamePhaseWagerId: 1212,
        sessionBalance: {
            playerId: "sisal_cust",
            bonusBalance: 0,
            balance: 10000
        }
    } as SisalWagerResponse;
}

export function endWager(req: Request & SisalExtraRequestData): SisalWagerResponse {
    return {
        gamePhaseId: 24, // sisal round id
        inPhaseSeqNumber: 42,
        playerGamePhaseId: 2121,
        playerGamePhaseWagerId: 1212,
        sessionBalance: {
            playerId: "sisal_cust",
            bonusBalance: 0,
            balance: 15000
        }
    } as SisalWagerResponse;
}

export function cancelWager(req: Request & SisalExtraRequestData): SisalWagerResponse {
    return {
        gamePhaseId: 24, // sisal round id
        inPhaseSeqNumber: 42,
        playerGamePhaseId: 2121,
        playerGamePhaseWagerId: 1212,
        sessionBalance: {
            playerId: "sisal_cust",
            bonusBalance: 0,
            balance: 10000
        }
    } as SisalWagerResponse;
}
