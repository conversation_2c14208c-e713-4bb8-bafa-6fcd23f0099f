import { EntityNotFound } from "../errors";

let merchants: Merchants = {};

export const getAll = (): Merchants => merchants;

export const setAll = (newMerchants: Merchants) => merchants = newMerchants;

export const getById = (merchantId: string = "", raiseErrorIfNotExists: boolean = false): Merchant => {
    const merchant = merchants[merchantId];

    if (!merchant && raiseErrorIfNotExists) {
        throw new EntityNotFound("Entity", merchantId);
    }

    return merchant;
};

export const setById = (merchantId: string, merchantData: Merchant) => merchants[merchantId] = merchantData;

export const getCustomerById = (merchantId: string, customerId: string): Customer => {
    const merchant: Merchant = merchants[merchantId];
    if (merchant) {
        return merchant.customers && merchant.customers[customerId];
    }
};

interface Merchants {
    [merchantId: string]: Merchant;
}

export interface Merchant {
    merch_id: string;
    merch_pwd: string;
    customers?: {
        [customerId: string]: Customer;
    };
    isPromoInternal?: boolean;
    multiple_session?: boolean;
}

export interface MerchantInfo {
    merch_id: string;
    merch_pwd: string;
    isPromoInternal: string;
    multiple_session: string;
}

export interface Customer {
    cust_id: string;
    cust_login?: string;
    currency_code?: string;
    language?: string;
    country?: string;
    email?: string;
    test_cust?: boolean;
    first_name?: string;
    last_name?: string;
    date_of_birth?: string;
    status?: string;
    bet_limit?: number;
    rci?: number;
    balance?: CustomerBalance;
    bonusBalance?: number;
    cust_session_id?: string;
    freeBets?: {
        count: number;
        coin: number;
    };
    jurisdiction?: string;
    extraData?: CustomerExtraData;
    nickname?: string;
}

export interface CustomerExtraData {
    messageTitle?: string; // used in sisal
    messageBody?: string; // used in sisal
    messageBodyHtml?: string; // used in sisal
}

interface CustomerBalance {
    amount: number;
    currency_code: string;
}
