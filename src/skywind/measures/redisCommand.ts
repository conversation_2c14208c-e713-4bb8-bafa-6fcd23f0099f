export const RedisCommandList: string[] = [
    "connect",
    "scriptExists",
    "scriptLoad",
    "load",
    "acl",
    "append",
    "asking",
    "auth",
    "bgrewriteaof",
    "bgsave",
    "bitcount",
    "bitfield",
    "bitfield_ro",
    "bitop",
    "bitpos",
    "blmove",
    "blmpop",
    "blpop",
    "brpop",
    "brpoplpush",
    "bzmpop",
    "bzpopmax",
    "bzpopmin",
    "client",
    "cluster",
    "command",
    "config",
    "copy",
    "dbsize",
    "debug",
    "decr",
    "decrby",
    "del",
    "discard",
    "dump",
    "echo",
    "eval",
    "eval_ro",
    "evalsha",
    "evalsha_ro",
    "exec",
    "exists",
    "expire",
    "expireat",
    "expiretime",
    "failover",
    "fcall",
    "fcall_ro",
    "flushall",
    "flushdb",
    "function",
    "geoadd",
    "geodist",
    "geohash",
    "geopos",
    "georadius",
    "georadius_ro",
    "georadiusbymember",
    "georadiusbymember_ro",
    "geosearch",
    "geosearchstore",
    "get",
    "getbit",
    "getdel",
    "getex",
    "getrange",
    "getset",
    "hdel",
    "hello",
    "hexists",
    "hget",
    "hgetall",
    "hincrby",
    "hincrbyfloat",
    "hkeys",
    "hlen",
    "hmget",
    "hmset",
    "hrandfield",
    "hscan",
    "hset",
    "hsetnx",
    "hstrlen",
    "hvals",
    "incr",
    "incrby",
    "incrbyfloat",
    "info",
    "keys",
    "lastsave",
    "latency",
    "lcs",
    "lindex",
    "linsert",
    "llen",
    "lmove",
    "lmpop",
    "lolwut",
    "lpop",
    "lpos",
    "lpush",
    "lpushx",
    "lrange",
    "lrem",
    "lset",
    "ltrim",
    "memory",
    "mget",
    "migrate",
    "module",
    "monitor",
    "move",
    "mset",
    "msetnx",
    "multi",
    "object",
    "persist",
    "pexpire",
    "pexpireat",
    "pexpiretime",
    "pfadd",
    "pfcount",
    "pfdebug",
    "pfmerge",
    "pfselftest",
    "ping",
    "psetex",
    "psubscribe",
    "psync",
    "pttl",
    "publish",
    "pubsub",
    "punsubscribe",
    "quit",
    "randomkey",
    "readonly",
    "readwrite",
    "rename",
    "renamenx",
    "replconf",
    "replicaof",
    "reset",
    "restore",
    "restore-asking",
    "role",
    "rpop",
    "rpoplpush",
    "rpush",
    "rpushx",
    "sadd",
    "save",
    "scan",
    "scard",
    "script",
    "sdiff",
    "sdiffstore",
    "select",
    "set",
    "setbit",
    "setex",
    "setnx",
    "setrange",
    "shutdown",
    "sinter",
    "sintercard",
    "sinterstore",
    "sismember",
    "slaveof",
    "slowlog",
    "smembers",
    "smismember",
    "smove",
    "sort",
    "sort_ro",
    "spop",
    "spublish",
    "srandmember",
    "srem",
    "sscan",
    "ssubscribe",
    "strlen",
    "subscribe",
    "substr",
    "sunion",
    "sunionstore",
    "sunsubscribe",
    "swapdb",
    "sync",
    "time",
    "touch",
    "ttl",
    "type",
    "unlink",
    "unsubscribe",
    "unwatch",
    "wait",
    "watch",
    "xack",
    "xadd",
    "xautoclaim",
    "xclaim",
    "xdel",
    "xgroup",
    "xinfo",
    "xlen",
    "xpending",
    "xrange",
    "xread",
    "xreadgroup",
    "xrevrange",
    "xsetid",
    "xtrim",
    "zadd",
    "zcard",
    "zcount",
    "zdiff",
    "zdiffstore",
    "zincrby",
    "zinter",
    "zintercard",
    "zinterstore",
    "zlexcount",
    "zmpop",
    "zmscore",
    "zpopmax",
    "zpopmin",
    "zrandmember",
    "zrange",
    "zrangebylex",
    "zrangebyscore",
    "zrangestore",
    "zrank",
    "zrem",
    "zremrangebylex",
    "zremrangebyrank",
    "zremrangebyscore",
    "zrevrange",
    "zrevrangebylex",
    "zrevrangebyscore",
    "zrevrank",
    "zscan",
    "zscore",
    "zunion",
    "zunionstore",
];
