import { BaseErrorToLog, ErrorInfoToLog } from "./errors";

export interface GVCError extends BaseErrorToLog {
    responseStatus: number;
    errorCode: number;
    message: string;
    errorMsg: string;
}

export class GVCBaseError extends Error implements GVCError {
    public responseStatus: number;  // use field name that doesn't interfere with fastify ('status' or 'statusCode')
    public errorCode: number;
    public errorMsg: string;
    public extraData: any;

    constructor(status: number, errorCode?: number, errorMsg: string = "", extraData?: any) {
        super(errorMsg);
        this.responseStatus = status;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.extraData = extraData;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.errorCode,
                message: this.errorMsg,
                stack: this.stack
            }
        };
    }
}

export class TechnicalError extends GVCBaseError {
    constructor() {
        super(200, -1, "Technical error");
    }
}

export class InvalidSecureTokenError extends GVCBaseError {
    constructor() {
        super(200, 101, "Invalid secure token");
    }
}

export class BlockedCountryError extends GVCBaseError {
    constructor() {
        super(200, 102, "Player is from blocked country");
    }
}

export class GameNotFoundError extends GVCBaseError {
    constructor() {
        super(200, 103, "Game is disabled/not found");
    }
}

export class InsufficientBalanceError extends GVCBaseError {
    constructor() {
        super(200, 104, "Customer's balance is not sufficient to make a bet");
    }
}

export class CurrencyCodeMismatchError extends GVCBaseError {
    constructor() {
        super(200, 105, "Currency code mismatch");
    }
}

export class PlayerSessionExpiredError extends GVCBaseError {
    constructor() {
        super(200, 106, "Player's session expired");
    }
}

export class BetNotFoundError extends GVCBaseError {
    constructor() {
        super(200, 107, "Bet not found");
    }
}

export class FraudUserError extends GVCBaseError {
    constructor(value?: string) {
        super(200, 108, "Fraud user");
    }
}

export class ResponsibleGameBlockedError extends GVCBaseError {
    constructor() {
        super(200, 109, "Responsible game blocked");
    }
}

export class ServiceClosureByPLayerError extends GVCBaseError {
    constructor() {
        super(200, 110, "Service closure by player");
    }
}

export class ServiceClosureByAgentError extends GVCBaseError {
    constructor() {
        super(200, 111, "Service closure by agent");
    }
}

export class ServiceClosureError extends GVCBaseError {
    constructor() {
        super(200, 112, "Service closure (cool off period)");
    }
}

export class LocationError extends GVCBaseError {
    constructor() {
        super(200, 113, "GeoComply no location");
    }
}

export class InvalidLocationError extends GVCBaseError {
    constructor() {
        super(200, 114, "GeoComply location not valid");
    }
}

export class InvalidCurrencyCodeError extends GVCBaseError {
    constructor() {
        super(200, 115, "Invalid currency code");
    }
}

export class InvalidSkinCodeError extends GVCBaseError {
    constructor() {
        super(200, 116, "Invalid skin code");
    }
}

export class NoGameIsInProgressError extends GVCBaseError {
    constructor() {
        super(200, 117, "No game is on progress");
    }
}

export class BetAlreadySettledError extends GVCBaseError {
    constructor() {
        super(200, 118, "Bet already settled");
    }
}

export class InvalidBetAmountError extends GVCBaseError {
    constructor(value?: string) {
        super(200, 119, "Invalid bet amount");
    }
}

export class DuplicateTransactionRequestError extends GVCBaseError {
    constructor() {
        super(200, 120, "Duplicate transaction request");
    }
}

export class InvalidSessionError extends GVCBaseError {
    constructor() {
        super(200, 121, "Game session does not exist");
    }
}

export class InvalidAccountIdError extends GVCBaseError {
    constructor() {
        super(200, 122, "Invalid account");
    }
}

export class BetCancelationError extends GVCBaseError {
    constructor() {
        super(200, 123, "Failed to cancel one or more bets");
    }
}

export class RGSessionTimeLimitCrossedError extends GVCBaseError {
    constructor() {
        super(200, 124, "RG session time limit crossed");
    }
}

export class TransactionToCancelNotFoundError extends GVCBaseError {
    constructor() {
        super(200, 125, "Transaction to cancel not found");
    }
}

export class WinSubTransactionToCancelExistError extends GVCBaseError {
    constructor() {
        super(200, 126, "Transaction to cancel has win sub transactions");
    }
}

export class TransactionAlreadyCancelledError extends GVCBaseError {
    constructor(value?: string) {
        super(200, 127, "Transaction already cancelled" + value);
    }
}

export class InvalidValuesInRequestError extends GVCBaseError {
    constructor(value?: string) {
        super(200, 128, "Invalid values in request" + value);
    }
}

export class MandatoryParametersMissingError extends GVCBaseError {
    constructor(value?: string) {
        super(200, 129, "Mandatory parameters missing: " + value);
    }
}

export class GameNotOverError extends GVCBaseError {
    constructor() {
        super(200, 130, "Game not over");
    }
}

export class DailySpendingLimitExceededError extends GVCBaseError {
    constructor() {
        super(200, 131, "Daily spending limit exceeded");
    }
}

export class WeeklySpendingLimitExceededError extends GVCBaseError {
    constructor() {
        super(200, 133, "Weekly spending limit exceeded");
    }
}

export class MonthlySpendingLimitExceededError extends GVCBaseError {
    constructor() {
        super(200, 135, "Monthly spending limit exceeded");
    }
}

export class SessionLimitMetError extends GVCBaseError {
    constructor() {
        super(200, 137, "Session limit met");
    }
}

export class UnknownTransactionError extends GVCBaseError {
    constructor() {
        super(200, 200, "Unknown transaction");
    }
}
