import { getApplication } from "../../skywind/relaxServer";
import { Customer, Merchant, setAll } from "../../skywind/models/merchant";

import * as Session from "../../skywind/models/session";
import * as Transaction from "../../skywind/models/transaction";
import { createCustomerSession } from "../../skywind/service/session";
import { getDefaultCustomer } from "../../skywind/service/customer";

import { createActionError, RaiseType } from "../../skywind/models/customError";
import { createActionData } from "../../skywind/models/extraData";
import { RELAX_ACTION, RELAX_JURISDICTION } from "../../skywind/entities/relax";

export class RelaxBaseSpec {
    public server;
    public merchantCode = "relax";
    public playerCode = "relax";

    public sessionId: string;
    public merchant: Merchant;

    public async before() {
        this.server = await getApplication();
        Session.clearAll();
        Transaction.setAll({});

        const merchant: Merchant = {
            merch_id: this.merchantCode,
            merch_pwd: "test",
            multiple_session: true,
            customers: {}
        };
        setAll({ [this.merchantCode] : merchant });

        this.merchant = merchant;

        const customer = this.addPlayerToMerchant(this.playerCode);

        this.sessionId = createCustomerSession(merchant, customer);
    }

    public addPlayerToMerchant(name: string, jurisdiction = RELAX_JURISDICTION.MT): Customer {
        const customer: any = getDefaultCustomer(name);
        customer.jurisdiction = jurisdiction;
        this.merchant.customers[customer.cust_id] = customer;

        return customer;
    }

    public createCustomError(errorCode: number, action: RELAX_ACTION) {
        createActionError(this.merchantCode, this.playerCode, action, RaiseType.AFTER, {
            "error_code": errorCode,
            "http_response_status": 200,
            "error_msg": "My custom error",
            "custom_field": "Some additional data to error"
        });
    }

    public createExtraData(data: object, action: RELAX_ACTION) {
        createActionData(this.merchantCode, this.playerCode, action, data);
    }
}
