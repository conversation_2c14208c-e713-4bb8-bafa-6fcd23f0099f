export class SBTechError extends Error {
    constructor(public error: string, public errorMessage: string) {
        super(errorMessage);
    }
}

export class WrongCredentialsError extends SBTechError {
    constructor(message?) {
        super("WRONG_CREDENTIALS",
            message || "The providerKey or the providerPass provided on the request are wrong");
    }
}

export class InvalidClientTokenError extends SBTechError {
    constructor(message?) {
        super("INVALID_CLIENT_TOKEN",
            message || "The clientToken provided on the request doesn’t exist");
    }
}

export class InvalidSessionIdError extends SBTechError {
    constructor(message?) {
        super("INVALID_SESSIONID",
            message || "The clientToken provided has been expired or player logged-out");
    }
}

export class PlayerIsBlockedError extends SBTechError {
    constructor(message?) {
        super("PLAYER_IS_BLOCKED",
            message || "The player is blocked from launching the game");
    }
}

export class ForbiddenGeoLocationError extends SBTechError {
    constructor(message?) {
        super("FORBIDDEN_GEO_LOCATION",
            message || "Player is restricted from making wagers due to its geo-location");
    }
}

export class InternalError extends SBTechError {
    constructor(message?) {
        super("INTERNAL_ERROR",
            message || "The Service had an Internal Error and rejected the request");
    }
}

export class InsufficientFundsError extends SBTechError {
    constructor(message?) {
        super("INSUFFICIENT_FUNDS",
            message || "Player doesn’t have enough money to place the bet");
    }
}

export class InsufficientFundsRealMoneyError extends SBTechError {
    constructor(message?) {
        super("INSUFFICIENT_FUNDS_REAL_MONEY",
            message || "Player doesn’t have enough real money to place the bet or tip");
    }
}

export class ResponsibleGamingLossLimitError extends SBTechError {
    constructor(message?) {
        super("RESPONSIBLE_GAMING_LOSS_LIMIT",
            message || "Player is restricted from making wagers due to loss limit configured on its account");
    }
}

export class ResponsibleGamingSessionTimeoutError extends SBTechError {
    constructor(message?) {
        super("RESPONSIBLE_GAMING_SESSION_TIMEOUT",
            message || "Player’s session has ended");
    }
}

export class ResponsibleGamingDepositLimitError extends SBTechError {
    constructor(message?) {
        super("RESPONSIBLE_GAMING_DEPOSIT_LIMIT",
            message || "Player’s deposit limit reached its threshold");
    }
}

export class ResponsibleGamingTurnoverLimitError extends SBTechError {
    constructor(message?) {
        super("RESPONSIBLE_GAMING_TURNOVER_LIMIT",
            message || "Player’s turn over limit reached its threshold");
    }
}

export class ResponsibleGamingLifetimeDepositError extends SBTechError {
    constructor(message?) {
        super("RESPONSIBLE_GAMING_LIFETIME_DEPOSIT",
            message || "Player’s lifetime deposit limit reached its threshold");
    }
}

export class ResponsibleGamingTimeLimitError extends SBTechError {
    constructor(message?) {
        super("RESPONSIBLE_GAMING_TIME_LIMIT",
            message || "Player’s time limit reached its threshold");
    }
}

export class ResponsibleGamingError extends SBTechError {
    constructor(message?) {
        super("RESPONSIBLE_GAMING_ERROR",
            message || "Player is restricted from making wagers");
    }
}

export class RoundAlreadyEndedError extends SBTechError {
    constructor(message?) {
        super("ROUND_ALREADY_ENDED",
            message || "The round has already ended and debits calls are not allowed anymore for that round");
    }
}

export class RoundDoesntExistError extends SBTechError {
    constructor(message?) {
        super("ROUND_DOESNT_EXIST",
            message || "The round doesn’t exist on the operator’s system");
    }
}

export class TransactionDoesntExistError extends SBTechError {
    constructor(message?) {
        super("TRANSACTION_DOESNT_EXIST",
            message || "The debit transaction Id doesn’t exist on the operator side");
    }
}

export class GameUnavailableError extends SBTechError {
    constructor(message?) {
        super("GAME_UNAVAILABLE",
            message || "The game doesn’t support free spins");
    }
}

export class InvalidCoinValueError extends SBTechError {
    constructor(message?) {
        super("INVALID_COIN_VALUE",
            message || "The game doesn’t support the coin value provided");
    }
}

export class PromotionIdWasntFoundError extends SBTechError {
    constructor(message?) {
        super("PROMOTION_ID_WASNT_FOUND",
            message || "The promotion ID provided wasn’t found");
    }
}

export enum SBTECH_ERROR_NAME {
    WRONG_CREDENTIALS = "WRONG_CREDENTIALS",
    INVALID_CLIENT_TOKEN = "INVALID_CLIENT_TOKEN",
    INVALID_SESSIONID = "INVALID_SESSIONID",
    PLAYER_IS_BLOCKED = "PLAYER_IS_BLOCKED",
    FORBIDDEN_GEO_LOCATION = "FORBIDDEN_GEO_LOCATION",
    INTERNAL_ERROR = "INTERNAL_ERROR",
    INSUFFICIENT_FUNDS = "INSUFFICIENT_FUNDS",
    INSUFFICIENT_FUNDS_REAL_MONEY = "INSUFFICIENT_FUNDS_REAL_MONEY",
    RESPONSIBLE_GAMING_LOSS_LIMIT = "RESPONSIBLE_GAMING_LOSS_LIMIT",
    RESPONSIBLE_GAMING_SESSION_TIMEOUT = "RESPONSIBLE_GAMING_SESSION_TIMEOUT",
    RESPONSIBLE_GAMING_DEPOSIT_LIMIT = "RESPONSIBLE_GAMING_DEPOSIT_LIMIT",
    RESPONSIBLE_GAMING_TURNOVER_LIMIT = "RESPONSIBLE_GAMING_TURNOVER_LIMIT",
    RESPONSIBLE_GAMING_LIFETIME_DEPOSIT = "RESPONSIBLE_GAMING_LIFETIME_DEPOSIT",
    RESPONSIBLE_GAMING_TIME_LIMIT = "RESPONSIBLE_GAMING_TIME_LIMIT",
    RESPONSIBLE_GAMING_ERROR = "RESPONSIBLE_GAMING_ERROR",
    ROUND_ALREADY_ENDED = "ROUND_ALREADY_ENDED",
    ROUND_DOESNT_EXIST = "ROUND_DOESNT_EXIST",
    TRANSACTION_DOESNT_EXIST = "TRANSACTION_DOESNT_EXIST"
}
