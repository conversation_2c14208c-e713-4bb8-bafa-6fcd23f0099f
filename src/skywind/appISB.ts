// this should be the first line
import { measures, logging, sleep } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import config from "./config";

logging.setUpOutput({ type: config.loggingOutput, logLevel: config.logLevel });
logging.setRootLogger("ipm-mock");

import * as fs from "fs";
import { Server } from "http";
import { Application, NextFunction, Request, Response } from "express";
import { requestLogging } from "./api/config/middleware";
import createApplication from "../config/express";
import { INTEGRATION_TYPE } from "../config/integrationType";
import logger from "./utils/logger";
import { emulateLatency } from "./utils/helper";
import { syncModels } from "./utils/syncModels";
import health from "./api/health";
import settingsRoutes from "./api/settings";
import merchant from "./api/merchant-management";
import version from "../skywind/api/version";
import isbApi from "./integrations/ISB/api";
import { ERROR_ACTION, ERROR_CODES, ISBError, OperatorErrorAction } from "./integrations/ISB/errors";
import measureProvider = measures.measureProvider;
import { startServer as startInternalServer } from "./serverInternal";
import { getSwagger, setupSwagger } from "./utils/swagger";
import { updateSwaggerDocument } from "./api/routers";

const iVersion = fs.readFileSync(__dirname + "/version", "utf8");

config.serverName = INTEGRATION_TYPE.ISB;
const log = logger(config.serverName);

const startServer = async (port = 8000): Promise<Server>  => {
    if (config.db.syncOnStart) {
        await syncModels();
    }
    const app = await getApplication();
    return new Promise<Server>((resolve) => {
        const server: Server = require("http")
            .createServer(app)
            .listen(port, null, () => {
                log.info("IPM mock server listening on " + port);
                log.info("AppVersion: " + iVersion);
                resolve(server);
            });
    });
};

const addHeaderCORS = (req: Request, res: Response, next: NextFunction) => {
    if (res.headersSent) {
        next();
        return;
    }
    res.setHeader("Access-Control-Allow-Origin", "*");
    next();
};
const getErrorCode = (code: string): ERROR_CODES => {
    const keys = Object.keys(ERROR_CODES).filter(key => ERROR_CODES[key] === code);
    return keys.length > 0 ? ERROR_CODES[keys[0]] : ERROR_CODES.ERR_GENERAL_REQUEST;
};

const errorHandler = async (err: Error,
                            req: Request,
                            res: Response,
                            next: NextFunction) => {

    measureProvider.saveError(err);
    log.error(err.name, err.message);
    if (err["timeout"]) {
        await sleep(err["timeout"]);
    }
    if (err instanceof ISBError) {
        return res.status(err.getStatus()).json(err.getOperatorError());
    } else if (err["responseStatus"] && err["errorMsg"] && err["errorCode"]) {
        const isbError = new ISBError(...getISBErrorConstructorArguments(err));
        return res.status(isbError.getStatus()).json(isbError.getOperatorError());
    } else {
        return res.status(500).send("Internal server error");
    }
};

function getISBErrorConstructorArguments(errorObject): [ERROR_CODES, string, number, ERROR_ACTION, boolean, boolean,
    [OperatorErrorAction]] {
    return [
        getErrorCode(errorObject["errorCode"]),
        errorObject["errorMsg"] || "Unknown error message!",
        errorObject["responseStatus"] || 500,
        errorObject.action,
        errorObject.display,
        errorObject.retry,
        errorObject.buttons
    ];
}

const defineRoutes = async (app: Application) => {
    if (Number.isFinite(config.mockLatency)) {
        app.use(config.mockLatencyPathMask, emulateLatency);
    }
    const swagger = await getSwagger((doc) => updateSwaggerDocument(INTEGRATION_TYPE.ISB, doc));
    setupSwagger(app, swagger);

    app.use(requestLogging(log));
    app.use("/v1/*", addHeaderCORS);
    app.use("/v1", version);
    app.use("/v1", health);
    app.use("/v1", settingsRoutes);
    app.use("/rest/service", isbApi);
    app.use(errorHandler);
    app.use("/v1", merchant);
};

export async function getApplication(): Promise<Application> {
    const app: Application = createApplication();
    await defineRoutes(app);
    return app;
}

(async () => {
    await startServer();
    await startInternalServer(INTEGRATION_TYPE.ISB);
})();
