import { Request, Response } from "express";
import { getParamValues } from "../../utils/helper";
import { settings } from "../../models/settings";

export function ipmNotSaveAnyDataResponder(req: Request, res: Response, responseData?: object) {
    const data: any = responseData || {
        error_code: 0,
        balance: settings.amount,
        trx_id: getParamValues(req, ["trx_id"]).pop()
    };

    if (req.header("Accept") === "application/json") {
        return data;
    }

    let responseString: string = "";
    for (const property in data) {
        if (!data.hasOwnProperty(property)) {
            continue;
        }
        responseString += `${property}=${data[property]}\n`;
    }
    return responseString.slice(0, -1);
}
