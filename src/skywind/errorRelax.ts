import { BaseErrorToLog, ErrorInfoToLog } from "./errors";

export interface RelaxError extends BaseErrorToLog {
    errorcode?: string;
    status: string;
    responseStatus: number;
    errormessage: string;
}

export class RelaxBaseError extends Error implements RelaxError {
    public status = "error";
    constructor(public responseStatus: number, public errorcode: string, public errormessage: string) {
        super(errormessage);
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.errorcode,
                message: this.errormessage,
                stack: this.stack
            }
        };
    }
}

export class RelaxUnhandledError extends RelaxBaseError {
    constructor(reason: string = "Unhandled error") {
        super(500, "UNHANDLED", reason);
    }
}

export class RelaxInvalidTokenError extends RelaxBaseError {
    constructor() {
        super(400, "INVALID_TOKEN", "Invalid token");
    }
}

export class RelaxInvalidParameterError extends RelaxBaseError {
    constructor(message: string) {
        super(400, "INVALID_PARAMETERS", message);
    }
}

export class RelaxUnauthorizedError extends RelaxBaseError {
    constructor() {
        super(401, "UNAUTHORIZED", "Unauthorized");
    }
}

export class RelaxSessionExpiredError extends RelaxBaseError {
    constructor() {
        super(403, "SESSION_EXPIRED", "Session expired");
    }
}

export class RelaxInsufficientBalanceError extends RelaxBaseError {
    constructor() {
        super(400, "INSUFFICIENT_FUNDS", "Insufficient balance");

    }
}

export class CurrencySessionMismatch extends RelaxBaseError {
    constructor() {
        super(400, "CURRENCY_SESSION_MISMATCH", "Invalid currency");

    }
}

export class RelaxTransactionNotFound extends RelaxBaseError {
    constructor() {
        super(404, "TRANSACTION_NOT_FOUND", "Transaction not found");
    }
}

export class RelaxBlockedFromProduct extends RelaxBaseError {
    constructor() {
        super(400, "BLOCKED_FROM_PRODUCT", "Blocked from product");
    }
}

export class RelaxLossLimitError extends RelaxBaseError {
    public events: { event: string }[];
    constructor() {
        super(400, "SPENDING_BUDGET_EXCEEDED", "Spending budget exceeded");
        this.events = [{ event: "losslimit" }];
    }
}

export class RelaxDailyTimeLimit extends RelaxBaseError {
    constructor() {
        super(400, "DAILY_TIME_LIMIT", "Daily time limit");
    }
}

export class RelaxWeeklyTimeLimit extends RelaxBaseError {
    constructor() {
        super(400, "WEEKLY_TIME_LIMIT", "Weekly time limit");
    }
}

export class RelaxMonthlyTimeLimit extends RelaxBaseError {
    constructor() {
        super(400, "MONTHLY_TIME_LIMIT", "Monthly time limit");
    }
}
