{"name": "@skywind-group/sw-management-playersession", "version": "2.140.0-develop", "description": "", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p lib/skywind && echo $(node -p \"require('./package.json').version\") $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version"}, "devDependencies": {"@skywind-group/sw-wallet-adapter-core": "2.1.6", "ioredis": "5.5.0"}}