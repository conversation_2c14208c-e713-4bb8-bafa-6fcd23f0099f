import {
    BalanceRequest,
    BetRequest,
    WinRequest,
    EMAuthenticateRequest,
    EMAuthenticateResponse,
    EVERY_MATRIX_JURISDICTION,
    EVERY_MATRIX_SEX,
    CancelBetRequest,
    BaseResponse,
    EveryMatrixPlayerInfo,
    BonusWinRequest,
    EMRoundInfoResponse,
    EMRoundInfoRequest,
} from "./entities";
import config from "../../config";
import {
    CurrencyDoesNotMatch,
    EVERY_MATRIX_STATUS,
    IEveryMatrixError, InsufficientFunds, IPIsNotAllowed,
    TokenNotFound, UnknownError, UserIsBlocked,
} from "./errors";

class PlayerTransactionsCacheItem {
    constructor(userId: string) {
        this.UserId = userId;
        this.Bets = [];
        this.Wins = [];
        this.Cancels = [];
    }

    public UserId: string;
    public Bets: BetRequest[];
    public Wins: WinRequest[];
    public Cancels: CancelBetRequest[];

    public clear() {
        this.Bets = [];
        this.Wins = [];
        this.Cancels = [];
    }
}

class TransactionsHolder {
    private static cache: PlayerTransactionsCacheItem[] = [];

    public static checkIfBetAlreadyApplied(userId: string, win: BetRequest) {
        const cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            return false;
        }

        return !!cacheItem.Bets.find(x => x.ExternalId === win.ExternalId);
    }

    public static addBet(userId: string, bet: BetRequest) {
        let cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            cacheItem = new PlayerTransactionsCacheItem(userId);
            TransactionsHolder.cache.push(cacheItem);
        }

        cacheItem.Bets.push(bet);
    }

    public static checkIfWinAlreadyApplied(userId: string, win: WinRequest) {
        const cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            return false;
        }

        return !!cacheItem.Wins.find(x => x.ExternalId === win.ExternalId);
    }

    public static addWin(userId: string, win: WinRequest) {
        let cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            cacheItem = new PlayerTransactionsCacheItem(userId);
            TransactionsHolder.cache.push(cacheItem);
        }

        cacheItem.Wins.push(win);
    }

    public static checkIfCancelBetAlreadyApplied(userId: string, cancel: CancelBetRequest) {
        const cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            return false;
        }

        return !!cacheItem.Cancels.find(x => x.ExternalId === cancel.ExternalId);
    }

    public static addCancelBet(userId: string, cancel: CancelBetRequest) {
        let cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            cacheItem = new PlayerTransactionsCacheItem(userId);
            TransactionsHolder.cache.push(cacheItem);
        }

        cacheItem.Cancels.push(cancel);
    }

    public static clearPlayerHistory(userId: string) {
        const cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (cacheItem) {
            cacheItem.clear();
        }
    }

    public static findBet (userId: string, externalId: string) {
        const cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            return;
        }

        return cacheItem.Bets.find(x => x.ExternalId === externalId);
    }

    public static findWin(userId: string, externalId: string) {
        const cacheItem = TransactionsHolder.cache.find(x => x.UserId === userId);

        if (!cacheItem) {
            return;
        }

        return cacheItem.Wins.find(x => x.ExternalId === externalId);
    }

    public static getPlayerHistory(userId: string) {
        return  TransactionsHolder.cache.find(x => x.UserId === userId);
    }
}

export class EvereMatrixSimpleEngineService {
    private static players: EveryMatrixPlayerInfo[] = [
        {
            "LaunchToken": "SimpleEngine:LaunchToken:Olga:EUR:UK",
            "GameToken": "SimpleEngine:GameToken:Olga:EUR:UK",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "EUR",
            "UserName": "Olga",
            "UserId": "Olga1",
            "Country": "Ukraine",
            "Age": 26,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
                "LaunchToken": "SimpleEngine:LaunchToken:Ivan:EUR:MT",
                "GameToken": "SimpleEngine:GameToken:Ivan:EUR:MT",
                "TotalBalance": config.defaultSettings.amount,
                "Currency": "EUR",
                "UserName": "Ivan",
                "UserId": "Ivan2",
                "Country": "Ukraine",
                "Age": 35,
                "Sex": EVERY_MATRIX_SEX.Male,
                "Jurisdiction": EVERY_MATRIX_JURISDICTION.MT
        }, {
                "LaunchToken": "SimpleEngine:LaunchToken:Valera:EUR:UK",
                "GameToken": "SimpleEngine:GameToken:Valera:EUR:UK",
                "TotalBalance": config.defaultSettings.amount,
                "Currency": "EUR",
                "UserName": "Valera",
                "UserId": "Valera3",
                "Country": "Ukraine",
                "Age": 42,
                "Sex": EVERY_MATRIX_SEX.Male,
                "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
                "LaunchToken": "SimpleEngine:LaunchToken:Bet:UnknownError:EUR:UK",
                "GameToken": "SimpleEngine:GameToken:Bet:UnknownError:EUR:UK",
                "TotalBalance": config.defaultSettings.amount,
                "Currency": "EUR",
                "UserName": "Bet:UnknownError",
                "UserId": "BetUnknown4",
                "Country": "ErrorLand",
                "Age": 44,
                "Sex": EVERY_MATRIX_SEX.Female,
                "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
            "LaunchToken": "SimpleEngine:LaunchToken:Bet:Cancel:UnknownError:EUR:UK",
            "GameToken": "SimpleEngine:GameToken:Bet:Cancel:UnknownError:EUR:UK",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "EUR",
            "UserName": "Bet:Cancel:UnknownError",
            "UserId": "BetCancelUnknown5",
            "Country": "ErrorLand",
            "Age": 44,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
                "LaunchToken": "SimpleEngine:LaunchToken:Bet:CurrencyDoesNotMatch:EUR:UK",
                "GameToken": "SimpleEngine:GameToken:Bet:CurrencyDoesNotMatch:EUR:UK",
                "TotalBalance": config.defaultSettings.amount,
                "Currency": "EUR",
                "UserName": "Bet:CurrencyDoesNotMatch",
                "UserId": "BetCurrency6",
                "Country": "ErrorLand",
                "Age": 44,
                "Sex": EVERY_MATRIX_SEX.Female,
                "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
            "LaunchToken": "SimpleEngine:LaunchToken:Win:UnknownError:EUR:UK",
            "GameToken": "SimpleEngine:GameToken:Win:UnknownError:EUR:UK",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "USD",
            "UserName": "Win:UnknownError",
            "UserId": "WinUnknown7",
            "Country": "ErrorLand",
            "Age": 56,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
            "LaunchToken": "SimpleEngine:LaunchToken:BonusWin:UnknownError:EUR:UK",
            "GameToken": "SimpleEngine:GameToken:BonusWin:UnknownError:EUR:UK",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "USD",
            "UserName": "Win:UnknownError",
            "UserId": "BonusWinUnknown7",
            "Country": "ErrorLand",
            "Age": 56,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        },  {
                "LaunchToken": "SimpleEngine:LaunchToken:Win:CurrencyDoesNotMatch:EUR:UK",
                "GameToken": "SimpleEngine:GameToken:Win:CurrencyDoesNotMatch:EUR:UK",
                "TotalBalance": config.defaultSettings.amount,
                "Currency": "USD",
                "UserName": "Win:CurrencyDoesNotMatch",
                "UserId": "WinCurrency8",
                "Country": "ErrorLand",
                "Age": 58,
                "Sex": EVERY_MATRIX_SEX.Female,
                "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
            "LaunchToken": "SimpleEngine:LaunchToken:BonusWin:CurrencyDoesNotMatch:EUR:UK",
            "GameToken": "SimpleEngine:GameToken:BonusWin:CurrencyDoesNotMatch:EUR:UK",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "USD",
            "UserName": "Win:CurrencyDoesNotMatch",
            "UserId": "BonusWinCurrency8",
            "Country": "ErrorLand",
            "Age": 58,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
                "LaunchToken": "SimpleEngine:LaunchToken:Cancel:UnknownError:EUR:UK",
                "GameToken": "SimpleEngine:GameToken:Cancel:UnknownError:EUR:UK",
                "TotalBalance": config.defaultSettings.amount,
                "Currency": "EUR",
                "UserName": "Cancel:UnknownError",
                "UserId": "CancelUnknown9",
                "Country": "ErrorLand",
                "Age": 37,
                "Sex": EVERY_MATRIX_SEX.Female,
                "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
                "LaunchToken": "SimpleEngine:LaunchToken:Cancel:CurrencyDoesNotMatch:EUR:UK",
                "GameToken": "SimpleEngine:GameToken:Cancel:CurrencyDoesNotMatch:EUR:UK",
                "TotalBalance": config.defaultSettings.amount,
                "Currency": "EUR",
                "UserName": "GetBalance:CurrencyDoesNotMatch",
                "UserId": "GetBalanceCurrency10",
                "Country": "ErrorLand",
                "Age": 38,
                "Sex": EVERY_MATRIX_SEX.Female,
                "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
            "LaunchToken": "SimpleEngine:LaunchToken:GetBalance:UnknownError:EUR:UK",
            "GameToken": "SimpleEngine:GameToken:GetBalance:UnknownError:EUR:UK",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "EUR",
            "UserName": "GetBalance:UnknownError",
            "UserId": "GetBalanceUnknown11",
            "Country": "ErrorLand",
            "Age": 37,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }, {
            "LaunchToken": "SimpleEngine:LaunchToken:GetBalance:CurrencyDoesNotMatch:EUR:UK",
            "GameToken": "SimpleEngine:GameToken:GetBalance:CurrencyDoesNotMatch:EUR:UK",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "EUR",
            "UserName": "GetBalance:CurrencyDoesNotMatch",
            "UserId": "GetBalanceCurrency12",
            "Country": "ErrorLand",
            "Age": 38,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        }
    ];

    private expiredPlayer: EveryMatrixPlayerInfo = {
        "LaunchToken": "SimpleEngine:LaunchToken:expired.token",
        "GameToken": "SimpleEngine:GameToken:expired.token",
        "TotalBalance": config.defaultSettings.amount,
        "Currency": "EUR",
        "UserName": "Expired",
        "UserId": "Expired13",
        "Country": "Poland",
        "Age": 46,
        "Sex": EVERY_MATRIX_SEX.Female,
        "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
    };

    private blockedPlayer: EveryMatrixPlayerInfo = {
            "LaunchToken": "SimpleEngine:LaunchToken:blocked.user",
            "GameToken": "SimpleEngine:GameToken:blocked.user",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "EUR",
            "UserName": "Blocked",
            "UserId": "Blocked13",
            "Country": "Ukraine",
            "Age": 26,
            "Sex": EVERY_MATRIX_SEX.Male,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        };

    private ipNotAllowedPlayer: EveryMatrixPlayerInfo = {
            "LaunchToken": "SimpleEngine:LaunchToken:ip.not.allowed",
            "GameToken": "SimpleEngine:GameToken:ip.not.allowed",
            "TotalBalance": config.defaultSettings.amount,
            "Currency": "EUR",
            "UserName": "IpNotAllowed",
            "UserId": "IpNotAllowed15",
            "Country": "England",
            "Age": 26,
            "Sex": EVERY_MATRIX_SEX.Female,
            "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
        };

    private insufficientFundsPlayer: EveryMatrixPlayerInfo = {
        "LaunchToken": "SimpleEngine:LaunchToken:insufficient.funds",
        "GameToken": "SimpleEngine:GameToken:insufficient.funds",
        "TotalBalance": 0,
        "Currency": "EUR",
        "UserName": "InsufficientFunds",
        "UserId": "InsufficientFunds16",
        "Country": "Russia",
        "Age": 26,
        "Sex": EVERY_MATRIX_SEX.Male,
        "Jurisdiction": EVERY_MATRIX_JURISDICTION.UK
    };

    private static toAuthResponse(player: EveryMatrixPlayerInfo, status: string): EMAuthenticateResponse {
        return {
            Token: player.GameToken,
            TotalBalance: player.TotalBalance.toString(),
            Currency: player.Currency,
            UserName: player.UserName,
            UserId: player.UserId,
            Country: player.Country,
            Age: player.Age.toString(),
            Sex: player.Sex,
            Jurisdiction: player.Jurisdiction,
            Status: status,
        };
    }

    public findPlayerByLaunchToken(launchToken: string): EveryMatrixPlayerInfo {
        return EvereMatrixSimpleEngineService.players
            .find(x => x.LaunchToken === launchToken);
    }

    public findPlayerByGameToken(launchToken: string): EveryMatrixPlayerInfo {
        if (launchToken === "SimpleEngine:GameToken:expired.token") {
            throw new TokenNotFound();
        }

        if (launchToken === "SimpleEngine:GameToken:blocked.user") {
            throw new UserIsBlocked();
        }

        if (launchToken === "SimpleEngine:GameToken:ip.not.allowed") {
            throw new IPIsNotAllowed();
        }

        if (launchToken === "SimpleEngine:GameToken:insufficient.funds") {
            throw new InsufficientFunds();
        }

        return EvereMatrixSimpleEngineService.players
            .find(x => x.GameToken === launchToken);
    }

    private checkInsufficientBalance(player: EveryMatrixPlayerInfo, bet: BetRequest) {
        if (player.TotalBalance < bet.Amount) {
            throw new InsufficientFunds();
        }
    }

    public authenticatePlayer(request: EMAuthenticateRequest): EMAuthenticateResponse | IEveryMatrixError {
        if (request.LaunchToken === "SimpleEngine:LaunchToken:expired.token") {
            return EvereMatrixSimpleEngineService.toAuthResponse(this.expiredPlayer, EVERY_MATRIX_STATUS.OK);
        }
        if (request.LaunchToken === "SimpleEngine:LaunchToken:blocked.user") {
            return EvereMatrixSimpleEngineService.toAuthResponse(this.blockedPlayer, EVERY_MATRIX_STATUS.OK);
        }
        if (request.LaunchToken === "SimpleEngine:LaunchToken:ip.not.allowed") {
            return EvereMatrixSimpleEngineService.toAuthResponse(this.ipNotAllowedPlayer, EVERY_MATRIX_STATUS.OK);
        }
        if (request.LaunchToken === "SimpleEngine:LaunchToken:insufficient.funds") {
            return EvereMatrixSimpleEngineService.toAuthResponse(this.insufficientFundsPlayer, EVERY_MATRIX_STATUS.OK);
        }

        const player = this.findPlayerByLaunchToken(request.LaunchToken);

        if (!player) {
            return new TokenNotFound();
        }

        return EvereMatrixSimpleEngineService.toAuthResponse(player, EVERY_MATRIX_STATUS.OK);
    }

    public bet(userId: string, request: BetRequest): BaseResponse {
        const player = this.findPlayerByGameToken(request.Token);

        this.checkInsufficientBalance(player, request);

        if (request.Token === "SimpleEngine:GameToken:Bet:UnknownError:EUR:UK" ||
            request.Token === "SimpleEngine:GameToken:Bet:Cancel:UnknownError:EUR:UK") {
            throw new UnknownError();
        }

        if (request.Token === "SimpleEngine:GameToken:Bet:CurrencyDoesNotMatch:EUR:UK") {
            throw new CurrencyDoesNotMatch();
        }

        if (!TransactionsHolder.checkIfBetAlreadyApplied(userId, request)) {
            player.TotalBalance -= (+request.Amount);
            TransactionsHolder.addBet(userId, request);
        }

        return {
            Status: EVERY_MATRIX_STATUS.OK,
            TotalBalance: player.TotalBalance.toString(),
            Currency: player.Currency
        };
    }

    public win(userId: string, request: WinRequest): BaseResponse {
        const player = this.findPlayerByGameToken(request.Token);

        if (request.Token === "SimpleEngine:GameToken:Win:UnknownError:EUR:UK") {
            throw new UnknownError();
        }

        if (request.Token === "SimpleEngine:GameToken:Win:CurrencyDoesNotMatch:EUR:UK") {
            throw new CurrencyDoesNotMatch();
        }

        if (!TransactionsHolder.checkIfWinAlreadyApplied(userId, request)) {
            player.TotalBalance += (+request.Amount);
            TransactionsHolder.addWin(userId, request);
        }

        return {
            Status: EVERY_MATRIX_STATUS.OK,
            TotalBalance: player.TotalBalance.toString(),
            Currency: player.Currency
        };
    }

    public roundInfo(userId: string, request: EMRoundInfoRequest): EMRoundInfoResponse {
        return {
            Status: "Ok"
        };
    }

    public bonusWin(userId: string, request: BonusWinRequest): BaseResponse {
        const player = this.findPlayerByGameToken(request.Token);

        if (request.Token === "SimpleEngine:GameToken:BonusWin:UnknownError:EUR:UK") {
            throw new UnknownError();
        }

        if (request.Token === "SimpleEngine:GameToken:BonusWin:CurrencyDoesNotMatch:EUR:UK") {
            throw new CurrencyDoesNotMatch();
        }

        if (!TransactionsHolder.checkIfWinAlreadyApplied(userId, request)) {
            player.TotalBalance += (+request.Amount);
            TransactionsHolder.addWin(userId, request);
        }

        return {
            Status: EVERY_MATRIX_STATUS.OK,
            TotalBalance: player.TotalBalance.toString(),
            Currency: player.Currency
        };
    }

    public cancel(userId: string, request: CancelBetRequest): BaseResponse {
        const player = this.findPlayerByGameToken(request.Token);

        if (request.Token === "SimpleEngine:GameToken:Cancel:UnknownError:EUR:UK" ||
            request.Token === "SimpleEngine:GameToken:Bet:Cancel:UnknownError:EUR:UK") {
            throw new UnknownError();
        }

        if (request.Token === "SimpleEngine:GameToken:Cancel:CurrencyDoesNotMatch:EUR:UK") {
            throw new CurrencyDoesNotMatch();
        }

        if (!TransactionsHolder.checkIfCancelBetAlreadyApplied(userId, request)) {

            const winToCancel = TransactionsHolder.findWin(userId, request.CanceledExternalId);

            if (winToCancel) {
                player.TotalBalance -= (+winToCancel.Amount);
            }

            const betToCancel = TransactionsHolder.findBet(userId, request.CanceledExternalId);

            if (betToCancel) {
                player.TotalBalance += (+betToCancel.Amount);
            }

            TransactionsHolder.addCancelBet(userId, request);
        }

        return {
            Status: EVERY_MATRIX_STATUS.OK,
            TotalBalance: player.TotalBalance.toString(),
            Currency: player.Currency
        };
    }

    public getBalance(request: BalanceRequest): BaseResponse {
        const player = this.findPlayerByGameToken(request.Token);

        if (request.Token === "SimpleEngine:GameToken:GetBalance:UnknownError:EUR:UK") {
            throw new UnknownError();
        }

        if (request.Token === "SimpleEngine:GameToken:GetBalance:CurrencyDoesNotMatch:EUR:UK") {
            throw new CurrencyDoesNotMatch();
        }

        return {
            Status: EVERY_MATRIX_STATUS.OK,
            TotalBalance: player.TotalBalance.toString(),
            Currency: player.Currency
        };
    }

    public clearPlayerStatistic(request: any) {
        const player = this.findPlayerByGameToken(request.Token);
        if (player) {
            player.TotalBalance = config.defaultSettings.amount;
            TransactionsHolder.clearPlayerHistory(player.UserId);
            return {
                Status: EVERY_MATRIX_STATUS.OK,
                Message: "Player statistic reset"
            };
        } else {
            return {
                Status: EVERY_MATRIX_STATUS.Failed,
                Message: "Player not found"
            };
        }
    }

    public getPlayerHistory(request: any) {
        const player = this.findPlayerByGameToken(request.Token);
        if (player) {
            player.TotalBalance = config.defaultSettings.amount;
            return TransactionsHolder.getPlayerHistory(player.UserId);
        } else {
            return {
                Status: EVERY_MATRIX_STATUS.Failed,
                Message: "Player not found"
            };
        }
    }
}
