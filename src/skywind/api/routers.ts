import { Application, NextFunction, Request, Response } from "express";
import merchant from "./merchant-management";
import settingsRoutes from "./settings";
import ipmApi from "./ipm";
import popApi from "./pop";
import relaxApi from "./relax";
import pokerStarApi from "./pokerStar";
import everyMatrixApi from "../integrations/everyMatrix/api";
import mrGreenApi from "../integrations/mrGreen/api";
import betVictorApi from "../integrations/betVictor/api";
import pariplayAPI from "./pariplay";
import gvcApi from "./gvc";
import sbtechApi from "./sbtech";
import ganApi from "./gan";
import sisalApi from "../sisal/sisal.controllers";
import isbApi from "../integrations/ISB/api";
import softSwissApi from "../integrations/softSwiss/api";

import version from "./version";
import health from "./health";
import config from "../config";
import { emulateLatency } from "../utils/helper";
import { createErrorH<PERSON><PERSON>, createErrorHandlerForManagement } from "./config/errorMiddleware";
import { requestLogging } from "./config/middleware";
import logger from "../utils/logger";
import { logging } from "@skywind-group/sw-utils";
import { INTEGRATION_TYPE } from "../../config/integrationType";
import { INTEGRATION_VERSION } from "../../config/integrationVersion";
import { getSwagger, loadJson, setupSwagger } from "../utils/swagger";

const bodyParser = require("body-parser");

import Logger = logging.Logger;

const routesLog = logger("routes");

function setAPI(app: Application, appName: string) {
    switch (appName) {
        case INTEGRATION_TYPE.BET_VICTOR:
            return app.use("/v3", betVictorApi);
        case INTEGRATION_TYPE.MR_GREEN:
            return app.use("/nyx/adapters/externalGameAdapter", mrGreenApi);
        case INTEGRATION_TYPE.EVERY_MATRIX:
            return app.use("/v10/Skywind", everyMatrixApi);
        case INTEGRATION_TYPE.POKER_STAR:
            return app.use("/api/xml", pokerStarApi);
        case INTEGRATION_TYPE.RELAX:
            return app.use(`/p2p/${INTEGRATION_VERSION.RELAX}`, relaxApi);
        case INTEGRATION_TYPE.PARIPLAY:
            return app.use("/EVI", pariplayAPI);
        case INTEGRATION_TYPE.POP:
            return app.use("/api", popApi);
        case INTEGRATION_TYPE.GVC:
            return app.use("/api", gvcApi);
        case INTEGRATION_TYPE.SBTECH:
            return app.use("/api", sbtechApi);
        case INTEGRATION_TYPE.GAN:
            app.use("/tpgi/*", bodyParser.text({ type: "*/*" }));
            return app.use("/tpgi", ganApi);
        case INTEGRATION_TYPE.SISAL:
            return app.use("/atena", sisalApi);
        case INTEGRATION_TYPE.ISB:
            return app.use("/rest/service", isbApi);
        case INTEGRATION_TYPE.SOFT_SWISS:
            return app.use("/", softSwissApi);
        default:
            return app.use("/api", ipmApi);
    }
}

async function loadSwagger(appName: string): Promise<Record<string, any>> {
    switch (appName) {
        case INTEGRATION_TYPE.SOFT_SWISS:
            return await loadJson("swagger-softswiss.json");
        default:
            return {};
    }
}

export async function defineRoutes(app: Application,
                                   appName: INTEGRATION_TYPE = INTEGRATION_TYPE.IPM,
                                   integrationLog: Logger = null) {
    routesLog.info({
        mockLatency: config.mockLatency,
        mockLatencyPathMask: config.mockLatencyPathMask,
        mockLatencySpreading: config.mockLatencySpreading
    }, "Mock latency settings");
    if (Number.isFinite(config.mockLatency)) {
        app.use(config.mockLatencyPathMask, emulateLatency);
    }

    const log = integrationLog || routesLog;

    app.use(requestLogging(log));
    app.use("/v1/*", addHeaderCORS);
    app.use("/v1", version);
    app.use("/v1", health);
    app.use("/v1", settingsRoutes);
    setAPI(app, appName);
    app.use(createErrorHandler(log, appName));

    const swaggerApi = await loadSwagger(appName);
    const swaggerJson = await getSwagger((doc) => updateSwaggerDocument(appName, doc, swaggerApi));
    setupSwagger(app, swaggerJson);

    app.use("/v1", merchant);
    app.use(createErrorHandlerForManagement(log));
}

function addHeaderCORS(req: Request, res: Response, next: NextFunction) {
    if (res.headersSent) {
        next();
        return;
    }
    res.setHeader("Access-Control-Allow-Origin", "*");
    next();
}

export function updateSwaggerDocument(
    appName: string,
    swagger: { info: { title: string, description: string }, paths: Record<string, any> },
    api?: Record<string, any>
) {
    return {
        ...swagger,
        info: {
            ...swagger.info,
            title: `${appName.toUpperCase()} - ${swagger.info.title}`,
            description: `${appName.toUpperCase()} - ${swagger.info.description}`
        },
        paths: {
            ...swagger.paths,
            ...api,
        }
    };
}
