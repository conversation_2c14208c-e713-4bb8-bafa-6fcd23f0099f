import { suite, test } from "mocha-typescript";
const request = require("supertest");
import { expect } from "chai";
import { BalanceRequest } from "../../skywind/entities/relax";
import config from "../../skywind/config";
import { RelaxBaseSpec } from "./base.spec";
import { INTEGRATION_VERSION } from "../../config/integrationVersion";

@suite
class RelaxGetBalanceSpec extends RelaxBaseSpec {
    @test
    public async getBalanceAuthFail() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/getbalance`)
            .auth("invalid", "invalid")
            .send({})
            .expect(401);

        expect(response.body.errorcode).to.be.equal("UNAUTHORIZED");
    }

    @test
    public async getBalanceMerchantNotFound() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/11/getbalance`)
            .auth(config.auth.username, config.auth.password)
            .send({})
            .expect(500);

        expect(response.body.errorcode).to.be.equal("UNHANDLED");
    }

    @test
    public async getBalanceWithoutRequiredFields() {
        const balanceRequest = {
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "sessionid": 10355
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/getbalance`)
            .auth(config.auth.username, config.auth.password)
            .send(balanceRequest)
            .expect(400);

        expect(response.body.errorcode).to.be.equal("INVALID_PARAMETERS");
    }

    @test
    public async getBalancePlayerNotFound() {
        const balanceRequest = {
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "playerid": 1231203,
            "sessionid": this.sessionId
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/getbalance`)
            .auth(config.auth.username, config.auth.password)
            .send(balanceRequest)
            .expect(403);

        expect(response.body.errorcode).to.be.equal("SESSION_EXPIRED");
    }

    @test
    public async getBalanceSessionNotFound() {
        const balanceRequest = {
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "playerid": this.playerCode,
            "sessionid": "invalidSession"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/getbalance`)
            .auth(config.auth.username, config.auth.password)
            .send(balanceRequest)
            .expect(403);

        expect(response.body.errorcode).to.be.equal("SESSION_EXPIRED");
    }

    @test
    public async getBalanceSuccess() {
        const balanceRequest: BalanceRequest = {
            "currency": "EUR",
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "sessionid": this.sessionId,
            "playerid": this.playerCode
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/getbalance`)
            .auth(config.auth.username, config.auth.password)
            .send(balanceRequest)
            .expect(200);

        expect(response.body.balance).to.be.equal(config.defaultSettings.amount);
        expect(response.body.currency).to.be.equal("USD");
        expect(response.body.sessionid).to.be.equal(this.sessionId);
    }
}
