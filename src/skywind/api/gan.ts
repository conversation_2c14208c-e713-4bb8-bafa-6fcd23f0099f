import { NextFunction, Request, Response, Router } from "express";
import logger from "../utils/logger";
import { getGANService } from "../service/gan/service";
// import * as responder from "../service/gan/responder";
import {
    GANExtraRequestData,
    GANEnsureContestForPlayerRequest,
    GANEnsureContestForPlayerResponse,
    GANGetPlayerInfoRequest,
    GANGetPlayerInfoResponse,
    GANPaymentRequest,
    GANPaymentResponse,
    GANBetPaymentRequestAction,
    GANWinPaymentRequestAction,
    GANRollbackPaymentRequestAction,
    GAN_PAYMENT_TYPE
} from "../entities/gan";
import {
    authenticate,
    wrapApiControllers,
    validate,
    parseXml,
    convertToXml
} from "./config/ganMiddleware";
import { settings } from "../models/settings";
import { isEmpty } from "../utils/isEmpty";
import { INTEGRATION_TYPE } from "../../config/integrationType";
import * as Merchants from "../models/merchant";
import * as GANErrors from "../errorsGAN";

const bodyParser = require("body-parser");
const log = logger(`${INTEGRATION_TYPE.GAN}:api`);
const router: Router = Router();

router.post("/*", parseXml);

router.post("/ensureContestForPlayer",
    wrapApiControllers([
        validate(),
        authenticate,
        // notSaveAnyDataMiddleware(responder.getBalance),
        ensureContestForPlayer
    ]));

router.post("/findPlayer",
    wrapApiControllers([
        validate(),
        authenticate,
        // notSaveAnyDataMiddleware(responder.getBalance),
        getPlayerInfo
    ]));

router.post("/submitPlayAction",
    wrapApiControllers([
        validate(checkPaymentOperation),
        authenticate,
        // notSaveAnyDataMiddleware(responder.getBalance),
        submitPlayAction
    ]));

/**
 * MEMO: API controllers
 */

async function ensureContestForPlayer(req: Request & GANExtraRequestData, res: Response, next: NextFunction) {
    const data = req.parsedRequest as GANEnsureContestForPlayerRequest;
    const response: GANEnsureContestForPlayerResponse = await getGANService(req.merchant, req.customer)
        .ensureContestForPlayer(data);

    res.send(convertToXml(response));
}

async function getPlayerInfo(req: Request & GANExtraRequestData, res: Response, next: NextFunction) {
    const data = req.parsedRequest as GANGetPlayerInfoRequest;
    const response: GANGetPlayerInfoResponse = await getGANService(req.merchant, req.customer).getPlayerInfo(data);

    res.send(convertToXml(response));
}

async function submitPlayAction(req: Request & GANExtraRequestData, res: Response, next: NextFunction) {
    const requestBody = req.parsedRequest as GANPaymentRequest;
    let response: GANPaymentResponse;

    if (requestBody.Play.Action._void) {
        response = await getGANService(req.merchant, req.customer)
            .rollback(requestBody as GANPaymentRequest<GANRollbackPaymentRequestAction>);

    } else if (requestBody.Play.Action._type === GAN_PAYMENT_TYPE.BET) {
        response = await getGANService(req.merchant, req.customer)
            .debit(requestBody as GANPaymentRequest<GANBetPaymentRequestAction>);

    } else if (requestBody.Play.Action._type === GAN_PAYMENT_TYPE.WIN) {
        response = await getGANService(req.merchant, req.customer)
            .credit(requestBody as GANPaymentRequest<GANWinPaymentRequestAction>);

    } else {
        throw new GANErrors.InvalidActionType();
    }

    res.send(convertToXml(response));
}

/*

async function endRound(req: Request & GANExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SBTechEndRoundRequest;
    const response: SBTechEndRoundResponse = getGANService(req.merchant, req.customer).endRound(data);

    res.send(response);
}*/

/**
 * MEMO: Auxiliary methods
 */

function checkPaymentOperation(requestBody: GANPaymentRequest) {
    if (!requestBody.Play._playRef) {
        throw new GANErrors.MissingPlayRef();
    }
    if (!requestBody.Play._contestRef) {
        throw new GANErrors.MissingContestRefForPlay();
    }
    if (!requestBody.Play._playerGuid) {
        throw new GANErrors.MissingPlayerGUIDForPlay();
    }
    if (!requestBody.Play.Action) {
        throw new GANErrors.NoActionSpecified();
    }
    if (!requestBody.Play.Action._ref) {
        throw new GANErrors.MissingActionRef();
    }

    // unless action is rollback
    if (!requestBody.Play.Action._void) {
        if (!requestBody.Play.Action._type) {
            throw new GANErrors.MissingActionType();
        }
        if (!requestBody.Play.Action._amount) {
            throw new GANErrors.MissingActionAmount();
        }
        if (!requestBody.Play.Action._currency) {
            throw new GANErrors.MissingCurrency();
        }
    }
}

function notSaveAnyDataMiddleware(responder: (req: Request & GANExtraRequestData) => object) {
    return (req: Request & GANExtraRequestData, res: Response, next: NextFunction) => {
        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req));
            } catch (err) {
                return next(err);
            }
        }
        return next();
    };
}

export default router;
