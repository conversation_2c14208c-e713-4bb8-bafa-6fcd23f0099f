import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");

import { SisalBaseSpec } from "./base.spec";
import { createTicket } from "../../skywind/service/ticket";
import {
    SisalBalanceResponse
} from "../../skywind/sisal/sisal.entities";
import { Ticket } from "../../skywind/models/ticket";

@suite("Sisal.balance")
class SisalBalanceSpec extends SisalBaseSpec {

    @test("should return player's balance")
    public async getBalance() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse: SisalBalanceResponse = {
            "balance": 10000,
            bonusBalance: 0,
            playerId: this.fixtures.playerCode
        };

        const actualResponse = await request(this.server)
            .get(this.urlPathToSessionBalance(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .expect(200);

        expect(actualResponse.body).deep.equal(expectedResponse);
    }
}
