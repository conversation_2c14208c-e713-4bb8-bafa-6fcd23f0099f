# Seamless Wallet + IMP-Mock Integration Test Scripts

This directory contains comprehensive Hurl test scripts that demonstrate and validate the complete game flow using seamless wallet integration and IMP-mock service.

## Overview

The test scripts cover:
- Complete game flow from user registration to gameplay
- Seamless wallet integration with transaction handling
- IMP-mock service interactions for external dependencies
- Error scenarios and edge cases
- Bonus and promotional features
- Multi-balance handling (main, bonus, free bet)

## Prerequisites

1. **Hurl Installation**: Install Hurl from https://hurl.dev/
2. **Local Services**: Ensure all required services are running:
   - Management API (port 3000)
   - IMP-Mock Service (port 8000)
   - Terminal Service (port 3004)
   - Wallet Service (port 3005)
   - Game Provider Service (port 3006)
   - Game Auth Service (port 3007)

3. **Environment Setup**: Configure environment variables in the appropriate `.env` file

## Test Scripts

### 1. `seamless-wallet-game-flow.hurl`
**Purpose**: Complete happy path game flow demonstration

**Flow**:
1. Management API authentication
2. Terminal token generation
3. IMP-Mock merchant setup
4. Customer creation with balance
5. Player login via terminal
6. Seamless wallet initialization
7. Game URL generation
8. Game session management
9. Bet placement and win processing
10. Session keep-alive
11. Balance verification
12. Player logout

**Usage**:
```bash
hurl --variables-file environments/local.env seamless-wallet-game-flow.hurl
```

### 2. `seamless-wallet-error-scenarios.hurl`
**Purpose**: Error handling and edge case validation

**Scenarios**:
- Invalid merchant configurations
- Duplicate merchant/customer creation
- Insufficient balance handling
- Expired ticket scenarios
- Invalid authentication tokens
- Currency mismatches
- Session timeout simulation
- Transaction duplicates

**Usage**:
```bash
hurl --variables-file environments/local.env seamless-wallet-error-scenarios.hurl
```

### 3. `seamless-wallet-bonus-flow.hurl`
**Purpose**: Bonus and promotional features testing

**Features**:
- Multi-balance structure (main, bonus, free bet)
- Bonus bet placement and wins
- Free bet handling
- Balance transfers between types
- Transaction rollbacks
- Promotional merchant setup

**Usage**:
```bash
hurl --variables-file environments/local.env seamless-wallet-bonus-flow.hurl
```

## Environment Configurations

### Local Development (`environments/local.env`)
- Configured for localhost services
- Default ports and credentials
- Debug logging enabled
- Mock services enabled

### Staging (`environments/staging.env`)
- Configured for staging environment
- HTTPS endpoints
- Production-like settings
- External service integrations

## Running Tests

### Single Script
```bash
# Run main game flow
hurl --variables-file environments/local.env seamless-wallet-game-flow.hurl

# Run with verbose output
hurl --variables-file environments/local.env --verbose seamless-wallet-game-flow.hurl

# Run with specific game code
hurl --variables-file environments/local.env --variable GAME_CODE=sw_ss seamless-wallet-game-flow.hurl
```

### All Scripts
```bash
# Run all test scripts
for script in *.hurl; do
    echo "Running $script..."
    hurl --variables-file environments/local.env "$script"
done
```

### Staging Environment
```bash
# Run against staging
hurl --variables-file environments/staging.env seamless-wallet-game-flow.hurl
```

## Key Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MAPI_URL` | Management API URL | `http://localhost:3000` |
| `MOCK_URL` | IMP-Mock Service URL | `http://localhost:8000` |
| `TERMINAL_URL` | Terminal Service URL | `http://localhost:3004` |
| `WALLET_URL` | Wallet Service URL | `http://localhost:3005` |
| `MAPI_PASSWORD` | Management API password | Required |
| `GAME_CODE` | Game to test | `sw_mrmnky` |
| `DEFAULT_CURRENCY` | Currency for tests | `USD` |
| `TEST_PLAYER_BALANCE` | Initial player balance | `100000` |

## Test Data Management

### Merchants Created
- `seamless_test_merchant`: Main test merchant
- `promo_merchant`: Promotional features merchant
- `test_merchant_dup`: Error scenario merchant

### Players Created
- `test_player_001`: Main test player
- `bonus_player_001`: Bonus features player
- `poor_player`: Insufficient balance player
- `blocked_player`: Blocked status player
- `limited_player`: Limited balance player

### Game Codes Used
- `sw_mrmnky`: Primary test game (Monkey)
- `sw_ss`: Alternative test game (Super Spin)
- `invalid_game_code`: Error testing

## Assertions and Validations

The scripts include comprehensive assertions for:
- HTTP status codes
- Response structure validation
- Balance calculations
- Transaction integrity
- Session management
- Error message verification
- Token format validation

## Troubleshooting

### Common Issues

1. **Service Not Running**
   ```
   Error: Connection refused
   ```
   Solution: Ensure all required services are running on expected ports

2. **Authentication Failure**
   ```
   HTTP 401 Unauthorized
   ```
   Solution: Check `MAPI_PASSWORD` and other credentials in environment file

3. **Invalid Game Code**
   ```
   HTTP 404 Game not found
   ```
   Solution: Verify game exists in system or use default `sw_mrmnky`

4. **Balance Mismatch**
   ```
   Assertion failed: balance calculation
   ```
   Solution: Check if previous test data affects current test

### Debug Mode
```bash
# Run with maximum verbosity
hurl --variables-file environments/local.env --verbose --very-verbose seamless-wallet-game-flow.hurl

# Save output to file
hurl --variables-file environments/local.env seamless-wallet-game-flow.hurl > test-output.log 2>&1
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run Seamless Wallet Tests
  run: |
    hurl --variables-file scripts/hurl/environments/staging.env \
         scripts/hurl/seamless-wallet-game-flow.hurl
```

### Jenkins Pipeline Example
```groovy
stage('API Tests') {
    steps {
        sh 'hurl --variables-file scripts/hurl/environments/staging.env scripts/hurl/*.hurl'
    }
}
```

## Contributing

When adding new test scenarios:
1. Follow existing naming conventions
2. Include comprehensive assertions
3. Add appropriate error handling
4. Update this README with new scenarios
5. Test against both local and staging environments

## Support

For issues or questions:
1. Check service logs for detailed error information
2. Verify environment configuration
3. Ensure all prerequisites are met
4. Review assertion failures for specific validation issues
