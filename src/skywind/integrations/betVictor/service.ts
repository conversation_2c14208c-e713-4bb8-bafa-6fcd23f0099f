import { Request } from "express";
import { Customer, Merchant } from "../../models/merchant";
import { Ticket, tickets } from "../../models/ticket";
import {
    BET_VICTOR_RETURN_CODE,
    BET_VICTOR_RETURN_CODE_DESCRIPTION,
    BetVictorError,
    InvalidRollbackRequest,
    SessionNotFound
} from "./errors";
import {
    AccountResponse,
    AuthenticateRequest,
    AuthenticateResponse,
    BasePaymentRequest,
    BasePaymentResponse,
    BaseRequest,
    BET_VICTOR_ACTION, PlaceSettleFreeBetRequest, SettleBet, SettlePlaceBetRequest, UnsettledBetRequest
} from "./entities";
import { throwCustomError } from "../../service/customError";
import { extraDataHandler } from "../../service/extraData";

import * as Transaction from "../../models/transaction";

import { RequestAdditionalInfo } from "./middleware";
import { WALLET_ACTION } from "../../models/transaction";
import * as RoundModel from "../../models/round";

export interface BetVictorWalletApi {
    authenticate(request: AuthenticateRequest): Promise<AuthenticateResponse>;
    getAccountDetails(request: BaseRequest): Promise<AccountResponse>;
    placeBet(request: UnsettledBetRequest): Promise<BasePaymentResponse>;
    settleBet(request: SettleBet): Promise<BasePaymentResponse>;
    rollbackBet(request: BasePaymentRequest): Promise<BasePaymentResponse>;
    additionalWinnings(request: SettleBet): Promise<BasePaymentResponse>;
    promoCredit(request: SettleBet): Promise<BasePaymentResponse>;
}

export class BetVictorService implements BetVictorWalletApi {
    constructor(public merchant: Merchant, public customer: Customer,
                private req: Request & RequestAdditionalInfo) {
    }

    public static authorizePlayer(gameToken: string): Ticket {
        const ticket = tickets[gameToken];
        if (!ticket) {
            throw new SessionNotFound();
        }
        return ticket;
    }

    private toFixedPrecise(number: number) {
        return Number((number).toFixed(5));
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.AUTHENTICATE })
    @extraDataHandler({ action: BET_VICTOR_ACTION.AUTHENTICATE })
    public async authenticate(request: AuthenticateRequest): Promise<AuthenticateResponse> {
        const response: AuthenticateResponse =  {
            responseCode: 0,
            sessionId: request.authToken
        };
        if (this.customer.bet_limit) {
            response.maxStake = +this.customer.bet_limit;
        }
        return response;
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.GET_BALANCE })
    @extraDataHandler({ action: BET_VICTOR_ACTION.GET_BALANCE })
    public async getAccountDetails(request: BaseRequest): Promise<AccountResponse> {
        return {
            accountNumber: this.req.ticket.custId,
            country: this.req.customer.country,
            currency: this.req.customer.currency_code,
            responseCode: 0,
            firstName: this.req.customer.first_name,
            lastName: this.req.customer.last_name,
            balance: this.toFixedPrecise(this.req.customer.balance.amount)
        };
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.BET })
    @extraDataHandler({ action: BET_VICTOR_ACTION.BET })
    public async placeBet(request: SettlePlaceBetRequest): Promise<BasePaymentResponse> {
        const action = WALLET_ACTION.debit;
        const transactionId = request.clientTransactionId;
        const transaction = Transaction.getById(transactionId, action);
        RoundModel.createOrUpdate({
            merchantId: this.merchant.merch_id,
            customerId: this.customer.cust_id,
            gameId: request.clientGamePlayId
        }, action, request.betAmount);

        if (!transaction) {
            Transaction.setById(transactionId, action,
                Math.abs(request.betAmount),
                this.req.customer.cust_id);
            this.req.customer.balance.amount = this.req.customer.balance.amount + Math.abs(request.betAmount) * action;
            if (request.settle) {
                Transaction.setById(transactionId, action,
                    Math.abs(request.settle.winnings), this.req.customer.cust_id);
                this.req.customer.balance.amount =
                    this.req.customer.balance.amount + Math.abs(request.settle.winnings);
            }
        }

        return {
            responseCode: 0,
            duplicate: (!!transaction),
            transactionId: transactionId,
            balance: this.toFixedPrecise(this.req.customer.balance.amount)
        };
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.BET })
    @extraDataHandler({ action: BET_VICTOR_ACTION.BET })
    public async placeFreeBet(request: PlaceSettleFreeBetRequest): Promise<BasePaymentResponse> {
        const action = WALLET_ACTION.credit;
        const transactionId = request.clientTransactionId;
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action,
                0,
                this.req.customer.cust_id);
            Transaction.setById(transactionId, action, Math.abs(request.settle.winnings), this.req.customer.cust_id);
            this.req.customer.balance.amount =
                this.req.customer.balance.amount + Math.abs(request.settle.winnings) * action;
        }

        return {
            responseCode: 0,
            duplicate: (!!transaction),
            transactionId: transactionId,
            balance: this.toFixedPrecise(this.req.customer.balance.amount)
        };
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.ADDITIONAL_BET })
    @extraDataHandler({ action: BET_VICTOR_ACTION.ADDITIONAL_BET })
    public async additionalBet(request: UnsettledBetRequest): Promise<BasePaymentResponse> {
        const action = WALLET_ACTION.debit;
        const transactionId = request.clientTransactionId;
        const transaction = Transaction.getById(transactionId, action);
        const round = RoundModel.findOne(
            this.merchant.merch_id,
            this.customer.cust_id,
            request.clientGamePlayId
        );
        if (!round) {
            throw new BetVictorError(
                BET_VICTOR_RETURN_CODE.INVALID_GAME_PLAY_ID,
                BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_GAME_PLAY_ID
            );
        }

        if (!transaction) {
            Transaction.setById(transactionId, action,
                Math.abs(request.betAmount),
                this.req.customer.cust_id);
            this.req.customer.balance.amount = this.req.customer.balance.amount + Math.abs(request.betAmount) * action;
        }

        return {
            responseCode: 0,
            duplicate: (!!transaction),
            transactionId: transactionId,
            balance: this.toFixedPrecise(this.req.customer.balance.amount)
        };
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.CANCEL })
    @extraDataHandler({ action: BET_VICTOR_ACTION.CANCEL })
    public async rollbackBet(request: BasePaymentRequest): Promise<BasePaymentResponse> {
        const transactionId = request.clientTransactionId;

        const rollbackTransaction = Transaction.getById(transactionId, WALLET_ACTION.rollback);

        if (!rollbackTransaction) {
            const originalTransaction = Transaction.getById(transactionId, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new InvalidRollbackRequest();
            }

            const [, amount, , isRollback] = originalTransaction;

            if (!isRollback) {
                Transaction.rollbackById(transactionId, WALLET_ACTION.debit);
                this.req.customer.balance.amount = this.req.customer.balance.amount - amount;
            }

            Transaction.setById(transactionId, WALLET_ACTION.rollback, 0, this.req.customer.cust_id);

            return {
                responseCode: 0,
                duplicate: isRollback,
                transactionId: transactionId,
                balance: this.toFixedPrecise(this.req.customer.balance.amount)
            };
        } else {
            return {
                responseCode: 0,
                duplicate: false,
                transactionId: transactionId,
                balance: this.toFixedPrecise(this.req.customer.balance.amount)
            };
        }
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.WIN })
    @extraDataHandler({ action: BET_VICTOR_ACTION.WIN })
    public async settleBet(request: SettleBet): Promise<BasePaymentResponse> {
        const action = WALLET_ACTION.credit;
        const transactionId = request.clientTransactionId;
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action, Math.abs(request.winnings), this.req.customer.cust_id);
            this.req.customer.balance.amount = this.req.customer.balance.amount + Math.abs(request.winnings) * action;
        }
        const round = RoundModel.findOne(
            this.merchant.merch_id,
            this.customer.cust_id,
            request.clientGamePlayId
        );
        if (!round) {
            throw new BetVictorError(
                BET_VICTOR_RETURN_CODE.INVALID_GAME_PLAY_ID,
                BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_GAME_PLAY_ID
            );
        }
        return {
            responseCode: 0,
            duplicate: (!!transaction),
            transactionId: transactionId,
            balance: this.toFixedPrecise(this.req.customer.balance.amount)
        };
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.ADDITIONAL_WINNINGS })
    @extraDataHandler({ action: BET_VICTOR_ACTION.ADDITIONAL_WINNINGS })
    public async additionalWinnings(request: SettleBet): Promise<BasePaymentResponse> {
        const action = WALLET_ACTION.credit;
        const transactionId = request.clientTransactionId;
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action, Math.abs(request.winnings), this.req.customer.cust_id);
            this.req.customer.balance.amount = this.req.customer.balance.amount + Math.abs(request.winnings) * action;
        }

        return {
            responseCode: 0,
            duplicate: (!!transaction),
            transactionId: transactionId,
            balance: this.toFixedPrecise(this.req.customer.balance.amount)
        };
    }

    @throwCustomError({ action: BET_VICTOR_ACTION.PROMO_CREDIT })
    @extraDataHandler({ action: BET_VICTOR_ACTION.PROMO_CREDIT })
    public async promoCredit(request: SettleBet): Promise<BasePaymentResponse> {
        const action = WALLET_ACTION.credit;
        const transactionId = request.clientTransactionId;
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action, Math.abs(request.winnings), this.req.customer.cust_id);
            this.req.customer.balance.amount = this.req.customer.balance.amount + Math.abs(request.winnings) * action;
        }

        return {
            responseCode: 0,
            duplicate: (!!transaction),
            transactionId: transactionId,
            balance: this.toFixedPrecise(this.req.customer.balance.amount)
        };
    }
}
