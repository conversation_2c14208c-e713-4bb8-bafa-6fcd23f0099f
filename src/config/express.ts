import { NextFunction, Request, Response } from "express";

const compression = require("compression");
const bodyParser = require("body-parser");
const methodOverride = require("method-override");
const cookieParser = require("cookie-parser");
import * as express from "express";

export interface ApplicationSettings {
    skipMiddlewareWithPath?: string;
}

export default function create(settings: ApplicationSettings = { skipMiddlewareWithPath: "" }): express.Application {

    const app: express.Application = express();

    app.use(cookieParser());
    app.use(compression({}));
    app.use(unless(settings.skipMiddlewareWithPath, bodyParser.urlencoded({ extended: true })));
    app.use(unless(settings.skipMiddlewareWithPath, bodyParser.json()));
    app.use(methodOverride());

    return app;
}

export function unless(path: any, controller: (req: Request, res: Response, next: NextFunction) => any) {
    const regexp = new RegExp(path);

    return function(req: Request, res: Response, next: NextFunction) {
        if (path && regexp.test(req.path)) {
            return next();
        }
        return controller(req, res, next);
    };
};
