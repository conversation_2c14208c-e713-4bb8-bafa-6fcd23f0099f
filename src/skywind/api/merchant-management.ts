import { Router, Request, Response, NextFunction } from "express";
import { getParamValues, sumMajorUnits } from "../utils/helper";
import * as Merchants from "../models/merchant";
import { createTicket } from "../service/ticket";
import * as MerchantService from "../service/merchant";
import * as CustomerService from "../service/customer";
import { routerCallbackWrapper } from "./config/middleware";
import { InvalidRequest } from "../errors";
import * as CustomErrorModel from "../models/customError";
import * as ExtraDataModel from "../models/extraData";
import { getAndValidateAction, getAndValidateRaiseType } from "../service/customError";
import { RaiseType } from "../models/customError";
import { getRegisterRoundValidators } from "./config/ipmValidator";
import { setRegisterRound } from "../models/round";
import { body } from "express-validator/check";

const router: Router = Router();

function getMerchants(req: Request, res: Response) {
    res.send(Merchants.getAll());
}
router.get("/merchant/", routerCallbackWrapper(getMerchants));

function getMerchant(req: Request, res: Response) {
    res.send(Merchants.getById(req.params.merch_id, true));
}
router.get("/merchant/:merch_id", routerCallbackWrapper(getMerchant));

function updateMerchant(req: Request, res: Response) {
    const merchant = MerchantService.getMerchant(req);
    res.send(MerchantService.updateMerchant(merchant.merch_id, req.body));
}
router.patch("/merchant/:merch_id", routerCallbackWrapper(updateMerchant));

function createMerchant(req: Request, res: Response) {
    const [ merchantId ] = getParamValues(req, ["merch_id", "merch_pwd"]);
    res.status(201).send(MerchantService.createMerchant(merchantId, req.body));
}
router.post("/merchant/", routerCallbackWrapper(createMerchant));

function createCustomer(req: Request, res: Response) {
    const merchant = MerchantService.getMerchant(req);
    getParamValues(req, ["currency_code", "cust_id"]);
    const createdCustomer = CustomerService.createCustomer(merchant, req.body);

    res.status(201).send(createdCustomer);
}
router.post("/merchant/:merch_id/customer", routerCallbackWrapper(createCustomer));

function updateCustomer(req: Request, res: Response) {
    const merchant = MerchantService.getMerchant(req);
    const customer = CustomerService.getMerchantCustomer(req);
    res.send(CustomerService.updateCustomer(merchant, customer, req.body));
}
router.patch("/merchant/:merch_id/customer/:cust_id", routerCallbackWrapper(updateCustomer));

router.get("/merchant/:merch_id/customer/:cust_id",
    routerCallbackWrapper((req: Request, res: Response) => res.send(CustomerService.getMerchantCustomer(req)))
);

function updateCustomerBalance(req: Request, res: Response, next: NextFunction) {
    const customer = CustomerService.getMerchantCustomer(req);

    const amount = +(req.params.amount);
    customer.balance.amount = sumMajorUnits(customer.balance.amount, amount);
    res.send(customer);
}
router.post("/merchant/:merch_id/customer/:cust_id/balance/:amount", routerCallbackWrapper(updateCustomerBalance));

function setCustomerFreebets(req: Request, res: Response) {
    const merchant = MerchantService.getMerchant(req);
    if (merchant.isPromoInternal) {
        throw new InvalidRequest("free bets are managed by Skywind promotion system");
    }
    const customer = CustomerService.getMerchantCustomer(req);
    const [ coin, count ]: string[] = getParamValues(req, ["coin", "count"]);

    res.send(CustomerService.setCustomerFreebets(customer, +coin, +count));
}

router.post("/merchant/:merch_id/customer/:cust_id/freebets/:count/:coin",
    routerCallbackWrapper(setCustomerFreebets));

// create or update extra data for customer
router.post("/merchant/:merch_id/customer/:cust_id/extra_data/:action",
    routerCallbackWrapper((req: Request, res: Response) => {
    const merchant = MerchantService.getMerchant(req);
    const customer = CustomerService.getMerchantCustomer(req);

    const action = getAndValidateAction(req.params.action);

    res.status(201).send({
        message: "Extra data created",
        extra_data: ExtraDataModel.createActionData(merchant.merch_id, customer.cust_id, action, req.body)
    });
}));

// Get customer extra data by action
router.get("/merchant/:merch_id/customer/:cust_id/extra_data/:action",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);

        const action: string = getAndValidateAction(req.params.action);

        res.send(ExtraDataModel.getActionData(merchant.merch_id, customer.cust_id, action));
    }));

// Delete customer extra data
router.delete("/merchant/:merch_id/customer/:cust_id/extra_data/:action",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);

        const action: string = getAndValidateAction(req.params.action);

        ExtraDataModel.deleteActionData(merchant.merch_id, customer.cust_id, action);
        res.status(204).end();
    }));

// Get all merchant extra data
router.get("/merchant/:merch_id/extra_data",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);

        res.send(ExtraDataModel.getMerchantData(merchant.merch_id));
    }));

// Get customer extra data
router.get("/merchant/:merch_id/customer/:cust_id/extra_data",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);

        res.send(ExtraDataModel.getCustomerData(merchant.merch_id, customer.cust_id));
    }));

// create or update custom error for customer
router.post("/merchant/:merch_id/customer/:cust_id/error/:action/raiseType/:raiseType",
    routerCallbackWrapper((req: Request, res: Response, next: NextFunction) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);

        const action: string = getAndValidateAction(req.params.action);
        const raiseType: RaiseType = getAndValidateRaiseType(req.params.raiseType);

        const customError = CustomErrorModel.createActionError(merchant.merch_id, customer.cust_id,
            action, raiseType, req.body);
        res.status(201).send({ message: "error created", error: customError });
    }));

// delete custom errors from customer
router.delete("/merchant/:merch_id/customer/:cust_id/error/:action/raiseType/:raiseType",
    routerCallbackWrapper((req: Request, res: Response, next: NextFunction) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);

        const action: string = getAndValidateAction(req.params.action);
        const raiseType: RaiseType = getAndValidateRaiseType(req.params.raiseType);

        CustomErrorModel.deleteActionErrorByRaiseType(merchant.merch_id, customer.cust_id, action, raiseType);

        res.status(204).end();
    }));

// Get all merchant customer errors
router.get("/merchant/:merch_id/error",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);

        res.send(CustomErrorModel.getMerchantErrors(merchant.merch_id));
    }));

// Get customer errors
router.get("/merchant/:merch_id/customer/:cust_id/error",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);

        res.send(CustomErrorModel.getCustomerErrors(merchant.merch_id, customer.cust_id));
    }));

// Get customer errors by action
router.get("/merchant/:merch_id/customer/:cust_id/error/:action",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);
        const action = getAndValidateAction(req.params.action);

        res.send(CustomErrorModel.getActionError(merchant.merch_id, customer.cust_id, action));
    }));

router.delete("/merchant/:merch_id/customer/:cust_id/error/:action",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);
        const action = getAndValidateAction(req.params.action);

        CustomErrorModel.deleteActionError(merchant.merch_id, customer.cust_id, action);

        res.status(204).end();
    }));

router.get("/merchant/:merch_id/customer/:cust_id/ticket",
    routerCallbackWrapper((req: Request, res: Response) => {
        const merchant = MerchantService.getMerchant(req);
        const customer = CustomerService.getMerchantCustomer(req);
        const ticket = createTicket(merchant.merch_id, customer.cust_id);
        res.send(ticket.id);
    }));

router.post("/set/register_round",
    body("merch_id").isString(),
    body("merch_password").isString(),
    body("merch_password").isString(),
    ...getRegisterRoundValidators(),
    function(req: Request, res: Response) {
    const response = setRegisterRound(req.body);
    response.error_code = 0;
    res.send(response);
});

export default router;
