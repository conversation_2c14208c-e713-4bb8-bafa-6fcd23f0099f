import { NextFunction, Request, Response } from "express";
import { MerchantHolder } from "../../entities/common";
import { MOCK_RESPONSE_HEADER } from "../../utils/helper";
import { settings } from "../../models/settings";

export function notSaveAnyDataMiddleware(
    responder: (req: Request & MerchantHolder, res?: Response) => string | object
) {
    return (req: Request & MerchantHolder, res: Response, next: NextFunction) => {
        res.set(MOCK_RESPONSE_HEADER, `${settings.notSaveAnyData}`);

        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req, res));
            } catch (err) {
                return next(err);
            }

        }
        return next();
    };
}
