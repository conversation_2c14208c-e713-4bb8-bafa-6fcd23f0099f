import { NextFunction, Request, Response } from "express";

import config from "../../config";
import * as crypto from "crypto";
import * as ISBErrors from "./errors";

import { REQUEST_COMMAND, RequestEx } from "./entities";
import { InvalidRequest } from "../../errors";
import { settings } from "../../models/settings";
import { selectResponse } from "./responses";
import { extractAdapterRequestData } from "./utils";
import { validateCustomerSession } from "../../service/session";
import { CustomerSessionInfo } from "../../models/session";
import * as SessionModel from "../../models/session";
import {
    InvalidSessionError,
    InvalidTokenError,
    MerchantNotFoundError,
    PlayerNotFoundError
} from "./errors";
import * as MerchantService from "../../service/merchant";
import * as CustomerService from "../../service/customer";
import * as Merchants from "../../models/merchant";
import { tickets } from "../../models/ticket";

export function validateRequestData(req: Request, res: Response, next: NextFunction) {
    const hash = req.query.hash;
    if (!hash) {
        next(new ISBErrors.ParamMissedError("HMAC is absent"));
    }
    const hmac = crypto.createHmac(config.operator.cryptoAlgorythm, config.operator.secretKey)
        .update(JSON.stringify(req.body))
        .digest("hex");

    if (hmac !== req.query.hash) {
        next(new ISBErrors.GeneralRequestError("Invalid HMAC"));
    }
    return next();
}

export function parseCommand(req: RequestEx, res: Response, next: NextFunction) {
    const adapterRequest = extractAdapterRequestData(req);
    if (adapterRequest.action) {
        req.operatorCommand = adapterRequest.action.command;
        next();
    } else {
        next(new InvalidRequest("Command is not provided"));
    }
}

export function hardCodedResponse(req: RequestEx, res: Response, next: NextFunction) {
    if (settings.notSaveAnyData) {
        return res.send(selectResponse(req.operatorCommand));
    }
    return next();
}

export const authenticate = (req: RequestEx, res: Response, next: NextFunction) => {
    const reqData = extractAdapterRequestData(req);

    const sessionId = reqData.sessionid;
    const session: CustomerSessionInfo = SessionModel.getById(sessionId);

    if (reqData.action.command === REQUEST_COMMAND.BET && (!session || !validateCustomerSession(sessionId))) {
        throw new ISBErrors.AuthError("Session is invalid or expired");
    }

    const adapterRequest = extractAdapterRequestData(req);

    if (adapterRequest.token.startsWith("isoftbet")) {
        const parts = adapterRequest.token.split("_");
        if (parts.length !== 4) {
            throw new InvalidTokenError();
        }
        const licenseeid = parts[1];
        const operator = parts[2];
        const jurisdiction = parts[3];
        const merchantCode = `ISB__${licenseeid}__${operator}__${jurisdiction.toLowerCase()}`;

        req.merchant = MerchantService.createMerchant(merchantCode, {
            merch_id: merchantCode,
            merch_pwd: "qwerty123",
            isPromoInternal: "false",
            multiple_session: "false"
        });

        req.customer = CustomerService.createCustomer(req.merchant, {
            currency_code: adapterRequest.currency,
            cust_id: adapterRequest.playerid,
            jurisdiction: jurisdiction,
            first_name: "firstName",
            last_name: "lastName",
            country: adapterRequest.country
        });
    } else {
        const ticket = tickets[adapterRequest.token];
        if (!ticket) {
            throw new InvalidSessionError();
        }
        req.merchant = Merchants.getById(ticket.merchantId);
        if (!req.merchant) {
            throw new MerchantNotFoundError();
        }
        req.customer = req.merchant.customers[ticket.custId];
        if (!req.customer) {
            throw new PlayerNotFoundError();
        }
    }

    return next();
};
