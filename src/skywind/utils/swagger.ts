import { lazy } from "@skywind-group/sw-utils";
import { Application } from "express";
import * as fs from "fs";
import { serve, setup, SwaggerUiOptions } from "swagger-ui-express";

export function loadJson(file: string): any {
    // read swagger json file
    const content = fs.readFileSync(file, "utf8");
    return JSON.parse(content);
}

export function setupSwagger(app: Application, swaggerJson) {
    // Swagger UI
    const opt: SwaggerUiOptions = {
        explorer: true,
        swaggerUrl: "/api-docs",
        swaggerOptions: {
            docExpansion: "none",
            requestSnippetsEnabled: true,
        }
    };
    app.use("/docs", serve, setup(null, opt));
    app.get("/api-docs", (req, res) => res.json(swaggerJson));
}

const swaggerHolder = lazy(() => loadJson("swagger.json"));

export function getSwagger(updateDoc = (doc) => doc): Promise<any> {
    return new Promise( (resolve, reject) => {
        const updatedSwagger = updateDoc(swaggerHolder.get());
        resolve(updatedSwagger);
    });
}
