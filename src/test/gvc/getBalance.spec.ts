import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");
import {
    GVCPlayerBalanceResponse,
    SKYWIND_PARTNER_ID
} from "../../skywind/entities/gvc";

import { GVCBaseSpec } from "./baseSpec";
import { createTicket } from "../../skywind/service/ticket";

@suite("[GVC.balance]")
class GVCBalanceSpec extends GVCBaseSpec {

    @test("should return player's balance")
    public async testBalance() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedBalanceRequest = Object.assign(
            this.balanceRequest, { secureToken: ticket.id });

        const expectedResponse: GVCPlayerBalanceResponse = {
            accountId: `${this.merchantCode}__${this.playerCode}`,
            partnerId: SKYWIND_PARTNER_ID,
            currencyCode: "USD",
            gameCode: "GAMECODE001",
            balance: 10000, // minor units
            status: {
                statusCode: "0",
                statusMessage: "success",
            }
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(updatedBalanceRequest)
            .expect(200);

        expect(actualResponse.body).deep.equal(expectedResponse);
    }
}
