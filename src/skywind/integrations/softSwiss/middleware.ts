import { NextFunction, Response } from "express";
import * as SoftSwissErrors from "./errors";
import { RequestEx } from "./entities";
import * as Merchants from "../../models/merchant";

const MERCH_ID = "soft_swiss__mock";

export function validatePlayRequestData(req: RequestEx, res: Response, next: NextFunction) {
    const merchant: Merchants.Merchant = Merchants.getById(MERCH_ID, false);
    if (!merchant) {
        throw new SoftSwissErrors.BadRequestError(`Merchant with id ${MERCH_ID}  not found`);
    }
    const { account_id, currency, game_id } = req.body;
    if (!account_id) {
        throw new SoftSwissErrors.BadRequestError("Body param account_id is required");
    }
    if (!currency) {
        throw new SoftSwissErrors.BadRequestError("Body param currency is required");
    }
    if (!game_id) {
        throw new SoftSwissErrors.BadRequestError("Body param game_id is required");
    }
    const customer: Merchants.Customer = merchant.customers[account_id];
    if (!customer) {
        throw new SoftSwissErrors.BadRequestError(`Customer with id ${account_id} not found`);
    }

    req.merchant = merchant;
    req.customer = customer;
    next();
}

export function validateFreespinsFinish(req: RequestEx, res: Response, next: NextFunction) {
    const merchant: Merchants.Merchant = Merchants.getById(MERCH_ID, false);
    if (!merchant) {
        throw new SoftSwissErrors.BadRequestError(`Merchant with id ${MERCH_ID}  not found`);
    }
    const account_id = "3812";
    const customer: Merchants.Customer = merchant.customers[account_id];
    if (!customer) {
        throw new SoftSwissErrors.BadRequestError(`Customer with id ${account_id} not found`);
    }

    req.merchant = merchant;
    req.customer = customer;
    next();
}
