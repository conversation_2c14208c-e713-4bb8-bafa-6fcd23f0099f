import { Request } from "express";
import {
    BalanceResponse,
    VerifyTokenResponse,
    PaymentResponse,
    RollbackResponse,
    RELAX_JURISDICTION, AckPromotionResponse
} from "../entities/relax";
import { getDataFromTemplateTicket } from "./ticket";
import * as Session from "./session";
import * as uuid from "uuid";
import { settings } from "../models/settings";
import { MerchantHolder } from "../entities/common";

export const verifyTokenResponder = (req: Request & MerchantHolder): VerifyTokenResponse => {
    const { token, partnerid } = req.body;
    const [custId, currency, jurisdiction, country] = getDataFromTemplateTicket(token);
    const playerId = custId || Date.now().toString();
    return {
        playerid: playerId,
        currency,
        currencyrate: 1.0,
        customerid: "test_id",
        username: "test_username",
        locale: "en_UK",
        countrycode: country || "GB",
        gender: 1,
        lastlogin: new Date().toISOString(),
        birthdate: new Date().toISOString(),
        balance: settings.amount,
        jurisdiction: jurisdiction || RELAX_JURISDICTION.MT,
        sessionid: Session.generateCustomerSessionId(playerId),
        partnerid
    };
};

export const getBalanceResponder = (req: Request & MerchantHolder): BalanceResponse => {
    const { sessionid, currency } = req.body;

    return {
        sessionid,
        currency,
        balance: settings.amount
    };
};

export const paymentResponder = (req: Request & MerchantHolder): PaymentResponse => {
    const { sessionid, txid } = req.body;

    return {
        sessionid,
        txid,
        relaxid: uuid.v4(),
        balance: settings.amount
    };
};

export const rollbackResponder = (req: Request & MerchantHolder): RollbackResponse => {
    const { sessionid, txid } = req.body;

    return {
        sessionid,
        txid,
        relaxtxid: uuid.v4()
    };
};

export const ackPromotionResponder = (req: Request & MerchantHolder): AckPromotionResponse => {
    return {
        promotion_statuses: [{ status: "success", txid: "test"}]
    };
};
