interface SQLOperation {
    readonly operation: string;
    readonly fastPattern?: RegExp;
    readonly pattern: RegExp;
}

export class SQLOperationParser {
    public static getOperation(operation: any): string {
        const sql: string = typeof operation === "object" ? (operation.sql || operation.text) : operation as string;
        for (const m of SQLOperationParser.OPERATIONS) {
            const description = this.parse(sql, m);
            if (description) {
                return description;
            }
        }
        return undefined;
    }

    private static OPERATIONS: SQLOperation[] = [
        // eslint-disable-next-line no-useless-escape
        { operation: "select", fastPattern: /^\s*select/i, pattern: /^\s*select\s+(?:.*?\s+from\s+((?:"[^"]*"\.)?(?:"[^"]*"|[a-zA-Z_]\w*\(\)))|([a-zA-Z_]\w*\(\)))/i },
        { operation: "update", fastPattern: /^\s*update/i, pattern: /^\s*update\s+([^\s,;]*).*/i },
        {
            operation: "insert",
            fastPattern: /^\s*insert/i,
            pattern: /^\s*insert(?:\s+ignore)?\s+into\s+([^\s(,;]*).*/i,
        },
        { operation: "delete", fastPattern: /^\s*delete/i, pattern: /^\s*delete\s+from\s+([^\s,(;]*).*/i },
        { operation: "transaction", pattern: /^\s*(start|begin|rollback|commit)/i },
        { operation: "command", pattern: /^\s*(set)/i },
    ];

    private static parse(sql: string, operation: SQLOperation): string {
        const match = operation.fastPattern ? operation.fastPattern.test(sql) : true;
        let result;
        if (match) {
            const queryMatch = operation.pattern.exec(sql);
            if (queryMatch) {
                const srt = operation.operation === "select" ? queryMatch[1] || queryMatch[2] : queryMatch[1];
                result = `${operation.operation} ${srt}`;
            }
        }

        return result;
    }
}
