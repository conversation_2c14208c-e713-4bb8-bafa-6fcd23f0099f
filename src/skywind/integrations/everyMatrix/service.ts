import { Customer, Merchant } from "../../models/merchant";
import {
    BalanceRequest,
    BasePaymentRequest,
    BaseResponse,
    BetRequest, BonusWinRequest,
    CancelBetRequest,
    EMAuthenticateRequest,
    EMAuthenticateResponse, EMRoundInfoResponse,
    EVERY_MATRIX_ACTION,
    EVERY_MATRIX_GAME_TOKEN,
    EVERY_MATRIX_JURISDICTION,
    EVERY_MATRIX_TOKEN,
    WinRequest
} from "./entities";
import {
    CurrencyDoesNotMatch,
    EVERY_MATRIX_STATUS,
    IEveryMatrixError,
    InvalidJurisdictionError,
    JurisdictionNotSetError,
    TokenNotFound, TransactionNotFound,
    UnknownError
} from "./errors";
import { Ticket, tickets } from "../../models/ticket";
import { settings } from "../../models/settings";
import * as CustomErrors from "../../models/customError";
import { RaiseType } from "../../models/customError";
import * as Transaction from "../../models/transaction";
import { WALLET_ACTION } from "../../models/transaction";

import { throwCustomError, throwCustomErrorIfExists } from "../../service/customError";
import { extraDataHandler, getExtraData } from "../../service/extraData";
import { InsufficientBalance } from "../../errors";

export interface EveryMatrixWalletAPI {
    authenticatePlayer(request: EMAuthenticateRequest,
                       merchant: Merchant): Promise<EMAuthenticateResponse | IEveryMatrixError>;

    authenticatePredefinedPlayer(request: EMAuthenticateRequest, operatorId: string,
                                 jurisdiction: string): EMAuthenticateResponse | IEveryMatrixError;

    bet(request: BetRequest): Promise<BaseResponse>;

    win(request: WinRequest): Promise<BaseResponse>;

    bonusWin(request: BonusWinRequest): Promise<BaseResponse>;

    cancel(request: CancelBetRequest): Promise<BaseResponse>;

    getBalance(request: BalanceRequest): Promise<BaseResponse>;
}

export class EveryMatrixService implements EveryMatrixWalletAPI {
    constructor(public merchant: Merchant, public customer?: Customer) {
    }

    public async authenticatePlayer(request: EMAuthenticateRequest): Promise<EMAuthenticateResponse | IEveryMatrixError> {
        const ticket = this.authorizePlayer(request.LaunchToken);
        const customer = this.merchant.customers[ticket.custId];

        if (!customer) {
            throw new TokenNotFound();
        }

        await throwCustomErrorIfExists(EVERY_MATRIX_ACTION.AUTHENTICATE, this.merchant, customer);

        if (!customer.jurisdiction) {
            throw new JurisdictionNotSetError();
        }

        if (customer.jurisdiction !== EVERY_MATRIX_JURISDICTION.MT &&
            customer.jurisdiction !== EVERY_MATRIX_JURISDICTION.UK &&
            customer.jurisdiction !== EVERY_MATRIX_JURISDICTION.FI) {
            throw new InvalidJurisdictionError();
        }

        const result: EMAuthenticateResponse = {
            Token: request.LaunchToken,
            TotalBalance: customer.balance.amount.toString(),
            Currency: customer.currency_code,
            UserName: customer.cust_login,
            UserId: customer.cust_id,
            Country: customer.country,
            Age: "35",
            Sex: "men",
            Jurisdiction: customer.jurisdiction,
            Status: EVERY_MATRIX_STATUS.OK
        };

        if (customer.bet_limit) {
            result.BetLimit = customer.bet_limit;
        }

        return getExtraData(result, EVERY_MATRIX_ACTION.AUTHENTICATE, this.merchant, customer);
    }

    public authenticatePredefinedPlayer(request: EMAuthenticateRequest,
                                        operatorId: string,
                                        jurisdiction: string): EMAuthenticateResponse | IEveryMatrixError {
        const gameToken = request.LaunchToken.replace(EVERY_MATRIX_TOKEN.NORMAL, EVERY_MATRIX_GAME_TOKEN.NORMAL);
        const { userId } = this.validateToken(gameToken, operatorId);
        const customer = this.getOrCreateCustomer(userId, jurisdiction);

        this.setCustomErrorDuringLogin(request.LaunchToken, customer);

        const result = {
            Token: gameToken,
            TotalBalance: customer.balance.amount.toString(),
            Currency: customer.currency_code,
            UserName: customer.cust_login,
            UserId: customer.cust_id,
            Country: customer.country,
            Age: "35",
            Sex: "men",
            Jurisdiction: customer.jurisdiction,
            Status: EVERY_MATRIX_STATUS.OK,
        };

        return getExtraData(result, EVERY_MATRIX_ACTION.AUTHENTICATE, this.merchant, customer);
    }

    private validateToken(gameToken: string, operatorId: string): { userId: string } {
        // example of launch token 'emLaunchToken_{operatorId}_{jurisdiction}'
        if (gameToken.startsWith(EVERY_MATRIX_GAME_TOKEN.NORMAL)) {
            const { custId } = this.getOrCreateTicket(gameToken, operatorId);
            return {
                userId: custId
            };
        }
        throw new TokenNotFound();
    }

    private getOrCreateTicket(gameToken: string, operatorId: string): Ticket {
        const ticket = tickets[gameToken];

        if (ticket) {
            return ticket;
        }

        tickets[gameToken] = {
            id: gameToken,
            merchantId: operatorId,
            custId: Date.now().toString(),
            creationTime: Date.now()
        };

        return tickets[gameToken];
    }

    private getOrCreateCustomer(userId: string, jurisdiction: string): Customer {
        const defaultCurrency: string = "EUR";

        if (!this.merchant.customers[userId]) {
            this.merchant.customers[userId] = {
                cust_id: userId,
                cust_login: `log${userId}`,
                country: "US",
                jurisdiction,
                currency_code: defaultCurrency,
                balance: {
                    currency_code: defaultCurrency,
                    amount: settings.amount
                }
            };
        }

        return this.merchant.customers[userId];
    }

    public static checkIfPredefinedErrorToken(token: string) {
        if (token.startsWith(`${EVERY_MATRIX_TOKEN.BET_UNKNOWN}`) ||
            token.startsWith(`${EVERY_MATRIX_TOKEN.BET_CURRENCY_NOT_MATCH}`) ||
            token.startsWith(`${EVERY_MATRIX_TOKEN.WIN_UNKNOWN}`) ||
            token.startsWith(`${EVERY_MATRIX_TOKEN.WIN_CURRENCY_NOT_MATCH}`) ||
            token.startsWith(`${EVERY_MATRIX_TOKEN.CANCEL_UNKNOWN}`) ||
            token.startsWith(`${EVERY_MATRIX_TOKEN.CANCEL_CURRENCY_NOT_MATCH}`) ||
            token.startsWith(`${EVERY_MATRIX_TOKEN.BET_CANCEL_UNKNOWN}`)) {
            return true;
        }

        return false;
    }

    private setCustomErrorDuringLogin(token: string, customer: Customer) {
        if (token.startsWith(`${EVERY_MATRIX_TOKEN.BET_UNKNOWN}`)) {
            return CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.BET,
                RaiseType.AFTER,
                new UnknownError());
        }

        if (token.startsWith(`${EVERY_MATRIX_TOKEN.BET_CURRENCY_NOT_MATCH}`)) {
            return CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.BET,
                RaiseType.AFTER,
                new CurrencyDoesNotMatch());
        }

        if (token.startsWith(`${EVERY_MATRIX_TOKEN.WIN_UNKNOWN}`)) {
            return CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.WIN,
                RaiseType.AFTER,
                new UnknownError());
        }

        if (token.startsWith(`${EVERY_MATRIX_TOKEN.WIN_CURRENCY_NOT_MATCH}`)) {
            return CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.WIN,
                RaiseType.AFTER,
                new CurrencyDoesNotMatch());
        }

        if (token.startsWith(`${EVERY_MATRIX_TOKEN.CANCEL_UNKNOWN}`)) {
            return CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.CANCEL,
                RaiseType.AFTER,
                new UnknownError());
        }

        if (token.startsWith(`${EVERY_MATRIX_TOKEN.CANCEL_CURRENCY_NOT_MATCH}`)) {
            return CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.CANCEL,
                RaiseType.AFTER,
                new CurrencyDoesNotMatch());
        }

        if (token.startsWith(`${EVERY_MATRIX_TOKEN.BET_CANCEL_UNKNOWN}`)) {
            CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.BET,
                RaiseType.AFTER,
                new UnknownError());

            return CustomErrors.createActionError(
                this.merchant.merch_id, customer.cust_id,
                EVERY_MATRIX_ACTION.CANCEL,
                RaiseType.AFTER,
                new UnknownError());
        }
    }

    public authorizePlayer(gameToken: string): Ticket {
        const ticket = tickets[gameToken];
        if (!ticket) {
            throw new TokenNotFound();
        }
        return ticket;
    }

    @throwCustomError({ action: EVERY_MATRIX_ACTION.BET })
    @extraDataHandler({ action: EVERY_MATRIX_ACTION.BET })
    public async bet(request: BetRequest): Promise<BaseResponse> {
        return this.makePayment(request, WALLET_ACTION.debit);
    }

    @throwCustomError({ action: EVERY_MATRIX_ACTION.CANCEL })
    @extraDataHandler({ action: EVERY_MATRIX_ACTION.CANCEL })
    public async cancel(request: CancelBetRequest): Promise<BaseResponse> {
        const rollbackTransaction = Transaction.getById(request.ExternalId, WALLET_ACTION.rollback);

        if (!rollbackTransaction) {
            const originalTransaction = Transaction.getById(request.CanceledExternalId, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new TransactionNotFound();
            }

            const [_, amount, __, isRollback] = originalTransaction;

            if (!isRollback) {
                Transaction.rollbackById(request.CanceledExternalId, WALLET_ACTION.debit);
                this.customer.balance.amount = this.customer.balance.amount - amount;
            }

            Transaction.setById(request.ExternalId, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        }

        return {
            TotalBalance: this.customer.balance.amount.toString(),
            Currency: this.customer.balance.currency_code,
            Status: EVERY_MATRIX_STATUS.OK
        };
    }

    @throwCustomError({ action: EVERY_MATRIX_ACTION.WIN })
    @extraDataHandler({ action: EVERY_MATRIX_ACTION.WIN })
    public async win(request: WinRequest): Promise<BaseResponse> {
        return this.makePayment(request, WALLET_ACTION.credit);
    }

    @throwCustomError({ action: EVERY_MATRIX_ACTION.ROUND_INFO })
    @extraDataHandler({ action: EVERY_MATRIX_ACTION.ROUND_INFO })
    public async roundInfo(request: EMAuthenticateRequest): Promise<EMRoundInfoResponse> {
        return {
            Status: "Ok"
        };
    }

    @throwCustomError({ action: EVERY_MATRIX_ACTION.BONUS_WIN })
    @extraDataHandler({ action: EVERY_MATRIX_ACTION.BONUS_WIN })
    public async bonusWin(request: BonusWinRequest): Promise<BaseResponse> {
        return this.makePayment(request, WALLET_ACTION.credit);
    }

    @throwCustomError({ action: EVERY_MATRIX_ACTION.GET_BALANCE })
    @extraDataHandler({ action: EVERY_MATRIX_ACTION.GET_BALANCE })
    public async getBalance(request: BalanceRequest): Promise<BaseResponse> {
        return {
            TotalBalance: this.customer.balance.amount.toString(),
            Currency: this.customer.balance.currency_code,
            Status: EVERY_MATRIX_STATUS.OK
        };
    }

    private makePayment(request: BasePaymentRequest, action: WALLET_ACTION): BaseResponse {
        const amount = +request.Amount;
        if (this.customer.balance.amount + amount * action < 0) {
            throw new InsufficientBalance();
        }

        const transaction = Transaction.getById(request.ExternalId, action);

        if (!transaction) {
            Transaction.setById(request.ExternalId, action, Math.abs(request.Amount), this.customer.cust_id);
            this.customer.balance.amount = this.customer.balance.amount + Math.abs(request.Amount) * action;
        }

        return {
            TotalBalance: this.customer.balance.amount.toString(),
            Currency: this.customer.balance.currency_code,
            Status: EVERY_MATRIX_STATUS.OK
        };
    }
}
