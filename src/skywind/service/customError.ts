import { EntityNotFound, InvalidRequest } from "../errors";
import { Customer, Merchant } from "../models/merchant";
import * as CustomErrorModel from "../models/customError";
import { RaiseType } from "../models/customError";
import logger from "../utils/logger";
import { INTEGRATION_TYPE } from "../../config/integrationType";
const log = logger("custom-error");

export function getCustomError(merchantId: string, customerId: string, action: string) {
    const error = CustomErrorModel.getActionError(merchantId, customerId, action);
    if (!error) {
        throw new EntityNotFound("custom error", action);
    }

    return error;
}

export async function throwCustomErrorIfExists(action: string,
                                         merchant?: Merchant, customer?: Customer,
                                         raisingType: RaiseType = RaiseType.AFTER) {
    if (!merchant || !customer) {
        return;
    }
    // safely return and remove the value from a nested key in an object
    const error = CustomErrorModel.shiftActionErrorByRaiseType(merchant.merch_id,
        customer.cust_id, action, raisingType);

    if (!error) {
        return;
    }
    if (error.delayMs) {
        log.info({ action, raisingType, error }, `Waiting for error delay of ${error.delayMs} ms`);
        await new Promise(resolve => setTimeout(resolve, error.delayMs));
    }
    log.info({ action, raisingType, error }, "Throw mock custom error");

    throw error;
}

export function getAndValidateAction(action: string): string {
    if (!action) {
        throw new InvalidRequest("action is missing");
    }

    if (CustomErrorModel.actionList.indexOf(action) === -1) {
        throw new InvalidRequest("Only these actions allowed: " + JSON.stringify(CustomErrorModel.actionList));
    }
    return action;
}

export function getAndValidateRaiseType(raisingType: string) {
    if (!raisingType) {
        throw new InvalidRequest("raiseType is missing (please specify value 'before' or 'after')");
    }
    const result = RaiseType[raisingType.toUpperCase()];

    if (!result) {
        throw new InvalidRequest(`raiseType '${raisingType}' is invalid (please specify value 'before' or 'after')`);
    }
    return result;
}

export interface CustomErrorOptions {
    action?: string;
}

// The function that this decorator is applied to MUST be an async function
export function throwCustomError(options: CustomErrorOptions = {}) {
    return (target, key, descriptor) => {
        const originalMethod = descriptor.value;

        descriptor.value = async function(...args) {
            const action = (options.action || key).toLowerCase();
            await throwCustomErrorIfExists(action, this.merchant, this.customer, RaiseType.BEFORE);
            const result = originalMethod.apply(this, args);
            await throwCustomErrorIfExists(action, this.merchant, this.customer, RaiseType.AFTER);
            return result;
        };
    };
}
