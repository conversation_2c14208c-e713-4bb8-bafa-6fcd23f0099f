import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { SinonStub, spy, stub } from "sinon";
import { SISAL_ERROR_CODE } from "../../skywind/sisal/sisal.errors";
import { SisalBaseSpec } from "./base.spec";
import { createTicket } from "../../skywind/service/ticket";
import { Ticket } from "../../skywind/models/ticket";
const request = require("supertest");

@suite("Sisal.auth")
class SisalAuthSpec extends SisalBaseSpec {

    @test("should authenticate")
    public async authenticate() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);

        await request(this.server)
            .get(this.urlPathToSessionBalance(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .expect(200);
    }

    @test("throw error when gameSessionId is invalid")
    public async throwErrorWhenInvalidGameSessionId() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);

        const actualResponse = await request(this.server)
            .get(this.urlPathToSessionBalance(ticket.id + "invalid"))
            .set("Authorization", this.fixtures.headers.authorization)
            .expect(500);

        expect(actualResponse.body.code).deep.equal(SISAL_ERROR_CODE.UNAUTHORIZED_ERROR);
    }

    @test("should authenticate and check x-authorization cypher")
    public async authenticateAndCheckCypher() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);

        await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(200);
    }

    @test("should authenticate and check x-authorization cypher")
    public async throwErrorWhenCypherIsInvalid() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const invalidDebitRequest = {
            ...this.fixtures.debitRequest,
            gamePhaseId: "round2"
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(invalidDebitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(500);

        expect(actualResponse.body.code).deep.equal(SISAL_ERROR_CODE.INVALID_XAUTH_ERROR);
    }
}
