export enum ERROR_CODES {
    FORBIDDEN = 403,
    BAD_REQUEST = 400,
    NOT_ENOUGHT_FUNDS = 100
}

class SoftSwissError extends Error {
    private status: number;
    private code: number;

    constructor(
        code: ERROR_CODES,
        message: string,
        status: number = 400,
    ) {
        super(message);
        this.status = status;
        this.code = code;
    }

    public getStatus() {
        return this.status;
    }
}

export class BadRequestError extends SoftSwissError {
    constructor(message: string) {
        super(ERROR_CODES.BAD_REQUEST, message, 400);
    }
}

export class ForbiddenError extends SoftSwissError {
    constructor(message: string) {
        super(ERROR_CODES.FORBIDDEN, message, 403);
    }
}

export class NotEnoughFundsError extends SoftSwissError {
    constructor(message: string) {
        super(ERROR_CODES.NOT_ENOUGHT_FUNDS, message);
    }
}
