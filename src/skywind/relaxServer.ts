"use strict";

import createApplication from "../config/express";
import { defineRoutes } from "./api/routers";
import { Application } from "express";
import { Server } from "http";
import * as fs from "fs";
import logger from "./utils/logger";
import config from "./config";
import { INTEGRATION_TYPE } from "../config/integrationType";

const version = fs.readFileSync(__dirname + "/version", "utf8");

export default async (port = 8004): Promise<Server> => {
    const log = logger(INTEGRATION_TYPE.RELAX);
    const app = await getApplication();
    return new Promise<Server>((resolve) => {
        config.serverName = "relax";
        const server: Server = require("http")
            .createServer(app)
            .listen(port, null, () => {
                log.info("Relax mock server listening on " + port);
                log.info("AppVersion: " + version);
                resolve(server);
            });
    });
};

let app: Application;

export async function getApplication(): Promise<Application> {
    config.serverName = INTEGRATION_TYPE.RELAX;
    if (!app) {
        app = createApplication();
        await defineRoutes(app, INTEGRATION_TYPE.RELAX);
    }
    return app;
}
