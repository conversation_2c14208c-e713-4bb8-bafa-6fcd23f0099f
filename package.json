{"name": "IPM-mock", "version": "5.54.0", "description": "Merchant emulation", "main": "out/skywind/app.js", "scripts": {"lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "clean": "rm -rf ./out", "compile": "tsc -b tsconfig.json", "start": "node out/skywind/app.js", "test": "pnpm run only-test && pnpm run sonar", "only-test": "MEASURES_BASE_INSTRUMENT=false nyc node_modules/.bin/_mocha --timeout 6000 out/test/**/*.js", "version": "echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./out/skywind/version", "sonar": "node sonarqube.mjs", "dev": "pnpm run compile && pnpm run version && INTERNAL_SERVER_PORT=9003 MEASURES_PROVIDER=memory node out/skywind/app.js", "dev:pariplay": "pnpm run compile && pnpm run version && INTERNAL_SERVER_PORT=9003 MEASURES_PROVIDER=memory node out/skywind/appPariplay.js", "dev:softswiss": "pnpm run compile && pnpm run version && INTERNAL_SERVER_PORT=9003 MEASURES_PROVIDER=memory node out/skywind/appSoftSwiss.js", "preinstall": "node preinstall.cjs"}, "devDependencies": {"@types/chai": "^4.3.20", "@types/chai-as-promised": "^7.1.8", "@types/chai-http": "3.0.5", "@types/express": "4.17.21", "@types/lodash": "4.17.16", "@types/mocha": "^10.0.10", "@types/node": "^22.13.10", "@types/pg": "8.11.11", "@types/sinon": "^17.0.3", "@types/sinon-chai": "3.2.12", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "chai-shallow-deep-equal": "1.4.6", "chai-xml": "0.4.1", "eslint": "^9.21.0", "mocha": "10.8.2", "mocha-typescript": "1.1.17", "nyc": "17.1.0", "sinon": "^18.0.1", "sinon-chai": "3.7.0", "sonarqube-scanner": "3.5.0", "supertest": "7.0.0", "ts-node": "^10.7.0", "typescript": "5.7.3"}, "author": "<PERSON> <<EMAIL>>", "license": "ISC", "dependencies": {"@skywind-group/gelf-stream": "1.2.6", "@skywind-group/sw-currency-exchange": "2.3.17", "@skywind-group/sw-utils": "2.3.4", "body-parser": "1.20.3", "bole": "5.0.18", "bole-console": "0.1.10", "camaro": "6.2.3", "cls-hooked": "4.2.2", "compression": "1.8.0", "cookie-parser": "1.4.7", "crypto-js": "^4.2.0", "dateformat": "4.6.3", "deepmerge": "4.3.1", "emitter-listener": "1.1.2", "express": "4.21.2", "express-http-proxy": "2.1.1", "express-prom-bundle": "7.0.2", "express-validator": "5.3.1", "fast-xml-parser": "4.5.3", "generic-pool": "3.9.0", "ioredis": "5.6.0", "jsonwebtoken": "9.0.2", "kafka-node": "5.0.0", "measured-core": "^2.0.0", "method-override": "3.0.0", "node-schedule": "2.1.1", "pg": "8.14.1", "prom-client": "~15.0.0", "properties": "1.2.1", "random-number": "0.0.9", "reflect-metadata": "0.2.2", "sequelize": "6.37.6", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "xmlbuilder": "15.1.1"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.15.5"}