import { InvalidRequest } from "../errors";
import { ACTION } from "../service/pariplay/actions";
import { RELAX_ACTION } from "../entities/relax";
import { TransactionType as SoftSwissOperatorAction } from "../integrations/softSwiss/entities";

const ipmActionList = [
    "validate_ticket",
    "get_balance",
    "rollback",
    "debit",
    "credit",
    "finish_round_with_statistics"
];

// pop action list
export const popActionList = [
    "verifyplayersession",
    "getplayerinfo",
    "moneytransactions",
    "getplayerbalance",
    "canceltransactions",
    "createbrokengame"
];

// Poker Star action list
export const pokerStarActionList = [
    "startgame",
    "getbalance",
    "bet",
    "handresult",
    "finalizedhandresult"
];

// EveryMatrix action list
export const everyMatrixActionList = [
    "authenticate",
    "getbalance",
    "win",
    "bet",
    "cancel",
    "roundinfo"
];

export const gvcActionList = [
    "verifytoken",
    "getbalance",
    "makepayment",
    "cancelbet"
];

export const mrGreenActionList = [
    "getaccount",
    "wager",
    "result",
    "cancelwager",
    "getbalance",
];

export const betVictorActionList = [
    "authenticate",
    "account-details",
    "place-bet",
    "settle-bet",
    "rollback-bet",
    "additional-winnings",
    "additional-bet"
];

export const sisalActionList = [
    "sessionbalance",
    "wager",
    "endwager",
    "cancelwager"
];

export const isoftBetActionList = [
    "init",
    "bet",
    "win",
    "cancel",
    "balance",
    "end",
    "rounddetails"
];

export const actionList = [
    ...ipmActionList,
    ...popActionList,
    ...pokerStarActionList,
    ...everyMatrixActionList,
    ...Object.values(RELAX_ACTION),
    ...Object.values(ACTION),
    ...Object.values(SoftSwissOperatorAction),
    ...gvcActionList,
    ...mrGreenActionList,
    ...betVictorActionList,
    ...sisalActionList,
    ...isoftBetActionList
];

export enum RaiseType {
    BEFORE = "BEFORE",
    AFTER = "AFTER"
}

let customErrors = {};

export const getAll = () => customErrors;

export const setAll = (newCustomErrors) => {
    customErrors = newCustomErrors;
    return customErrors;
};

export const getMerchantErrors = (merchantId: string) => customErrors[merchantId];

export const getCustomerErrors = (merchantId: string, customerId: string) => {
    return customErrors?.[merchantId]?.[customerId];
};

export const getActionError = (merchantId: string, customerId: string, action: string) => {
    return customErrors?.[merchantId]?.[customerId]?.[action];
};

export const shiftActionErrorByRaiseType = (merchantId: string, customerId: string,
                                            action: string, raiseType: RaiseType) => {
    const errors = customErrors?.[merchantId]?.[customerId]?.[action]?.[raiseType];
    return errors && errors.shift();
};

export const deleteActionError = (merchantId: string, customerId: string, action: string) => {
    if (customErrors?.[merchantId]?.[customerId]?.[action] === undefined) {
        throw new InvalidRequest("extra Errors is missing");
    }

    delete customErrors[merchantId][customerId][action];
};

export const deleteActionErrorByRaiseType =
    (merchantId: string, customerId: string, action: string, raiseType: RaiseType) => {
        if (customErrors?.[merchantId]?.[customerId]?.[action]?.[raiseType] === undefined) {
            throw new InvalidRequest("extra Errors is missing");
        }

        delete customErrors[merchantId][customerId][action][raiseType];
    };

export const createActionError = (merchantId: string, customerId: string, action: string,
                                  raiseType: RaiseType = RaiseType.AFTER,
                                  errors: any) => {
    customErrors[merchantId] = customErrors[merchantId] || {};
    customErrors[merchantId][customerId] = customErrors[merchantId][customerId] || {};
    customErrors[merchantId][customerId][action] = customErrors[merchantId][customerId][action] || {};
    customErrors[merchantId][customerId][action][raiseType] =
        customErrors[merchantId][customerId][action][raiseType] || [];

    if (Array.isArray(errors)) {
        errors.forEach(error => customErrors[merchantId][customerId][action][raiseType].push(error));
    } else {
        customErrors[merchantId][customerId][action][raiseType].push(errors);
    }

    return errors;
};
