import { NextFunction, Request, Response, Router } from "express";
import { SBTechExtraRequestData } from "../../entities/sbtech";
import * as Merchants from "../../models/merchant";
import { Merchant, Customer } from "../../models/merchant";
import { Ticket } from "../../models/ticket";
import { getSBTechTicket } from "../../service/ticket";
import config from "../../config";
import * as SBTechErrors from "../../errorsSBTech";

export const authenticate = (req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) => {
    let merchantId: string;
    let playerId: string;

    if (req.body.clientToken) {
        req.tokenData = getSBTechTicket(req.body.clientToken, config.expirationTime.ticket) as Ticket;
        merchantId = req.tokenData.merchantId;
        playerId = req.tokenData.custId;

    } else {
        merchantId = req.body.brandId;
        playerId = req.body.playerId;
    }

    const merchant: Merchant = Merchants.getById(merchantId, false);
    if (!merchant) {
        return next(new SBTechErrors.WrongCredentialsError());
    }

    const customer: Customer = merchant.customers[playerId];
    if (!customer) {
        return next(new SBTechErrors.WrongCredentialsError());
    }

    req.merchant = merchant;
    req.customer = customer;

    next();
};

type CustomFunction = (req: Request, res: Response, next: NextFunction) => void;
export type ControllersToBeWrapped = CustomFunction[] | CustomFunction;

export function wrapApiControllers(controllers: ControllersToBeWrapped): Array<any> {
    if (!controllers || !controllers.length) {
        return;
    }
    controllers = Array.isArray(controllers) ? controllers : [controllers];

    return controllers.map(controller =>
        async (req: Request, res: Response, next: NextFunction) => {
            try {
                await controller(req, res, next);
            } catch (error) {
                next(error);
            }
        });
}
