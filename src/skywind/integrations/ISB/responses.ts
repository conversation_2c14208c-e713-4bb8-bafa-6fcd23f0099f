import { AuthenticateResponse, BalanceResponse, BaseResponse, REQUEST_COMMAND, RESPONSE_STATUS } from "./entities";
import { settings } from "../../models/settings";

function getAuthResponse(): AuthenticateResponse {
    return {
        "status": RESPONSE_STATUS.SUCCESS,
        "sessionid": "IkL1Fs4eIuOywBIyGHRpHUBkbWSMvwqv",
        "playerid": "pragmatic2",
        "username": "pragmatic2",
        "currency": "EUR",
        "balance": settings.amount
    };
};

function getBalanceResponse(): BalanceResponse {
    return {
        "status": RESPONSE_STATUS.SUCCESS,
        "currency": "EUR",
        "balance": settings.amount
    };
}

export const selectResponse = (cmd: REQUEST_COMMAND): BaseResponse =>
    cmd === REQUEST_COMMAND.INIT ? getAuthResponse() : getBalanceResponse();
