{"swagger": "2.0", "info": {"version": "5.50.0", "title": "Skywind Mock Management", "description": "Management for <PERSON><PERSON>"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/merchant": {"get": {"description": "Get all Merchants and customers", "summary": "Get Merchant", "tags": ["Merchant"], "responses": {"200": {"description": "Found merchants", "schema": {"$ref": "#/definitions/MerchantList"}}}}, "post": {"summary": "Create Merchant", "tags": ["Merchant"], "parameters": [{"name": "Body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Merchant"}}], "responses": {"201": {"description": "Merchant created", "schema": {"$ref": "#/definitions/Merchant"}}}}}, "/v1/merchant/{merch_id}": {"get": {"summary": "Get Merchant", "tags": ["Merchant"], "parameters": [{"$ref": "#/parameters/merch_id"}], "responses": {"200": {"description": "Merchant found", "schema": {"$ref": "#/definitions/MerchantWithCustomer"}}, "404": {"description": "- 502: Merchant not found"}}}, "patch": {"summary": "Update Merchant", "tags": ["Merchant"], "parameters": [{"name": "Body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Merchant"}}, {"$ref": "#/parameters/merch_id"}], "responses": {"200": {"description": "Merchant updated", "schema": {"$ref": "#/definitions/MerchantWithCustomer"}}}}}, "/v1/settings": {"get": {"summary": "Get settings", "tags": ["Settings"], "responses": {"200": {"description": "Found settings", "schema": {"$ref": "#/definitions/Settings"}}}}, "patch": {"summary": "Update settings", "tags": ["Settings"], "parameters": [{"name": "Body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Settings"}}], "responses": {"200": {"description": "Updated settings", "schema": {"$ref": "#/definitions/Settings"}}}}}, "/v1/merchant/{merch_id}/customer": {"post": {"description": "Create Customer", "summary": "Create Customer", "tags": ["Customer"], "parameters": [{"name": "Body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CustomerInputData"}}, {"$ref": "#/parameters/merch_id"}], "responses": {"201": {"description": "Customer created", "schema": {"$ref": "#/definitions/CustomerAllData"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/balance/{amount}": {"post": {"description": "Add To Balance", "summary": "Add To Balance", "tags": ["Balance"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"name": "amount", "in": "path", "required": true, "type": "number", "default": 2000, "description": "Add this amount to customer balance"}], "responses": {"200": {"description": "Amount added to balance", "schema": {"$ref": "#/definitions/CustomerAllData"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/freebets/{count}/{coin}": {"post": {"description": "Add To Balance", "summary": "Set Free Bets", "tags": ["Balance"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"name": "count", "in": "path", "required": true, "type": "integer", "default": 10}, {"name": "coin", "in": "path", "required": true, "type": "number", "default": 0.2}], "responses": {"200": {"description": "Free bets added to customer", "schema": {"$ref": "#/definitions/CustomerAllData"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}": {"patch": {"description": "Get Customer", "summary": "Update Customer", "tags": ["Customer"], "parameters": [{"name": "Body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CustomerInputData"}}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/merch_id"}], "responses": {"200": {"description": "Customer updated", "schema": {"$ref": "#/definitions/CustomerAllData"}}}}, "get": {"description": "Get Customer", "summary": "Get Customer", "tags": ["Customer"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}], "responses": {"200": {"description": "Customer found", "schema": {"$ref": "#/definitions/CustomerAllData"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/ticket": {"get": {"description": "Create Ticket", "summary": "Create Ticket", "tags": ["Customer"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}], "responses": {"200": {"description": "Ticket created", "schema": {"$ref": "#/definitions/Ticket"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/error/{action}/raiseType/{raise_type}": {"post": {"summary": "Create Error", "description": "Create custom error. This error will be returned in MAPI on the custom action of this Customer, after that error will be removed. Please note!!! If you create a new error the old will be overwritten", "tags": ["Error"], "parameters": [{"name": "Body", "in": "body", "required": true, "description": "Body of errors", "schema": {"$ref": "#/definitions/Error"}}, {"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/raise_type"}, {"$ref": "#/parameters/action"}], "responses": {"200": {"description": "Error created successful", "schema": {"$ref": "#/definitions/ErrorCreated"}}}}, "delete": {"summary": "Delete Errors", "description": "Delete custom errors for specified action and raise type", "tags": ["Error"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/raise_type"}, {"$ref": "#/parameters/action"}], "responses": {"204": {"description": "Error delete successful", "schema": {"type": "object", "example": {"message": "error deleted"}}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/error/{action}": {"get": {"description": "Get errors created for this customer and this action of both before and after raising types", "summary": "Get error with all raise types (after or before or both)", "tags": ["Error"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/action"}], "responses": {"200": {"description": "Found error", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"description": "Delete error with all raise types (after or before or both)", "summary": "Delete Error", "tags": ["Error"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/action"}], "responses": {"200": {"description": "Error removed", "schema": {"type": "object", "example": {"message": "error deleted"}}}}}}, "/v1/merchant/{merch_id}/error": {"get": {"description": "Get all errors created for this merchant", "summary": "Get Merchant Errors", "tags": ["Error"], "parameters": [{"$ref": "#/parameters/merch_id"}], "responses": {"200": {"description": "Found error", "schema": {"$ref": "#/definitions/ErrorMerchantList"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/error": {"get": {"description": "Get all errors created for this customer", "summary": "Get Customer Errors", "tags": ["Error"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}], "responses": {"200": {"description": "Found errors for customer", "schema": {"$ref": "#/definitions/ErrorCustomerList"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/extra_data/{action}": {"post": {"summary": "Create Extra data", "description": "Create custom extra data. This extra data will be returned in MAPI on the custom action of this Customer, after that extra data will be removed.", "tags": ["Extra data"], "parameters": [{"name": "Body", "in": "body", "required": true, "description": "Fields of Extra message", "schema": {"$ref": "#/definitions/ExtraData"}}, {"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/action"}], "responses": {"200": {"description": "Extra data created successful", "schema": {"$ref": "#/definitions/ExtraDataCreated"}}}}, "get": {"description": "Get extra data created for this customer and this action", "summary": "Get Extra data", "tags": ["Extra data"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/action"}], "responses": {"200": {"description": "Found extra data", "schema": {"$ref": "#/definitions/ExtraData"}}}}, "delete": {"description": "Delete extra data immediately", "summary": "Delete Extra data", "tags": ["Extra data"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}, {"$ref": "#/parameters/action"}], "responses": {"200": {"description": "Extra data removed", "schema": {"type": "object", "example": {"message": "extra data deleted"}}}}}}, "/v1/merchant/{merch_id}/extra_data": {"get": {"description": "Get all extra data created for this merchant", "summary": "Get Merchant Extra data", "tags": ["Extra data"], "parameters": [{"$ref": "#/parameters/merch_id"}], "responses": {"200": {"description": "Found extra data", "schema": {"$ref": "#/definitions/ExtraDataMerchantList"}}}}}, "/v1/merchant/{merch_id}/customer/{cust_id}/extra_data": {"get": {"description": "Get all Extra Data created for this customer", "summary": "Get Customer Extra data", "tags": ["Extra data"], "parameters": [{"$ref": "#/parameters/merch_id"}, {"$ref": "#/parameters/cust_id"}], "responses": {"200": {"description": "Found Extra Data for customer", "schema": {"$ref": "#/definitions/ExtraDataCustomerList"}}}}}, "/v1/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns version, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}, "/v1/health": {"get": {"tags": ["Health"], "summary": "Checks server health", "responses": {"200": {"description": "Health check OK", "schema": {"type": "object", "properties": {"mockType": {"type": "string", "description": "Mock type: ipm or pop", "example": "ipm"}}}}}}}, "/api/register_round": {"put": {"summary": "Register round for ITG migration (IPM MOCK ONLY)", "description": "This route will be called by seamless adapter and should emulate SW360 behaviour", "tags": ["Register round"], "parameters": [{"name": "Body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RegisterRoundRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/RegisterRoundResponseWithErrorCode"}}}}}, "/v1/set/register_round": {"post": {"summary": "Mock register round for ITG migration", "description": "What you will pas here will be provided back as a response on  PUT /v1/register_round, thous register rounds are unique identified by account_id and round_id", "tags": ["Register round"], "parameters": [{"name": "Body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RegisterRoundMock"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/RegisterRoundResponseWithErrorCode"}}}}}}, "parameters": {"raise_type": {"name": "raise_type", "description": "Raise type determines when raise the error: BEFORE or AFTER executing action", "in": "path", "type": "string", "required": true, "default": "BEFORE"}, "merch_id": {"name": "merch_id", "in": "path", "required": true, "type": "string", "description": "Merchant code", "default": "swftest"}, "cust_id": {"name": "cust_id", "in": "path", "required": true, "type": "string", "description": "Customer Unique ID", "default": "Customer123"}, "action": {"name": "action", "description": "Action for which error will be returned:\n ### ipm:\n * validate_ticket \n * get_balance \n * rollback \n * debit \n * credit \n * finish_round_with_statistics\n \n### pop:\n \n * verifyplayersession \n * getplayerinfo \n * bet \n * win \n * canceltransactions \n * getplayerbalance \n * createbrokengame \n \n### poker-star\n\n * startgame \n * getbalance \n * bet \n * handresult \n * finalizedhandresult  \n \n### relax\n\n * verifytoken \n * getbalance \n * credit \n * debit \n * rollback \n \n###  everymatrix\n\n * authenticate \n * getbalance \n * win \n * bonuswin \n * bet \n * cancel \n * roundinfo \n \n###  pariplay\n\n * authenticate \n * getbalance \n * debit \n * credit \n * cancelbet\n\n ### gvc\n\n * verifytoken\n * getbalance\n * makepayment\n * cancelbet\n\n ### MrGreen\n\n * getaccount\n * wager\n * result\n * cancelwager\n * getbalance  \n\n ### BetVictor\n\n * authenticate\n * account-details\n * place-bet\n * settle-bet\n additional-winnings\n * rollback-bet \n\n ### Sisal\n\n * sessionBalance\n * wager\n * endwager\n * cancelwager \n\n ### ISoftBet\n\n * init\n * balance\n * bet\n * win\n * cancel\n * end\n * rounddetails\n\n ### SoftSwiss\n\n * bet\n * win\n * rollback\n * getbalance \n * freespins", "in": "path", "type": "string", "required": true}}, "definitions": {"Settings": {"example": {"aroundAmount": true, "decreaseLoad": false, "notSaveAnyData": false, "amount": 1000000, "addNickname": true, "useTestPlayers": false}, "type": "object", "properties": {"aroundAmount": {"description": "Option rules how IPM-mock working with balance.By default balance can be only in major units (MIN= 0.01), all other amounts around.If set \"false\" all amount will calculate without around", "example": true, "type": "boolean", "default": true}, "decreaseLoad": {"description": "If \"true\" IPM-mock disables some checks to decrease the load on IPM-mock for Performance Test. Default value \"false\" can be changed in Environment variable DECREASE_LOAD", "example": false, "type": "boolean", "default": false}, "amount": {"description": "Customer default amount value. Default value 1000000 can be changed in Environment variable MOCK_SETTINGS_CUSTOMER_AMOUNT", "example": false, "type": "number", "default": false}, "notSaveAnyData": {"description": "If \"true\" we strong decreasing load on IPM-mock for Performance Test. Default value \"false\" can be changed in Environment variable NOT_SAVE_ANY_DATA", "example": false, "type": "boolean", "default": false}, "addNickname": {"description": "If \"true\" IPM-mock adds to validate_ticket response 'nickname'. Default value \"false\" can be changed in Environment variable ADD_NICKNAME", "example": false, "type": "boolean", "default": false}, "useTestPlayers": {"description": "If \"true\" IPM-mock adds to validate_ticket response 'test_cust'. Default value \"false\" can be changed in Environment variable USE_TEST_PLAYERS", "example": false, "type": "boolean", "default": false}}, "required": ["aroundAmount"]}, "RegisterRound": {"example": {"game_code": "itg_xx", "total_bet": 100, "total_win": 50, "bet_count": 1, "win_count": 1, "started_at": 1680081652, "round_id": "1234"}, "type": "object", "properties": {"game_code": {"description": "Game code.", "example": true, "type": "string", "default": "itg_xxx"}, "total_bet": {"description": "Total sum of bets that was done in the round in major units format", "example": true, "type": "number", "default": 100}, "total_win": {"description": "Total sum of wins that was done in the round in major units format (optional)", "example": true, "type": "number", "default": 40}, "bet_count": {"description": "Total number of bets that was done in the round.(optional)", "example": true, "type": "number", "default": 1}, "win_count": {"description": "Total number of bets that was done in the round. (optional)", "example": true, "type": "number", "default": 1}, "started_at": {"description": "UNIX epoch  timestamp in milliseconds when the round was started. Should be in UTC timezone.", "example": true, "type": "number", "default": 1}, "round_id": {"description": "Provider round_id", "example": true, "type": "string", "default": "4123"}}}, "RegisterRoundResponse": {"allOf": [{"$ref": "#/definitions/RegisterRound"}, {"type": "object", "example": {"cust_id": "SW-360_Bob1000", "merch_id": "1999", "merch_password": "***"}, "properties": {"cust_id": {"description": "Account if of the player in the SW360 system.", "example": true, "type": "string", "default": "SW-360_Bob1000"}, "merch_id": {"description": "Merchant ID. For authentication/authorization purpose", "example": true, "type": "string", "default": "1999"}, "merch_password": {"description": "Merchant Password. For authentication/authorization purpose", "example": true, "type": "string", "default": "***"}}}]}, "RegisterRoundRequest": {"allOf": [{"$ref": "#/definitions/RegisterRound"}, {"type": "object", "example": {"account_id": "Bob1000"}, "properties": {"account_id": {"description": "External system player id", "example": true, "type": "string", "default": "SW-Bob1000"}}}]}, "RegisterRoundMock": {"allOf": [{"$ref": "#/definitions/RegisterRoundResponse"}, {"type": "object", "example": {"account_id": "Bob1000"}, "properties": {"account_id": {"description": "External system player id", "example": true, "type": "string", "default": "SW-Bob1000"}}}]}, "RegisterRoundResponseWithErrorCode": {"allOf": [{"$ref": "#/definitions/RegisterRoundResponse"}, {"type": "object", "example": {"error_code": 0}, "properties": {"error_code": {"description": "Error code, 0 means  everything ok", "example": true, "type": "number", "default": 0}}}]}, "Merchant": {"example": {"merch_id": "swftest", "merch_pwd": "qwerty123", "isPromoInternal": false, "multiple_session": false}, "type": "object", "properties": {"merch_id": {"description": "Merchant code", "example": "swftest", "type": "string"}, "merch_pwd": {"description": "Merchant password", "example": "qwerty123", "type": "string"}, "isPromoInternal": {"description": "Optional boolean flag, which show where promos handle in our MAPI or in Merchant system", "example": false, "type": "boolean", "default": false}, "multiple_session": {"description": "Optional boolean flag, which requires any previous sessions for this customer to be killed.", "example": false, "type": "boolean", "default": false}}, "required": ["merch_id", "merch_pwd"]}, "MerchantWithCustomer": {"allOf": [{"$ref": "#/definitions/Merchant"}, {"type": "object", "example": {"customers": {"Customer123": {"cust_id": "Customer123", "currency_code": "USD", "language": "en", "country": "US", "test_cust": false, "status": "normal", "bet_limit": null}, "customer2": {"cust_id": "customer2", "currency_code": "CNY", "language": "en", "country": "US", "test_cust": false, "status": "normal", "bet_limit": null}}}, "properties": {"customers": {"$ref": "#/definitions/CustomerList"}}}]}, "MerchantList": {"properties": {"swftest": {"$ref": "#/definitions/MerchantWithCustomer"}}}, "CustomerInputData": {"title": "Customer Input Data", "example": {"cust_id": "Customer123", "cust_login": "PLAYER1", "currency_code": "USD", "language": "en", "country": "US", "test_cust": false, "status": "normal", "bet_limit": null, "first_name": "", "last_name": "", "email": "", "jurisdiction": null}, "type": "object", "properties": {"cust_id": {"description": "Customer Unique ID", "example": "Customer123", "type": "string"}, "cust_login": {"example": "PLAYER1", "type": "string"}, "country": {"type": "string", "description": "country code [ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1)", "example": "US"}, "currency_code": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "language": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string"}, "test_cust": {"description": "Flag for test customers", "type": "boolean", "example": true}, "status": {"description": "Customer status", "type": "string", "example": "noraml", "enum": ["normal", "suspended"]}, "bet_limit": {"description": "Max amount of one bet", "type": "number", "example": null}, "jurisdiction": {"description": "Jurisdiction if needed", "type": "string", "example": null}}, "required": ["cust_id", "currency_code", "language", "country"]}, "CustomerAllData": {"allOf": [{"$ref": "#/definitions/CustomerInputData"}, {"title": "Customer all data with balance", "example": {"balance": {"amount": 100, "currency_code": "USD"}, "freeBets": {"count": 10, "coin": 0.5}}, "type": "object", "properties": {"balance": {"$ref": "#/definitions/Balance"}, "freeBets": {"$ref": "#/definitions/freeBets"}}, "required": ["balance"]}]}, "CustomerList": {"properties": {"cust_id": {"$ref": "#/definitions/CustomerAllData"}}}, "Balance": {"title": "Balance", "example": {"amount": 0, "currency_code": "USD"}, "type": "object", "properties": {"amount": {"description": "Amount/Balance", "example": 100, "type": "integer", "format": "int32"}, "currency_code": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}}, "required": ["amount", "currency_code"]}, "freeBets": {"title": "Free bets", "example": {"coin": 0.5, "count": 10}, "type": "object", "properties": {"count": {"description": "Count of free bets", "example": 10, "type": "integer", "format": "int32"}, "coin": {"type": "number", "description": "Amount of coin", "example": 0.5}}, "required": ["count", "coin"]}, "Ticket": {"title": "Ticket", "example": {"ticket": "TICKET-293746023"}, "type": "object", "properties": {"ticket": {"description": "Ticket for play", "example": "Ticket-293746023", "type": "integer", "format": "string"}}, "required": ["ticket"]}, "Error": {"example": [{"responseStatus": 200, "errorCode": -1502, "errorMsg": "Player has reached his deposit limit", "extraData": "Extra Data"}, {"responseStatus": 200, "errorCode": -1505, "errorMsg": "Reality check error", "extraData": "Extra Data"}, {"responseStatus": 200, "errorCode": -1509, "errorMsg": "The bet amount you selected exceeds your loss limit for this week", "extraData": "Extra Data"}, {"responseStatus": 400, "code": 9999, "message": "Sisal error", "extraData": "Extra Data"}], "type": "object", "properties": {"error_code": {"description": "Custom number code of error or '@xsi:type' of error for PokerStar", "example": "TemporaryUnavailable", "type": "string"}, "http_response_status": {"description": "Custom HTTP Response Status Code", "example": [200, 400, 500], "type": "integer"}, "error_msg": {"description": "Custom error message", "example": "My custom error", "type": "string"}, "custom_field": {"description": "Can add any custom field to error", "example": "Some additional data to error", "type": "string"}}, "required": ["error_code", "http_response_status", "error_msg"]}, "ErrorCreated": {"type": "object", "properties": {"message": {"type": "string", "default": "error created", "example": "error created"}, "error": {"$ref": "#/definitions/Error"}}}, "ErrorCustomerList": {"type": "object", "properties": {"action": {"$ref": "#/definitions/Error"}}}, "ErrorMerchantList": {"type": "object", "properties": {"cust_id": {"$ref": "#/definitions/ErrorCustomerList"}}}, "ExtraData": {"example": {"accountBalance": {"messageArray": [{"msgType": "Message", "accountMsg": "Bonus issued! Wishing you the best of luck in our games! - POP", "nonIntrusive": false}]}, "messages": [{"msgType": "message", "message": "Test CMA Message - IPM", "nonIntrusive": false, "title": "CMA"}]}, "type": "object", "properties": {"custom_field": {"description": "Can add any extra field to response", "example": "Some additional extra data", "type": "string"}, "extra_data": {"description": "Some exra data", "example": "Some additional extra data2", "type": "string"}}}, "ExtraDataCreated": {"type": "object", "properties": {"message": {"type": "string", "default": "Extra data created", "example": "Extra data created"}, "extra_data": {"$ref": "#/definitions/ExtraData"}}}, "ExtraDataCustomerList": {"type": "object", "properties": {"action": {"$ref": "#/definitions/ExtraData"}}}, "ExtraDataMerchantList": {"type": "object", "properties": {"cust_id": {"$ref": "#/definitions/ExtraDataCustomerList"}}}}}