import { Customer, getById, Merchant } from "../../models/merchant";
import { NextFunction, Request, Response } from "express";
import {
    ApiKeyNotFound,
    BetVictorError404,
    InvalidJurisdictionError,
    JurisdictionNotSetError,
    OperatorNotFoundError,
    OperatorNotSetError,
    SessionNotFound,
    WrongTokenFormatError
} from "./errors";
import * as MerchantService from "../../service/merchant";
import * as CustomerService from "../../service/customer";
import { BET_VICTOR_TOKEN, BET_VICTOR_JURISDICTION, BET_VICTOR_MOCK_TYPE } from "./entities";
import { Ticket, tickets } from "../../models/ticket";
import { BetVictorService } from "./service";

export interface RequestAdditionalInfo {
    token: string;
    ticket: Ticket;
    merchant: Merchant;
    customer: Customer;
    operatorId: string;
    jurisdiction: string;
    mockType: BET_VICTOR_MOCK_TYPE;
}

export const BetVictorConfig = {
    API_KEY: "BET_VICTOR_API_KEY"
};

export function authoriseMerchant(req: Request & RequestAdditionalInfo,
                                  res: Response,
                                  next: NextFunction) {
    req.token = req.body.sessionId || req.body.authToken;

    if (req.body.apiKey !== BetVictorConfig.API_KEY) {
        throw new ApiKeyNotFound();
    }

    if (!req.token) {
        throw new SessionNotFound();
    }

    if (req.token.startsWith(BET_VICTOR_TOKEN.NORMAL)) {
        req.mockType = BET_VICTOR_MOCK_TYPE.MockEnginePredefined;

        // Predefined player
        const parts = req.token.split("_");
        if (parts.length !== 5) {
            throw new WrongTokenFormatError();
        }
        const operatorId = parts[1];
        const jurisdiction = parseJurisdiction(parts[2]);
        const currency = parts[3];
        const country = parts[4];

        if (!operatorId) {
            throw new OperatorNotSetError();
        }
        let merchant = getById(operatorId);

        if (!merchant) {
            merchant = MerchantService.createMerchant(operatorId, {
                merch_id: operatorId,
                merch_pwd: "qwerty123",
                isPromoInternal: "false",
                multiple_session: "false"
            });
        }

        if (!merchant.customers[req.token]) {
            const customer = CustomerService.createCustomer(merchant, {
                currency_code: currency,
                cust_id: req.token,
                jurisdiction: jurisdiction,
                first_name: "firstName",
                last_name: "lastName",
                country: country
            });

            tickets[req.token] = {
                id: req.token,
                merchantId: merchant.merch_id,
                custId: req.token,
                creationTime: new Date().getTime()
            };

            customer.balance = {
                amount: 100000,
                currency_code: customer.currency_code
            };
        }

        req.merchant = merchant;
        req.operatorId = operatorId;
        req.jurisdiction = jurisdiction;
        req.ticket = tickets[req.token];
    } else {
        req.mockType = BET_VICTOR_MOCK_TYPE.MockEngine;
        req.ticket = BetVictorService.authorizePlayer(req.token);
        if (!req.ticket) {
            throw new SessionNotFound();
        }
        req.merchant = getById(req.ticket.merchantId);
        if (!req.merchant) {
            throw new OperatorNotFoundError();
        }
        req.operatorId = req.merchant.merch_id;
    }

    return next();
}

export function authorisePlayer(req: Request & RequestAdditionalInfo,
                                res: Response,
                                next: NextFunction) {

    const ticket = BetVictorService.authorizePlayer(req.token);

    req.ticket = ticket;
    req.customer = req.merchant.customers[ticket.custId];
    if (!req.customer) {
        throw new SessionNotFound();
    }
    if (!req.jurisdiction && req.customer.jurisdiction) {
        req.jurisdiction = req.customer.jurisdiction;
    }
    if (!req.jurisdiction) {
        throw new JurisdictionNotSetError();
    }
    if (req.jurisdiction !== BET_VICTOR_JURISDICTION.UK &&
        req.jurisdiction !== BET_VICTOR_JURISDICTION.GIB &&
        req.jurisdiction !== BET_VICTOR_JURISDICTION.IOM) {
        throw new InvalidJurisdictionError();
    }
    return next();
}

function parseJurisdiction(jurisdictionInput: string): BET_VICTOR_JURISDICTION {
    if (jurisdictionInput.toLowerCase() === BET_VICTOR_JURISDICTION.UK.toLowerCase()) {
        return BET_VICTOR_JURISDICTION.UK;
    } else if (jurisdictionInput.toLowerCase() === BET_VICTOR_JURISDICTION.GIB.toLowerCase()) {
        return BET_VICTOR_JURISDICTION.GIB;
    } else if (jurisdictionInput.toLowerCase() === BET_VICTOR_JURISDICTION.IOM.toLowerCase()) {
        return BET_VICTOR_JURISDICTION.IOM;
    }

    throw new JurisdictionNotSetError();
}
