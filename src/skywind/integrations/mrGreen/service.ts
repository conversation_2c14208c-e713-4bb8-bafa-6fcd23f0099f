import {
    BaseRequest,
    CancelWagerRequest, CancelWagerResponse, GameClientType,
    GetAccountResponse, GetBalanceRequest, GetBalanceResponse,
    MR_GREEN_ACTION,
    ResultRequest, ResultResponse,
    WagerRequest, WagerResponse,
} from "./entities";

import { Ticket, tickets } from "../../models/ticket";
import * as Transaction from "../../models/transaction";
import { WALLET_ACTION } from "../../models/transaction";

import { throwCustomError } from "../../service/customError";
import {
    AuthenticationFailure, BadArgument,
    RollbackFailure
} from "./errors";
import { Request } from "express";
import { RequestAdditionalInfo } from "./middleware";
import { extraDataHandler } from "../../service/extraData";
import { Customer, Merchant } from "../../models/merchant";

export interface MrGreenWalletApi {
    getAccount(request: BaseRequest): Promise<GetAccountResponse>;
    wager(request: WagerRequest): Promise<WagerResponse>;
    result(request: ResultRequest): Promise<ResultResponse>;
    cancelWager(request: CancelWagerRequest): Promise<CancelWagerResponse>;
    getBalance(request: GetBalanceRequest): Promise<GetBalanceResponse>;
}

export class MrGreenService implements MrGreenWalletApi {
    private defaultCurrencyRate: number = 1;
    constructor(public merchant: Merchant, public customer: Customer, private req: Request & RequestAdditionalInfo) {
    }

    public static authorizePlayer(gameToken: string): Ticket {
        const ticket = tickets[gameToken];
        if (!ticket) {
            throw new AuthenticationFailure();
        }
        return ticket;
    }

    private toFixedPrecise(number: number) {
        return Number((number).toFixed(5));
    }

    @throwCustomError({ action: MR_GREEN_ACTION.GET_ACCOUNT })
    @extraDataHandler({ action: MR_GREEN_ACTION.GET_ACCOUNT })
    public async getAccount(request: BaseRequest): Promise<GetAccountResponse> {
        return {
            sessionId: request.sessionid,
            accountId: this.req.ticket.custId,
            currency: this.req.customer.currency_code,
            country: this.req.customer.country,
            details: {
                sex: "M",
                city: this.req.customer.country,
                firstName: this.req.customer.first_name,
                lastName: this.req.customer.last_name
            }
        };
    }

    @throwCustomError({ action: MR_GREEN_ACTION.GET_BALANCE })
    @extraDataHandler({ action: MR_GREEN_ACTION.GET_BALANCE })
    public async getBalance(request: GetBalanceRequest): Promise<GetBalanceResponse> {

        return {
            sessionId: request.sessionid,
            balance: this.toFixedPrecise(this.req.customer.balance.amount),
            details: {
                realBalance: this.toFixedPrecise(this.req.customer.balance.amount),
                bonusBalance: 0,
            }
        };
    }

    @throwCustomError({ action: MR_GREEN_ACTION.BET })
    @extraDataHandler({ action: MR_GREEN_ACTION.BET })
    public async wager(request: WagerRequest): Promise<WagerResponse> {
        if (request.gameclienttype !== GameClientType.HTML) {
            throw new BadArgument("GameClientType is not HTML");
        }

        if (request.accountid !== this.req.customer.cust_id) {
            throw new BadArgument("AccountId is wrong");
        }

        const action = WALLET_ACTION.debit;
        const transcationId = request.transactionid.toString();
        const transaction = Transaction.getById(transcationId, action);

        if (!transaction) {
            Transaction.setById(transcationId, action, Math.abs(request.betamount), this.req.customer.cust_id);
            this.req.customer.balance.amount = this.req.customer.balance.amount + Math.abs(request.betamount) * action;
        }

        return {
            sessionId: request.sessionid,
            balance: this.toFixedPrecise(this.req.customer.balance.amount),
            walletTransactionId: +transcationId,
            alreadyProcessed: (!transaction),
            details: {
                realBalance: this.toFixedPrecise(this.req.customer.balance.amount),
                bonusBalance: 0,
                baseCurrencyRate: this.defaultCurrencyRate,
                baseCurrency: this.req.customer.currency_code,
                realMoneyBet: request.betamount,
                bonusMoneyBet: 0
            }
        };
    }

    @throwCustomError({ action: MR_GREEN_ACTION.CANCEL })
    @extraDataHandler({ action: MR_GREEN_ACTION.CANCEL })
    public async cancelWager(request: CancelWagerRequest): Promise<CancelWagerResponse> {
        const transactionId = request.transactionid.toString();

        const rollbackTransaction = Transaction.getById(transactionId, WALLET_ACTION.rollback);

        if (!rollbackTransaction) {
            const originalTransaction = Transaction.getById(transactionId, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new RollbackFailure();
            }

            const [, amount, , isRollback] = originalTransaction;

            if (!isRollback) {
                Transaction.rollbackById(request.transactionid.toString(), WALLET_ACTION.debit);
                this.req.customer.balance.amount = this.req.customer.balance.amount - amount;
            }

            Transaction.setById(transactionId, WALLET_ACTION.rollback, 0, this.req.customer.cust_id);

            return {
                sessionId: request.sessionid,
                transactionId: request.transactionid,
                balance: this.toFixedPrecise(this.req.customer.balance.amount),
                walletTransactionId: request.transactionid,
                alreadyProcessed: isRollback
            };
        } else {
            return {
                sessionId: request.sessionid,
                transactionId: request.transactionid,
                balance: this.toFixedPrecise(this.req.customer.balance.amount),
                walletTransactionId: request.transactionid,
                alreadyProcessed: true
            };
        }
    }

    @throwCustomError({ action: MR_GREEN_ACTION.WIN })
    @extraDataHandler({ action: MR_GREEN_ACTION.WIN })
    public async result(request: ResultRequest): Promise<ResultResponse> {
        const action = WALLET_ACTION.credit;
        const transactionId = request.transactionid.toString();
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action, Math.abs(request.result), this.req.customer.cust_id);
            this.req.customer.balance.amount = this.req.customer.balance.amount + Math.abs(request.result) * action;
        }

        return {
            walletTransactionId: +transactionId,
            alreadyProcessed: (!transaction),
            balance: this.toFixedPrecise(this.req.customer.balance.amount),
            sessionId: request.sessionid,
            details: {
                realBalance: this.toFixedPrecise(this.req.customer.balance.amount),
                bonusBalance: 0,
                baseCurrencyRate: this.defaultCurrencyRate,
                baseCurrency: this.req.customer.currency_code
            }
        };
    }
}
