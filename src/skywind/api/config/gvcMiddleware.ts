import { NextFunction, Request, Response, Router } from "express";
import { GVCExtraRequestData } from "../../entities/gvc";
import * as Merchants from "../../models/merchant";
import { Merchant, Customer } from "../../models/merchant";
import { Ticket } from "../../models/ticket";
import { getGVCTicket } from "../../service/ticket";
import config from "../../config";
import * as gvcErrors from "../../gvcErrors";

export const authenticate = (req: Request & GVCExtraRequestData, res: Response, next: NextFunction) => {
    const accountId: string = req.body.accountId;
    const [merchantId, playerId] = accountId.split("__");

    const merchant: Merchant = Merchants.getById(merchantId, false);
    if (!merchant) {
        return next(new gvcErrors.InvalidAccountIdError());
    }

    const customer: Customer = merchant.customers[playerId];
    if (!customer) {
        return next(new gvcErrors.InvalidAccountIdError());
    }

    req.merchant = merchant;
    req.customer = customer;

    if (req.body.secureToken) {
        const tokenData: Ticket = getGVCTicket(req.body.secureToken, config.expirationTime.ticket);
        req.tokenData = tokenData;
    }

    next();
};
