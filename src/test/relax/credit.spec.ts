import { suite, test } from "mocha-typescript";
const request = require("supertest");
import * as uuid from "uuid";
import { expect } from "chai";
import { getApplication } from "../../skywind/relaxServer";
import { Merchant, setAll } from "../../skywind/models/merchant";
import { getRelaxService } from "../../skywind/service/relax";
import * as Transaction from "../../skywind/models/transaction";
import config from "../../skywind/config";
import { getTransactionKey } from "../../skywind/models/transaction";
import { WALLET_ACTION } from "../../skywind/models/transaction";
import { RelaxBaseSpec } from "./base.spec";
import { INTEGRATION_VERSION} from "../../config/integrationVersion";

@suite
class RelaxCreditSpec extends RelaxBaseSpec {
    @test
    public async authFail() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/deposit`)
            .auth("invalid", "invalid")
            .send({})
            .expect(401);

        expect(response.body.errorcode).to.be.equal("UNAUTHORIZED");
    }

    @test
    public async merchantNotFound() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/11/deposit`)
            .auth(config.auth.username, config.auth.password)
            .send({})
            .expect(500);

        expect(response.body.errorcode).to.be.equal("UNHANDLED");
    }

    @test
    public async requiredParams() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/deposit`)
            .auth(config.auth.username, config.auth.password)
            .send({})
            .expect(400);

        expect(response.body.errorcode).to.be.equal("INVALID_PARAMETERS");
    }

    @test
    public async playerNotFound() {
        const debitRequest = {
            "amount": 100,
            "channel": "web",
            "clientid": "123",
            "currency": "EUR",
            "ended": false,
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "playerid": "invalidplayer",
            "roundid": "20002",
            "sessionid": this.sessionId,
            "txid": "PQ+OAAAChlsAAALXPQ+OAJH5+v=deposit",
            "txtype": "deposit"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/deposit`)
            .auth(config.auth.username, config.auth.password)
            .send(debitRequest)
            .expect(403);

        expect(response.body.errorcode).to.be.equal("SESSION_EXPIRED");
    }

    @test
    public async sessionNotFound() {
        const debitRequest = {
            "amount": 100,
            "channel": "web",
            "clientid": "123",
            "currency": "EUR",
            "ended": false,
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "playerid": this.playerCode,
            "roundid": "20002",
            "sessionid": "invalid",
            "txid": "PQ+OAAAChlsAAALXPQ+OAJH5+v=deposit",
            "txtype": "deposit"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/deposit`)
            .auth(config.auth.username, config.auth.password)
            .send(debitRequest)
            .expect(403);

        expect(response.body.errorcode).to.be.equal("SESSION_EXPIRED");
    }

    @test
    public async successDeposit() {
        const txid = "PQ+OAAAChlsAAALXPQ+OAJH5+v=withdraw";
        const debitRequest = {
            "amount": 100,
            "channel": "web",
            "clientid": "123",
            "currency": "USD",
            "ended": false,
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "playerid": this.playerCode,
            "roundid": "20002",
            "sessionid": this.sessionId,
            txid,
            "txtype": "deposit"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/deposit`)
            .auth(config.auth.username, config.auth.password)
            .send(debitRequest)
            .expect(200);

        expect(response.body.txid).to.be.equal(txid);
        expect(response.body.sessionid).to.be.equal(this.sessionId);
        expect(response.body.balance).to.be.equal(config.defaultSettings.amount + 100);
    }

    @test
    public async depositShouldBeIdempotency() {
        const txid = "PQ+OAAAChlsAAALXPQ+OAJH5+v=withdraw";
        const externalId = uuid.v4();
        Transaction.setAll({
            [getTransactionKey(txid, WALLET_ACTION.credit)]: [txid, -100, externalId, true]
        });

        const debitRequest = {
            "amount": 100,
            "channel": "web",
            "clientid": "123",
            "currency": "USD",
            "ended": false,
            "gameref": "rlx.sw.sw.sw_mrmnky",
            "playerid": this.playerCode,
            "roundid": "20002",
            "sessionid": this.sessionId,
            txid,
            "txtype": "deposit"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/deposit`)
            .auth(config.auth.username, config.auth.password)
            .send(debitRequest)
            .expect(200);

        expect(response.body.txid).to.be.equal(txid);
        expect(response.body.sessionid).to.be.equal(this.sessionId);
        expect(response.body.balance).to.be.equal(config.defaultSettings.amount);
        expect(response.body.relaxid).to.be.equal(externalId);
    }
}
