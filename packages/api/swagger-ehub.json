{"swagger": "2.0", "info": {"description": "Skywind EHUB API", "version": "5.56", "title": "Skywind EHUB API"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-access-token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {"entity": "Entity management", "entity:view": "View entity", "entity:game": "Manage game", "entity:game:view": "View game", "keyentity:game": "Manage game", "keyentity:game:view": "View game", "user": "User management", "user:view": "View entity's users"}}}, "paths": {"/login": {"post": {"tags": ["User"], "summary": "Logs user in", "description": "Logs user in by entity's secret key and credentials", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["secret<PERSON>ey", "username", "password"], "properties": {"secretKey": {"type": "string", "description": "secret key", "example": "key"}, "username": {"type": "string", "description": "username", "example": "USER1"}, "password": {"type": "string", "format": "password", "description": "password", "example": "123456qaB"}}}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 998: <PERSON>gin Failed\n", "schema": {"$ref": "#/definitions/Error"}}, "409": {"description": "- 753: Two factor auth code has been sent recently. Repeat attempt a little later\n"}, "500": {"description": "- 719: An error occurred when sending sms\n- 720: An error occurred when sending email\n"}}}}, "/login/refresh": {"post": {"security": [{"apiKey": []}, {"Permissions": []}], "tags": ["User"], "summary": "Refreshes access token before expiration", "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/logout": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "parameters": [{"name": "deleteSessions", "in": "query", "description": "Delete all user sessions", "required": false, "type": "boolean"}], "summary": "Logs user out", "description": "Logs user out by ending his session.", "responses": {"204": {"description": "Log out successfully executed"}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n - 204: Access token error\n- 205: Access Token has expired\n- 792: Access Session is expired\n"}}}}, "/entities/{path}/users": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:view"]}], "tags": ["User"], "summary": "Finds users under the key entity tree by path", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/userIdIn"}, {"$ref": "#/parameters/username__equal"}, {"$ref": "#/parameters/username__contains"}, {"$ref": "#/parameters/username__contains!"}, {"$ref": "#/parameters/username__in"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/entity__equal"}, {"$ref": "#/parameters/entity__contains"}, {"$ref": "#/parameters/entity__contains!"}, {"$ref": "#/parameters/entity__in"}, {"$ref": "#/parameters/fullTextSearchQuery"}, {"$ref": "#/parameters/fullTextSearchFields"}, {"$ref": "#/parameters/userType"}, {"$ref": "#/parameters/roleId__in"}, {"$ref": "#/parameters/roleId__in!"}, {"$ref": "#/parameters/customData"}, {"$ref": "#/parameters/customDataRoleId__in"}, {"$ref": "#/parameters/customDataRoleId__in!"}], "responses": {"200": {"description": "List of users", "schema": {"type": "array", "items": {"$ref": "#/definitions/DetailedUserInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/games": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:view"]}], "tags": ["Game"], "summary": "Gets list of entity's game by path", "description": "Gets games for entity, filtered by labels or category. Case sortBy == categoryList returns games from included list of category before others.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/typeStrictEquality"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/gameProviderCodeEquality"}, {"$ref": "#/parameters/gameProviderCode__in"}, {"$ref": "#/parameters/gameProviderTitleEquality"}, {"$ref": "#/parameters/gameProviderTitleContains"}, {"$ref": "#/parameters/gameProviderTitleNotContains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/isMarketplaceSupported"}, {"$ref": "#/parameters/isCustomLimitsSupported"}, {"$ref": "#/parameters/decreaseMaxBetSupported"}, {"$ref": "#/parameters/increaseMinBetSupported"}, {"$ref": "#/parameters/limitFiltersWillBeApplied"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/shortInfo"}, {"$ref": "#/parameters/limitsCurrencyInQuery"}, {"$ref": "#/parameters/schemaDefinitionIdInQuery"}, {"$ref": "#/parameters/physicalTableIdStrictEquality"}, {"$ref": "#/parameters/physicalTableIdIn"}, {"$ref": "#/parameters/excludeInactiveGames"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/games": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets list of key entity's game", "description": "Gets games for entity, filtered by labels or category. Case sortBy == categoryList returns games from included list of category before others.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/code__in"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/gameProviderCodeEquality"}, {"$ref": "#/parameters/gameProviderCode__in"}, {"$ref": "#/parameters/gameProviderTitleEquality"}, {"$ref": "#/parameters/gameProviderTitleContains"}, {"$ref": "#/parameters/gameProviderTitleNotContains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/isMarketplaceSupported"}, {"$ref": "#/parameters/isCustomLimitsSupported"}, {"$ref": "#/parameters/decreaseMaxBetSupported"}, {"$ref": "#/parameters/increaseMinBetSupported"}, {"$ref": "#/parameters/limitFiltersWillBeApplied"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/shortInfo"}, {"$ref": "#/parameters/limitsCurrencyInQuery"}, {"$ref": "#/parameters/schemaDefinitionIdInQuery"}, {"$ref": "#/parameters/physicalTableIdStrictEquality"}, {"$ref": "#/parameters/physicalTableIdIn"}, {"$ref": "#/parameters/typeStrictEquality"}, {"$ref": "#/parameters/excludeInactiveGames"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/short-structure": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/includeJurisdiction"}, {"$ref": "#/parameters/additionalFields"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities short structure of the related key used by path", "responses": {"200": {"description": "Entity full hierarchy with short info", "schema": {"$ref": "#/definitions/EntityShortInfo"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/short-structure": {"get": {"parameters": [{"$ref": "#/parameters/includeJurisdiction"}, {"$ref": "#/parameters/additionalFields"}], "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities short structure of the related key used", "responses": {"200": {"description": "Entity full hierarchy with short info", "schema": {"$ref": "#/definitions/EntityShortInfo"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/settings": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity settings by path", "parameters": [{"$ref": "#/parameters/onlyOwnSettings"}, {"$ref": "#/parameters/extendSettings"}], "responses": {"200": {"description": "Entity settings", "schema": {"$ref": "#/definitions/EntitySettings"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}, "definitions": {"$ref": "./mapi-swagger/definitions.json"}, "parameters": {"$ref": "./mapi-swagger/parameters.json"}}