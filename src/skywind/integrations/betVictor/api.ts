import { NextFunction, Request, Response, Router } from "express";

import { isEmpty } from "../../utils/isEmpty";
import { EmptyFieldError } from "./errors";
import { authoriseMerchant, RequestAdditionalInfo } from "./middleware";
import { settings } from "../../models/settings";
import {
    accountDetailsResponder,
    additionalWinningsResponder,
    authenticateResponder,
    placeBetResponder,
    additionalBetResponder,
    rollbackBetResponder,
    settleBetResponder,
    promoCreditResponder
} from "./responders";
import { authorisePlayer } from "./middleware";

import { BetVictorService } from "./service";
import logger from "../../utils/logger";
import { INTEGRATION_TYPE } from "../../../config/integrationType";
import { routerCallbackWrapper } from "../../api/config/middleware";

const router: Router = Router();
const log = logger(`${INTEGRATION_TYPE.BET_VICTOR}:api`);

function validateFieldsForEmpty(...fields: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        for (const param of fields) {
            const value = req.body[param];
            if (isEmpty(value)) {
                return next(new EmptyFieldError(`[${param}] is required`));
            }
        }
        return next();
    };
}

function notSaveAnyDataMiddleware(responder: (req: Request & RequestAdditionalInfo) => object) {
    return (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req));
            } catch (err) {
                return next(err);
            }
        }
        return next();
    };
}

// authenticate player
router.post("/authenticate",
    notSaveAnyDataMiddleware(authenticateResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "authToken"),
    routerCallbackWrapper((req: Request & RequestAdditionalInfo,
                           res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.authenticate(req.body);
        res.send(result);
        next();
    }));

// get balance and account details
router.post("/account-details",
    notSaveAnyDataMiddleware(accountDetailsResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "sessionId"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.getAccountDetails(req.body);
        res.send(result);
        next();
    });

// bet
router.post("/place-bet",
    notSaveAnyDataMiddleware(placeBetResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "sessionId",
        "clientTransactionId", "clientGamePlayId",
        "betAmount",
        "gameId"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.placeBet(req.body);
        res.send(result);
        next();
    });

router.post("/place-free-bet",
    notSaveAnyDataMiddleware(placeBetResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "sessionId",
        "clientTransactionId", "clientGamePlayId",
        "gameId", "settle"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.placeFreeBet(req.body);
        res.send(result);
        next();
    });

router.post("/additional-bet",
    notSaveAnyDataMiddleware(additionalBetResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "sessionId",
        "clientTransactionId", "clientGamePlayId",
        "betAmount"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.additionalBet(req.body);
        res.send(result);
        next();
    });

// win
router.post("/settle-bet",
    notSaveAnyDataMiddleware(settleBetResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "sessionId",
        "clientTransactionId", "clientGamePlayId",
        "winnings"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.settleBet(req.body);
        res.send(result);
        next();
    });

// additional win
router.post("/additional-winnings",
    notSaveAnyDataMiddleware(additionalWinningsResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "sessionId",
        "clientTransactionId", "clientGamePlayId",
        "winnings"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.additionalWinnings(req.body);
        res.send(result);
        next();
    });

// promo credit
router.post("/promo-credit",
    notSaveAnyDataMiddleware(promoCreditResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey",
        "clientTransactionId", "accountNumber",
        "winnings", "promoInfo"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.promoCredit(req.body);
        res.send(result);
        next();
    });

// rollback
router.post("/rollback-bet",
    notSaveAnyDataMiddleware(rollbackBetResponder),
    authoriseMerchant,
    authorisePlayer,
    validateFieldsForEmpty("apiKey", "sessionId",
        "clientTransactionId", "clientGamePlayId"),
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const betVictorService = new BetVictorService(req.merchant, req.customer, req);
        const result = betVictorService.rollbackBet(req.body);
        res.send(result);
        next();
    });

export default router;
