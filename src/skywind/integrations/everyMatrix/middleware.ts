import { NextFunction, Request, Response } from "express";
import {
    InvalidHash,
    JurisdictionNotSetError,
    OperatorNotFoundError,
    OperatorNotSetError,
    TokenNotFound,
    WrongTokenFormatError
} from "./errors";
import { EvereMatrixSimpleEngineService } from "./servicePredefined";

import { EVERY_MATRIX_JURISDICTION, EVERY_MATRIX_MOCK_TYPE, EVERY_MATRIX_TOKEN, } from "./entities";
import { Customer, getById, Merchant } from "../../models/merchant";
import { EveryMatrixService } from "./service";
import * as MerchantService from "../../service/merchant";

const dateformat = require("dateformat");
const crypto = require("crypto");

export const everyMatrixConfig = {
    swCreds: {
        vendorName: "Skywind",
        password: "testpassword"
    }
};

export enum EveryMatrixMethod {
    Authenticate = "Authenticate",
    GetBalance = "GetBalance",
    Bet = "Bet",
    Win = "Win",
    BonusWin = "BonusWin",
    Cancel = "Cancel",
    Reconciliation = "Reconciliation",
    RoundInfo = "RoundInfo"
}

export enum TokenType {
    Launch,
    Game
}

export interface RequestAdditionalInfo {
    mockType: EVERY_MATRIX_MOCK_TYPE;
    token: string;
    tokenType: TokenType;
    // EVERY_MATRIX_MOCK_TYPE.MockEngine
    merchant: Merchant;
    customer: Customer;
    operatorId: string;
    jurisdiction: string;
    // EVERY_MATRIX_MOCK_TYPE.PredefinedUser
    predefinedUserId: string;
}

export function authoriseMerchant(req: Request & RequestAdditionalInfo,
                                  res: Response,
                                  next: NextFunction) {
    if (req.body.LaunchToken) {
        req.token = req.body.LaunchToken;
        req.tokenType = TokenType.Launch;
    } else if (req.body.Token) {
        req.token = req.body.Token;
        req.tokenType = TokenType.Game;
    }

    if (!req.token) {
        throw new TokenNotFound();
    }

    if ((req.token.startsWith("SimpleEngine:"))) {
        req.mockType = EVERY_MATRIX_MOCK_TYPE.SimpleEngine;
    } else if (req.token.startsWith(EVERY_MATRIX_TOKEN.NORMAL)) {
        if (!EveryMatrixService.checkIfPredefinedErrorToken(req.token)) {
            // if it's not predefined errors we need to parse token and set merchant, operatorId, jurisdiction.
            const parts = req.token.split("_");
            if (parts.length !== 3) {
                throw new WrongTokenFormatError();
            }
            const operatorId = parts[1];
            if (!operatorId) {
                throw new OperatorNotSetError();
            }
            let merchant = getById(operatorId);

            if (!merchant) {
                merchant = MerchantService.createMerchant(operatorId, {
                    merch_id: operatorId,
                    merch_pwd: "qwerty123",
                    isPromoInternal: "false",
                    multiple_session: "false"
                });
            }

            const jurisdiction = parseJurisdiction(parts[2]);

            req.merchant = merchant;
            req.operatorId = operatorId;
            req.jurisdiction = jurisdiction;
        } else {
            const operatorId = "Predefined";

            let merchant = getById(operatorId);

            if (!merchant) {
                merchant = MerchantService.createMerchant(operatorId, {
                    merch_id: operatorId,
                    merch_pwd: "qwerty123",
                    isPromoInternal: "false",
                    multiple_session: "false"
                });
            }

            req.merchant = merchant;
            req.operatorId = operatorId;
            req.jurisdiction = EVERY_MATRIX_JURISDICTION.UK;
        }

        req.mockType = EVERY_MATRIX_MOCK_TYPE.MockEnginePredefined;
    } else {
        const ticket = new EveryMatrixService(null, null).authorizePlayer(req.token);
        const merchant = getById(ticket.merchantId);
        if (!merchant) {
            throw new OperatorNotFoundError();
        }
        req.merchant = merchant;
        req.mockType = EVERY_MATRIX_MOCK_TYPE.MockEngine;
    }

    return next();
}

function parseJurisdiction(jurisdictionInput: string): EVERY_MATRIX_JURISDICTION {
    if (jurisdictionInput.toLowerCase() === EVERY_MATRIX_JURISDICTION.UK.toLowerCase()) {
        return EVERY_MATRIX_JURISDICTION.UK;
    } else if (jurisdictionInput.toLowerCase() === EVERY_MATRIX_JURISDICTION.MT.toLowerCase()) {
        return EVERY_MATRIX_JURISDICTION.MT;
    } else if (jurisdictionInput.toLowerCase() === EVERY_MATRIX_JURISDICTION.FI.toLowerCase()) {
        return EVERY_MATRIX_JURISDICTION.FI;
    }

    throw new JurisdictionNotSetError();
}

/**
 * Authorise for Bet, Wins and so on...
 * @param req
 * @param res
 * @param next
 */
export function authorizePlayer(req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) {
    if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
        const player = new EvereMatrixSimpleEngineService().findPlayerByGameToken(req.body.Token);
        req.body = Object.assign(req.body, { UserId: player.UserId });
        if (!player) {
            throw new TokenNotFound();
        }
    } else if (req.mockType === EVERY_MATRIX_MOCK_TYPE.MockEnginePredefined ||
        req.mockType === EVERY_MATRIX_MOCK_TYPE.MockEngine) {
        const ticket = new EveryMatrixService(req.merchant, null).authorizePlayer(req.body.Token);
        req.customer = req.merchant.customers[ticket.custId];
        if (!req.customer) {
            throw new TokenNotFound();
        }
        if (!req.jurisdiction && req.customer.jurisdiction) {
            req.jurisdiction = req.customer.jurisdiction;
        }
        if (!req.jurisdiction) {
            throw new JurisdictionNotSetError();
        }
    }

    return next();
}

export function validateMethodHash(req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) {
    const methodName = getInvokingApiMethod(req);
    const computedCache = buildMethodHash(methodName);

    if (computedCache !== req.body.Hash) {
        throw new InvalidHash();
    }

    return next();
}

function getInvokingApiMethod(req: Request) {
    if (req.url.indexOf(EveryMatrixMethod.Authenticate) >= 0) {
        return EveryMatrixMethod.Authenticate;
    } else if (req.url.indexOf(EveryMatrixMethod.GetBalance) >= 0) {
        return EveryMatrixMethod.GetBalance;
    } else if (req.url.indexOf(EveryMatrixMethod.Bet) >= 0) {
        return EveryMatrixMethod.Bet;
    } else if (req.url.indexOf(EveryMatrixMethod.BonusWin) >= 0) {
        return EveryMatrixMethod.BonusWin;
    } else if (req.url.indexOf(EveryMatrixMethod.Win) >= 0) {
        return EveryMatrixMethod.Win;
    } else if (req.url.indexOf(EveryMatrixMethod.Cancel) >= 0) {
        return EveryMatrixMethod.Cancel;
    } else if (req.url.indexOf(EveryMatrixMethod.Reconciliation) >= 0) {
        return EveryMatrixMethod.Reconciliation;
    } else if (req.url.indexOf(EveryMatrixMethod.RoundInfo) >= 0) {
        return EveryMatrixMethod.RoundInfo;
    }
}

export function buildMethodHash(methodName: string) {
    const formattedDate: string = dateformat(new Date(), "UTC:yyyy:mm:dd:hh");
    const dataToHash = `${methodName}${formattedDate}${everyMatrixConfig.swCreds.password}`;
    return crypto.createHash("md5").update(dataToHash).digest("hex");
}
