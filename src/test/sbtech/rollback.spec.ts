import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");

import { SBTechBaseSpec } from "./base.spec";
import { WALLET_ACTION } from "../../skywind/models/transaction";
import { SBTECH_ERROR_NAME } from "../../skywind/errorsSBTech";
import * as TransactionModel from "../../skywind/models/transaction";

@suite("SBTech.rollback")
class GVCBalanceSpec extends SBTechBaseSpec {

    @test("should rollback DEBIT transaction")
    public async rollback() {
        const expectedResponse = {
            "balance": 10000
        };

        await request(this.server)
            .post(this.urlPathToDebit)
            .send(this.fixtures.debitRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .post(this.urlPathToRollback)
            .send(this.fixtures.rollbackRequest)
            .expect(200);

        delete actualResponse.body.sessionId;
        delete actualResponse.body.timestamp;
        delete actualResponse.body.trxId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should return the same balance when rollback has already been processed")
    public async testNonProcessedRollback() {
        const expectedResponse = {
            "balance": 10000
        };

        await request(this.server)
            .post(this.urlPathToDebit)
            .send(this.fixtures.debitRequest)
            .expect(200);

        await request(this.server)
            .post(this.urlPathToRollback)
            .send(this.fixtures.rollbackRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .post(this.urlPathToRollback)
            .send(this.fixtures.rollbackRequest)
            .expect(200);

        delete actualResponse.body.sessionId;
        delete actualResponse.body.timestamp;
        delete actualResponse.body.trxId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("throw error when DEBIT transaction not found")
    public async throwErrorWhenDebitTransactionNotFound() {
        const actualResponse = await request(this.server)
            .post(this.urlPathToRollback)
            .send(this.fixtures.rollbackRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.TRANSACTION_DOESNT_EXIST);
    }

    @test("should throw TransactionAlreadyCancelledError")
    public async testTransactionAlreadyCancelled() {
        await request(this.server)
            .post(this.urlPathToDebit)
            .send(this.fixtures.debitRequest)
            .expect(200);

        TransactionModel.rollbackById("1", WALLET_ACTION.debit);

        const actualResponse = await request(this.server)
            .post(this.urlPathToRollback)
            .send(this.fixtures.rollbackRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.INTERNAL_ERROR);
    }
}
