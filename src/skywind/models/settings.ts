import config from "../config";

export class Settings {
    public aroundAmount: boolean = true;
    public decreaseLoad: boolean = config.defaultSettings.decreaseLoad;
    public notSaveAnyData: boolean = config.defaultSettings.notSaveAnyData;
    public amount: number = config.defaultSettings.amount;
    public addNickname: boolean = config.defaultSettings.addNickname;
    public useTestPlayers: boolean = config.defaultSettings.useTestPlayers;
}

export let settings = new Settings();

export const clearAll = () => settings = new Settings();

export const setToDefault = () => settings = new Settings();
