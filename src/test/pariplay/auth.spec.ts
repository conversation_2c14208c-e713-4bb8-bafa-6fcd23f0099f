import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { getApplication } from "../../skywind/pariplayServer";
import { Customer, Merchant, setAll } from "../../skywind/models/merchant";
import {
    PariplayPaymentRequest,
    PariplayRollbackRequest,
    PariplayVerifyTokenRequest,
    PariplayWinPaymentRequest
} from "../../skywind/entities/pariplay";
import { getDefaultCustomer } from "../../skywind/service/customer";
import { createTicket } from "../../skywind/service/ticket";
import * as Session from "../../skywind/models/session";
import { createCustomerSession } from "../../skywind/service/session";
import * as Transaction from "../../skywind/models/transaction";
import { ACTION } from "../../skywind/service/pariplay/actions";
import { createActionError, RaiseType } from "../../skywind/models/customError";
import { createActionData } from "../../skywind/models/extraData";

const request = require("supertest");

export class PariplayBaseSpec {
    public server;
    public merchantCode = "pariplay";
    public playerCode = "pariplay";

    public sessionId: string;
    public merchant: Merchant;
    public debitRequest: PariplayPaymentRequest;
    public creditRequest: PariplayWinPaymentRequest;
    public rollbackRequest: PariplayRollbackRequest;

    public trxId: string;
    public creditTrxId: string;
    public rollbackTrxId: string;

    public async before() {
        this.server = await getApplication();
        Session.clearAll();
        Transaction.setAll({});

        const merchant: Merchant = {
            merch_id: this.merchantCode,
            merch_pwd: "test",
            multiple_session: true,
            customers: {}
        };
        setAll({ [this.merchantCode] : merchant });

        this.merchant = merchant;

        const customer = this.addPlayerToMerchant(this.playerCode);

        this.sessionId = createCustomerSession(merchant, customer);
        this.trxId = "PQ+OAAAChlsAAALXPQ+OAJH5+v=debit";
        this.creditTrxId = "PQ+OAAAChlsAAALXPQ+OAJH5+v=credit";
        this.rollbackTrxId = "PQ+OAAAChlsAAALXPQ+OAJH5+v=rollback";

        this.debitRequest = {
            Amount: 100,
            PlayerId: this.playerCode,
            RoundId: "20002",
            Token: this.sessionId,
            TransactionId: this.trxId,
            GameCode: "someGame",
            Account: {
                Username: this.merchantCode,
                Password: "somePwd"
            }
        };

        this.creditRequest = {
            ...this.debitRequest,
            DebitTransactionId: this.trxId,
            TransactionId: this.creditTrxId,
            IsRoundEnded: true
        };

        const { Amount, BonusId, ...requestData } = this.debitRequest;
        this.rollbackRequest = {
            ...requestData,
            DebitTransactionId: this.trxId,
            TransactionId: this.rollbackTrxId,
            IsRoundEnded: true
        };
    }

    public addPlayerToMerchant(name: string): Customer {
        const customer = getDefaultCustomer(name);
        this.merchant.customers[customer.cust_id] = customer;

        return customer;
    }

    public createCustomError(errorCode: number, action: ACTION) {
        createActionError(this.merchantCode, this.playerCode, action, RaiseType.AFTER, {
            "error_code": errorCode,
            "http_response_status": 200,
            "error_msg": "My custom error",
            "custom_field": "Some additional data to error"
        });
    }

    public createExtraData(data: object, action: ACTION) {
        createActionData(this.merchantCode, this.playerCode, action, data);
    }
}

@suite
class PariplayAuthSpec extends PariplayBaseSpec {
    @test
    public async testVerifyTokenSuccess() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const authRequest: PariplayVerifyTokenRequest = {
            ClientToken: ticket.id,
            GameCode: "test",
            ClientIp: "someIP",
            ExtraData: "",
            Account: {
                Username: this.merchantCode,
                Password: "somePassword"
            }
        };

        const response = await request(this.server)
            .post("/EVI/authenticate")
            .send(authRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.PlayerId).to.be.equal(this.playerCode);
    }

    @test
    public async testInvalidMerchant() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const authRequest: PariplayVerifyTokenRequest = {
            ClientToken: ticket.id,
            GameCode: "test",
            ClientIp: "someIP",
            ExtraData: "",
            Account: {
                Username: "InvalidMerch",
                Password: "somePassword"
            }
        };

        const response = await request(this.server)
            .post("/EVI/authenticate")
            .send(authRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(2);
    }

    @test
    public async testSessionExpire() {
        const authRequest: PariplayVerifyTokenRequest = {
            ClientToken: "someInvalidToken",
            GameCode: "test",
            ClientIp: "someIP",
            ExtraData: "",
            Account: {
                Username: this.merchantCode,
                Password: "somePassword"
            }
        };
        const response = await request(this.server)
            .post("/EVI/authenticate")
            .send(authRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }
}
