import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");

import { SisalBaseSpec } from "./base.spec";
import { SISAL_ERROR_CODE } from "../../skywind/sisal/sisal.errors";
import { Ticket } from "../../skywind/models/ticket";
import { createTicket } from "../../skywind/service/ticket";
import {SisalEndWagerRequest} from "../../skywind/sisal/sisal.entities";

@suite("Sisal.payment")
class SisalPaymentSpec extends SisalBaseSpec {

    @test("should make DEBIT operation")
    public async makeDebit() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse = {
            gamePhaseId: "round1",
            sessionBalance: {
                "balance": 9000,
                bonusBalance: 0,
                playerId: this.fixtures.playerCode
            }
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(200);

        delete actualResponse.body.inPhaseSeqNumber;
        delete actualResponse.body.playerGamePhaseId;
        delete actualResponse.body.playerGamePhaseWagerId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make CREDIT operation")
    public async makeCredit() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse = {
            gamePhaseId: "round1",
            sessionBalance: {
                "balance": 12000,
                bonusBalance: 0,
                playerId: this.fixtures.playerCode
            }
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToEndWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.creditRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.creditRequest)
            .expect(200);

        delete actualResponse.body.inPhaseSeqNumber;
        delete actualResponse.body.playerGamePhaseId;
        delete actualResponse.body.playerGamePhaseWagerId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make JP operation")
    public async makeJp() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse = {
            gamePhaseId: "round1",
            sessionBalance: {
                "balance": 16000,
                bonusBalance: 0,
                playerId: this.fixtures.playerCode
            }
        };

        let creditRequest = {
            ...this.fixtures.creditRequest,
            jackpotWinAmount: 1000
        };

        await request(this.server)
            .post(this.urlPathToEndWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(creditRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(creditRequest)
            .expect(200);

        creditRequest = {
            ...this.fixtures.creditRequest,
            txId: "1_win",
            jackpots: [{ amount: 1000, jackpotId: 123 }]
        } as any;

        const actualResponse = await request(this.server)
            .post(this.urlPathToEndWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(creditRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(creditRequest)
            .expect(200);

        delete actualResponse.body.inPhaseSeqNumber;
        delete actualResponse.body.playerGamePhaseId;
        delete actualResponse.body.playerGamePhaseWagerId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make DEBIT and CREDIT operations")
    public async makeDebitAndCredit() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse = {
            gamePhaseId: "round1",
            sessionBalance: {
                "balance": 11000,
                bonusBalance: 0,
                playerId: this.fixtures.playerCode
            }
        };

        await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.debitRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .post(this.urlPathToEndWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.creditRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.creditRequest)
            .expect(200);

        delete actualResponse.body.inPhaseSeqNumber;
        delete actualResponse.body.playerGamePhaseId;
        delete actualResponse.body.playerGamePhaseWagerId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make CREDIT and JP operations")
    public async makeCreditAndJP() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const expectedResponse = {
            gamePhaseId: "round1",
            sessionBalance: {
                "balance": 14000,
                bonusBalance: 0,
                playerId: this.fixtures.playerCode
            }
        };

        await request(this.server)
            .post(this.urlPathToEndWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(this.fixtures.creditRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(this.fixtures.creditRequest)
            .expect(200);

        const jpRequest = {
            ...this.fixtures.creditRequest,
            totalWinAmount: 0,
            jackpotWinAmount: 2000
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToEndWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(jpRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(jpRequest)
            .expect(200);

        delete actualResponse.body.inPhaseSeqNumber;
        delete actualResponse.body.playerGamePhaseId;
        delete actualResponse.body.playerGamePhaseWagerId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("throw error when player's balance is insufficient in DEBIT request")
    public async throwErrorWhenPlayerBalanceIsInsufficient() {
        const ticket: Ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const debitRequest = {
            ...this.fixtures.debitRequest,
            betAmount: 15000
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToWager(ticket.id))
            .set("Authorization", this.fixtures.headers.authorization)
            .set("GameCode", this.fixtures.headers.gameCode)
            .set("X-Authorization", this.getXAuthorization(debitRequest))
            .set("X-Client-ID", this.fixtures.headers.xClientId)
            .send(debitRequest)
            .expect(500);

        expect(actualResponse.body.code).deep.equal(SISAL_ERROR_CODE.INVALID_BET_AMOUNT_ERROR);
    }
}
