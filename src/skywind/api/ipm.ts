import { NextFunction, Request, Response, Router } from "express";
import logger from "../utils/logger";
import config from "../config";
import { getParamValues, parseBoolean, send, sumMajorUnits, TICKET_TYPE_TERMINAL, } from "../utils/helper";
import {
    createTicket,
    generateTicketForUser,
    getDataFromTemplateTicket,
    getTicket,
    isSpecialTicket
} from "../service/ticket";
import {
    DuplicateTransaction,
    EntityNotFound,
    GameTokenExpired,
    InvalidFreebet,
    InvalidRequest,
    MerchantMismatch,
    PlayerIsSuspended,
    PlayerNotFound,
    TransactionNotFound,
    ValueIsMissing
} from "../errors";
import { getMerchant, getMerchantWithPassword } from "../service/merchant";
import { createCustomerSession, generateCustomerSessionId, validateCustomerSession } from "../service/session";
import { Customer, Merchant } from "../models/merchant";
import {
    allowOnlyJ<PERSON><PERSON>,
    routerAsyncCallbackWrapper,
    router<PERSON>allbackWrapper
} from "./config/middleware";
import {
    defaultPageType,
    pageTypes,
    validateCurrency,
    validateCustomerSessionId,
    validateGetLobbyPageRequest,
    validatePromoType, getRegisterRoundValidators,
    validateWalletOperation
} from "./config/ipmValidator";
import { throwCustomErrorIfExists } from "../service/customError";
import * as ExtraDataService from "../service/extraData";
import { settings } from "../models/settings";
import * as TransactionModel from "../models/transaction";
import { TRANSACTION_INDEX, WALLET_ACTION } from "../models/transaction";
import * as SessionModel from "../models/session";
import { storeCustomerSession } from "../models/session";
import { defaultCustomerCurrency, defaultCustomerId, getDefaultCustomer } from "../service/customer";
import * as RoundModel from "../models/round";
import { Ticket } from "../models/ticket";
import { savePlayerInfoFromTicket } from "../service/savePlayerInfo";
import { RaiseType } from "../models/customError";
import { notSaveAnyDataMiddleware } from "./notSaveAnyData/not-save-any-data.middleware";
import { ipmNotSaveAnyDataResponder } from "./notSaveAnyData/ipm-not-save-any-data.responder";
import { MerchantHolder } from "../entities/common";
import { getRegisterRound } from "../models/round";

const filePath = require("path");
const log = logger("api");

const router: Router = Router();

router.post("/validate_ticket",
    notSaveAnyDataMiddleware((req: Request & MerchantHolder, res?: Response) => {
        const [ticketId] = getParamValues(req, ["ticket"]);
        const [custId, currency] = getDataFromTemplateTicket(ticketId);
        const responseData = getDefaultCustomer(custId, currency, settings.useTestPlayers) as any;
        responseData.error_code = 0;
        responseData.cust_session_id = ticketId;
        return ipmNotSaveAnyDataResponder(req, res, responseData);
    }),
    routerAsyncCallbackWrapper(validatePlayerTicket));
router.post("/validate_terminal_ticket", routerAsyncCallbackWrapper(validateTerminalTicket));

function validateTerminalTicket(req: Request, res: Response, next: NextFunction) {
    return validateTicket(req, res, next, config.expirationTime.terminalTicket, TICKET_TYPE_TERMINAL);
}

function validatePlayerTicket(req: Request, res: Response, next: NextFunction) {
    return validateTicket(req, res, next, config.expirationTime.ticket);
}

async function validateTicket(req: Request,
                              res: Response,
                              next: NextFunction,
                              expirationTime: number = config.expirationTime.ticket,
                              type?: string) {
    try {
        let response;

        const [merchantId, ticketId] = getParamValues(req, ["merch_id", "ticket", "merch_pwd"]);

        const merchant: Merchant = getMerchantWithPassword(req);
        let ticket: Ticket;

        if (isSpecialTicket(ticketId)) {
            const playerInfo = await savePlayerInfoFromTicket(ticketId);
            ticket = generateTicketForUser(merchant, playerInfo.playerCode, "CNY", false, false, 10000);
        } else {
            ticket = getTicket(ticketId, expirationTime);
        }

        if (ticket.merchantId !== merchantId) {
            throw new MerchantMismatch();
        }

        const customer = merchant.customers[ticket.custId];
        await throwCustomErrorIfExists("validate_ticket", merchant, customer);

        if (!customer) {
            throw new EntityNotFound("customer", ticket.custId);
        }

        customer.cust_session_id = createCustomerSession(merchant, customer, type);

        response = { error_code: 0, ...customer, freeBets: undefined, balance: undefined };

        response = ExtraDataService.getExtraData(response, "validate_ticket", merchant, customer);

        if (settings.addNickname) {
            response.nickname = customer.cust_id + "_nickname";
        }

        if (customer.bet_limit) {
            response.max_total_bet = +customer.bet_limit;
        }

        send(req, res, response);
    } catch (err) {
        next(err);
    }
}

function getCustomer(merchant: Merchant, req, operationWithoutToken?) {
    if (operationWithoutToken === undefined) {
        operationWithoutToken = false;
    }
    const [ custId ] = getParamValues(req, ["cust_id"]);
    const [ isFinalizationPayment ] = getParamValues(req, ["is_finalization_payment"], true);

    const customerSessionId = req.query.cust_session_id || req.body.cust_session_id;
    if (!operationWithoutToken && !customerSessionId) {
        throw new ValueIsMissing("cust_session_id is missing");
    }
    if (!!customerSessionId && !validateCustomerSession(customerSessionId, isFinalizationPayment)) {
        throw new GameTokenExpired("cust_session_id is expired");
    }
    const result = getCustomerById(merchant, custId);

    if (!!customerSessionId) {
        storeCustomerSession(customerSessionId);
    }

    return result;
}

function getCustomerById(merchant, custId) {
    const cust = merchant.customers[custId];
    if (!cust) {
        throw new PlayerNotFound();
    }
    if (cust.status === "suspended") {
        throw new PlayerIsSuspended();
    }

    return cust;
}

router.post("/get_player",
    routerCallbackWrapper(async (req: Request, res: Response) => {
        const merchant: Merchant = getMerchantWithPassword(req);
        const customer: Customer = getCustomer(merchant, req, true);
        await throwCustomErrorIfExists("get_player", merchant, customer);

        const response = {
            error_code: 0,
            cust_id: customer.cust_id,
            game_group: "",
            cust_login: customer.cust_login,
            currency_code: customer.currency_code,
            language: customer.language,
            country: customer.country,
            first_name: customer.first_name,
            last_name: customer.last_name,
            email: customer.email,
            test_cust: customer.test_cust
        };

        send(req, res, response);
    }));

// generates a valid ticket for a test user
router.post("/get_ticket",
    routerCallbackWrapper(async (req: Request, res: Response) => {
        const merchant: Merchant = getMerchantWithPassword(req);

        const custId = req.query.cust_id || req.body.cust_id || defaultCustomerId;
        const currencyCode = req.query.currency_code || req.body.currency_code || defaultCustomerCurrency;

        let singleSession: boolean = false;
        if (typeof req.body.single_session !== "undefined" || typeof req.query.single_session !== "undefined") {
            singleSession = parseBoolean(req.query.single_session || req.body.single_session);
        }

        let customer: Customer = merchant.customers[custId];
        if (!customer) {
            // Create customer
            customer = getDefaultCustomer(custId, currencyCode);
            merchant.customers[custId] = customer;
        } else if (!customer.test_cust) {
            throw new InvalidRequest("only test customer available for this method");
        } else if (customer.currency_code !== currencyCode) {
            throw new InvalidRequest(
                "currency_code from request does not match with currency_code from saved player"
            );
        } else {
            await throwCustomErrorIfExists("get_ticket", merchant, customer);
            if (singleSession) {
                SessionModel.deleteCustomerSession(custId);
            }
        }

        const ticket = createTicket(merchant.merch_id, custId);

        send(req, res, { ticket: ticket.id });
    }));

router.post("/get_test_customers",
    allowOnlyJSON,
    routerCallbackWrapper(async (req: Request, res: Response) => {
        const merchant: Merchant = getMerchantWithPassword(req);
        const testCustomers = [];

        if (!merchant.customers) {
            send(req, res, []);
        }
        for (const customerId in merchant.customers) {
            if (merchant.customers.hasOwnProperty(customerId)) {
                const customer: Customer = merchant.customers[customerId];
                await throwCustomErrorIfExists("get_test_customers", merchant, customer);
                if (customer.test_cust) {
                    testCustomers.push(customer);
                }
            }
        }
        send(req, res, testCustomers);
    }));

router.post("/get_balance",
    notSaveAnyDataMiddleware((req: Request & MerchantHolder, res?: Response) => {
        const [customerSessionId] = getParamValues(req, ["cust_session_id"]);
        const [custId, currency] = getDataFromTemplateTicket(customerSessionId);

        const responseData = {
            error_code: 0,
            balance: settings.amount,
            currency_code: currency,
        };
        return ipmNotSaveAnyDataResponder(req, res, responseData);
    }),
    routerCallbackWrapper(async (req: Request, res: Response) => {
        const merchant: Merchant = getMerchantWithPassword(req);
        const customer: Customer = getCustomer(merchant, req, true);
        await throwCustomErrorIfExists("get_balance", merchant, customer);

        let response = {
            error_code: 0,
            balance: customer.balance.amount,
            currency_code: customer.balance.currency_code
        };
        if (customer.freeBets && !merchant.isPromoInternal) {
            response["free_bet_count"] = customer.freeBets.count;
        }

        response = ExtraDataService.getExtraData(response, "get_balance", merchant, customer);
        send(req, res, response);
    }));

router.post("/get_page",
    routerCallbackWrapper((req: Request, res: Response) => {
        getMerchantWithPassword(req);
        validateGetLobbyPageRequest(req);

        const base = req.protocol + "://" + req.get("host") + "/api/get_test_lobby_page";
        const type: string = pageTypes.find(acceptType => acceptType === req.body.page_type) || defaultPageType;

        const response = {
            error_code: 0,
            url: `${base}?type=${type}`,
            size: 1024
        };
        send(req, res, response);
    }));

router.get("/get_test_lobby_page",
    routerCallbackWrapper((req: Request, res: Response) => {
        const type: string = pageTypes.find(acceptType => acceptType === req.query.page_type) || defaultPageType;
        res.sendFile(filePath.join(__dirname + `/../../../res/lobby_${type}.html`));
    }));

async function wallet(req: Request, res: Response, next: NextFunction, action: WALLET_ACTION) {
    const merchant: Merchant = getMerchantWithPassword(req);
    const customerSessionId = req.query.cust_session_id || req.body.cust_session_id;
    const [amount, isFinalizationPayment] = getParamValues(req, ["amount", "is_finalization_payment"], true);
    if (action === WALLET_ACTION.debit) {
        validateCustomerSessionId(customerSessionId, +amount === 0 ? isFinalizationPayment : false);
    }
    const customer: Customer = getCustomer(merchant, req, (action === WALLET_ACTION.credit && !customerSessionId));

    await throwCustomErrorIfExists(WALLET_ACTION[action], merchant, customer, RaiseType.BEFORE);

    const [
        trxId, gameId, currencyCode, gameCode, eventId, gameType, platform, roundId
    ] = getParamValues(
        req,
        [
            "trx_id", "game_id", "currency_code", "game_code", "event_id",
            "game_type",
            "platform",
            "round_id"
        ]);
    validateWalletOperation(req, merchant, customer, action);

    if (req.body.event_type !== "free-bet") {
        customer.balance.amount = sumMajorUnits(customer.balance.amount, +amount * action);
    } else if (+amount && !merchant.isPromoInternal) {
        customer.freeBets.count -= 1;
    }
    // Do not save transactions to decrease the load
    if (!settings.decreaseLoad) {
        TransactionModel.setById(trxId, action, +amount, customer.cust_id, {
            currencyCode,
            gameCode,
            gameId,
            eventId,
            gameType,
            platform,
            roundId
        });
        RoundModel.createOrUpdate({
            merchantId: merchant.merch_id,
            customerId: customer.cust_id,
            gameId
        }, action, amount);
    }
    await throwCustomErrorIfExists(WALLET_ACTION[action], merchant, customer, RaiseType.AFTER);
    let response = {
        error_code: 0,
        balance: customer.balance.amount,
        trx_id: trxId,
    };
    if (customer.freeBets) {
        response["free_bet_count"] = customer.freeBets.count;
    }

    response = ExtraDataService.getExtraData(response, WALLET_ACTION[action], merchant, customer);
    send(req, res, response);
}

async function bonus(req: Request, res: Response) {
    const action = WALLET_ACTION.bonus;
    const merchant: Merchant = getMerchantWithPassword(req, true);

    const customer: Customer = getCustomerById(merchant, getParamValues(req, ["cust_id"]));
    await throwCustomErrorIfExists(WALLET_ACTION[action], merchant, customer);

    const [amount, trxId] = getParamValues(req, ["amount", "trx_id"]);
    const [promoType] = getParamValues(req, ["promo_type"], true);
    validatePromoType(promoType);
    const transaction = TransactionModel.getById(trxId, action);
    if (transaction) {
        throw new DuplicateTransaction({
            balance: customer.balance.amount,
            trx_id: trxId,
            free_bet_count: customer.freeBets && customer.freeBets.count
        });
    }
    validateCurrency(req, customer);
    customer.balance.amount = sumMajorUnits(customer.balance.amount, amount);
    // Do not save transactions to decrease the load
    if (!settings.decreaseLoad) {
        TransactionModel.setById(trxId, action, +amount, customer.cust_id);
    }

    const response = {
        error_code: 0,
        balance: customer.balance.amount,
        trx_id: trxId,
    };

    send(req, res, response);
}

async function finalizeWithRoundStats(req: Request, res: Response) {
    const action = WALLET_ACTION.closeRoundWithRoundStats;
    // Do not save and check anything
    if (settings.notSaveAnyData) {
        const [trxId] = getParamValues(req, ["trx_id"]);
        const response = {
            error_code: 0,
            balance: settings.amount,
            trx_id: trxId,
        };
        send(req, res, response);
        return;
    }
    const merchant: Merchant = getMerchantWithPassword(req, true);

    const customer: Customer = getCustomerById(merchant, getParamValues(req, ["cust_id"]));
    await throwCustomErrorIfExists("finish_round_with_statistics", merchant, customer);

    // tslint:disable-next-line:prefer-const
    let [trxId, totalBet, totalWin, eventType, isFinalizationPayment, gameId] = getParamValues(req,
        ["trx_id", "total_bet", "total_win", "event_type", "is_finalization_payment", "game_id"]);

    totalBet = +totalBet;
    totalWin = +totalWin;

    if (eventType !== "round-statistics") {
        throw new InvalidRequest(`event type ${eventType} must be 'round-statistics'`);
    }

    if (isFinalizationPayment !== true) {
        throw new InvalidRequest("is_finalization_payment param must be true");
    }

    const transaction = TransactionModel.getById(trxId, action);
    if (transaction) {
        throw new DuplicateTransaction({
            balance: customer.balance.amount,
            trx_id: trxId
        });
    }
    validateCurrency(req, customer);
    const balanceChange = totalWin - totalBet;
    customer.balance.amount = sumMajorUnits(customer.balance.amount, balanceChange);
    // Do not save transactions to decrease the load
    if (!settings.decreaseLoad) {
        TransactionModel.setById(trxId, action, balanceChange, customer.cust_id);

        RoundModel.createOrUpdate({
            merchantId: merchant.merch_id,
            customerId: customer.cust_id,
            gameId
        }, action, balanceChange, totalBet, totalWin);
    }

    const response = {
        error_code: 0,
        balance: customer.balance.amount,
        trx_id: trxId,
    };

    send(req, res, response);
}

async function walletRollback(req: Request, res: Response) {
    const merchant: Merchant = getMerchantWithPassword(req);
    const [trxId, eventType, gameId] = getParamValues(req,
        ["trx_id", "event_type", "game_id", "timestamp", "event_id"]);
    const rollbackTransaction = TransactionModel.getById(trxId, WALLET_ACTION.rollback);
    if (rollbackTransaction) {
        const custId = rollbackTransaction[TRANSACTION_INDEX.CUSTOMER_ID];
        const customer = merchant[custId];
        if (!customer) {
            throw new PlayerNotFound();
        }
        throw new DuplicateTransaction({
            balance: customer.balance.amount,
            trx_id: trxId,
            free_bet_count: customer.freeBets && customer.freeBets.count
        });
    }

    let amount;
    let customer;
    if (eventType === "rollback") {
        const transaction = TransactionModel.getById(trxId, WALLET_ACTION.debit);
        if (!transaction) {
            throw new TransactionNotFound({});
        }
        amount = transaction[TRANSACTION_INDEX.AMOUNT];
        const custId = transaction[TRANSACTION_INDEX.CUSTOMER_ID];
        customer = merchant.customers[custId];
        if (!customer) {
            throw new PlayerNotFound();
        }
    }

    await throwCustomErrorIfExists("rollback", merchant, customer);

    customer.balance.amount = sumMajorUnits(customer.balance.amount, Math.abs(amount));
    if (!settings.decreaseLoad) {
        TransactionModel.setById(trxId, WALLET_ACTION.rollback, Math.abs(amount), customer.cust_id);
        RoundModel.createOrUpdate({
            merchantId: merchant.merch_id,
            customerId: customer.cust_id,
            gameId
        }, WALLET_ACTION.debit, amount);
    }

    let response = {
        error_code: 0,
        balance: customer.balance.amount,
        trx_id: trxId,
    };
    if (customer.freeBets) {
        response["free_bet_count"] = customer.freeBets.count;
    }

    response = ExtraDataService.getExtraData(response, "rollback", merchant, customer);
    send(req, res, response);
}

// BET
router.post("/debit",
    notSaveAnyDataMiddleware(ipmNotSaveAnyDataResponder),
    routerCallbackWrapper((req: Request, res: Response, next: NextFunction) =>
        wallet(req, res, next, WALLET_ACTION.debit)));

// WIN
router.post("/credit",
    notSaveAnyDataMiddleware(ipmNotSaveAnyDataResponder),
    routerCallbackWrapper((req: Request, res: Response, next: NextFunction) =>
        wallet(req, res, next, WALLET_ACTION.credit)));
// BONUS
router.post("/bonus",
    notSaveAnyDataMiddleware(ipmNotSaveAnyDataResponder),
    routerCallbackWrapper((req: Request, res: Response, next: NextFunction) =>
        bonus(req, res)));

// finalize with roundStatistics method
router.post("/finish_round_with_statistics",
    routerCallbackWrapper((req: Request, res: Response, next: NextFunction) =>
        finalizeWithRoundStats(req, res)));

router.post("/rollback",
    notSaveAnyDataMiddleware(ipmNotSaveAnyDataResponder),
    routerCallbackWrapper(walletRollback)
);

router.post("/get_free_bet", routerCallbackWrapper(async (req: Request, res: Response) => {
    const merchant: Merchant = getMerchantWithPassword(req);
    if (merchant.isPromoInternal) {
        throw new InvalidRequest("free bets are managed by Skywind promotion system");
    }
    const customer: Customer = getCustomer(merchant, req, false);
    await throwCustomErrorIfExists("get_free_bet", merchant, customer);

    let [stakeAll] = getParamValues(req, ["stake_all", "coin_multiplier"]);
    stakeAll = stakeAll.split(",").map(v => Number(v));
    if (stakeAll.filter(v => isNaN(v)).length > 0) {
        throw new InvalidRequest("stake_all is not array of numbers");
    }

    const response = {
        error_code: 0
    };

    if (customer.freeBets) {
        response["free_bet_count"] = customer.freeBets.count;
        response["free_bet_coin"] = customer.freeBets.coin;
    } else {
        throw new InvalidFreebet();
    }

    send(req, res, response);
}));

router.post("/resolve_round", routerCallbackWrapper(async (req: Request, res: Response) => {
    const merchant: Merchant = getMerchantWithPassword(req);
    const customer: Customer = getCustomer(merchant, req, true);

    await throwCustomErrorIfExists("resolve_round", merchant, customer);

    let response = {
        error_code: 0,
        balance: customer.balance.amount
    };

    response = ExtraDataService.getExtraData(response, "rollback", merchant, customer);
    send(req, res, response);
}));

router.post("/logout_player", routerCallbackWrapper((req: Request, res: Response) => {
    const merchant: Merchant = getMerchantWithPassword(req);
    // const customer: Customer = getCustomer(merchant, req, true);

    const [custId, logoutId, gameId, roundId, roundState, gameCode] = getParamValues(req,
        ["cust_id", "logout_id", "game_id", "round_id", "round_state", "game_code"]);

    // note, that for now we do not delete session in Mock on logout - probably, its more convinient to leave session
    // as if game link is shared - then second game start may fail
    // SessionModel.deleteCustomerSession(custId);

    const response = { error_code: 0 };

    send(req, res, response);
}));

router.post("/refresh_session",
    notSaveAnyDataMiddleware((req: Request & MerchantHolder, res?: Response) => {
        const sessionId = generateCustomerSessionId(req.body.cust_id);
        return ipmNotSaveAnyDataResponder(req, res, { error_code: 0, new_cust_session_id: sessionId });
    }),
    routerCallbackWrapper(async (req: Request, res: Response) => {
        const merchant = getMerchant(req);
        const customer = merchant.customers[req.body.cust_id];
        if (!customer) {
            throw new EntityNotFound("Customer", req.body.cust_id);
        }
        await throwCustomErrorIfExists("refresh_session", merchant, customer);

        customer.cust_session_id = createCustomerSession(merchant, customer);

        send(req, res, { error_code: 0, new_cust_session_id: customer.cust_session_id });
        return;
    }));

router.put("/register_round", ...getRegisterRoundValidators(), function(req: Request, res: Response) {
    const response = getRegisterRound(req.body.account_id, req.body.round_id);
    if (response.error_code) {
        res.status(400);
    } else {
        response.error_code = 0;
    }
    res.send(response);
});

export default router;
