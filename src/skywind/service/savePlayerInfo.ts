import { PlayerInfo, playerInfoModel } from "../models/playerInfo";
import * as uuid from "uuid";
import { EmailAlreadyExists, ValueIsMissing } from "../errors";

export async function savePlayerInfoFromTicket(ticketId: string): Promise<PlayerInfo> {
    const [ ticket, email, firstName, lastName, phone, company, language ] = ticketId.split("__");
    const cleanedEmail = trimString(email);
    const cleanedFirstName = trimString(firstName);
    const cleanedLastName = trimString(lastName);

    if (!cleanedEmail) {
        return Promise.reject(new ValueIsMissing("Email"));
    }

    let playerCode;
    if (!(cleanedFirstName || cleanedLastName)) {
        playerCode = uuid.v4();
    } else {
        const numberOfPlayers = await playerInfoModel.get().count({ where: { firstName, lastName }});
        playerCode = `${cleanedFirstName}.${cleanedLastName}${ numberOfPlayers ? "_" + numberOfPlayers : ""}`;
    }

    const existingPlayer = await playerInfoModel.get().findOne({ where: { email: cleanedEmail.toLowerCase() } });
    if (existingPlayer) {
        return Promise.reject(new EmailAlreadyExists(cleanedEmail));
    }

    try {
        const instance = await playerInfoModel.get().create({
            email: cleanedEmail.toLowerCase(),
            playerCode,
            firstName,
            lastName,
            phone,
            company,
            language
        });

        return instance.toJSON();
    } catch (err) {
        return Promise.reject(err);
    }

}

function trimString(value: string = ""): string {
    return value.trim();
}
