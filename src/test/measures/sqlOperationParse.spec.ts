import { restore } from "sinon";
import { expect } from "chai";
import { SQLOperationParser } from "../../skywind/measures/sqlOperationParser";

describe(SQLOperationParser.name, () => {
    after(() => {
        restore();
    });
    it("parse select sql", () => {
        const sql1 = "SELECT \"m\".\"id\" FROM \"public\".\"messages\" \"m\"";
        const sql2 = "SELECT \"type\" FROM \"public\".\"merchant_types\" AS \"MerchantType\"";
        const sql3 = "SELECT version()";
        const sql4 = "SELECT * FROM current_schema()";
        expect(SQLOperationParser.getOperation(sql1)).eq("select \"public\".\"messages\"");
        expect(SQLOperationParser.getOperation(sql2)).eq("select \"public\".\"merchant_types\"");
        expect(SQLOperationParser.getOperation(sql3)).eq("select version()");
        expect(SQLOperationParser.getOperation(sql4)).eq("select current_schema()");
    });

    it("parse set sql", () => {
        const sql1 = "SET TRANSACTION ISOLATION LEVEL READ COMMITTED";
        expect(SQLOperationParser.getOperation(sql1)).eq("command SET");
    });
});
