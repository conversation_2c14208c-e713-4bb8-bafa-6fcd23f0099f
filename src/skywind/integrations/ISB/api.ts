import { NextFunction, Response, Router } from "express";

import { router<PERSON>allbackWrapper } from "../../api/config/middleware";
import {
    authenticate,
    hardCodedResponse,
    parseCommand,
    validateRequestData
} from "./middleware";
import { ISBService } from "./service";
import { RequestEx } from "./entities";

const router: Router = Router();

router.post("/:providerid",
    validateRequestData,
    parseCommand,
    hardCodedResponse,
    authenticate,
    routerCallbackWrapper(
        (req: RequestEx, res: Response, next: NextFunction) => {
            const result = ISBService.getResponse(req);
            res.send(result);
            next();
        })
);

export default router;
