import { NextFunction, Request, Response, Router } from "express";
import logger from "../utils/logger";
import { getGVCService } from "../service/gvc";
import {
    balanceResponder,
    paymentResponder,
    cancelBetResponder,
    verifyTokenResponder
} from "../service/gvcResponders";
import {
    GVCPlayerBalanceRequest,
    GVCPlayerBalanceResponse,
    GVCFundsTransferRequest,
    GVCFundsTransferResponse,
    GVCVoidTransactionRequest,
    GVCVoidTransactionResponse,
    GVCExtraRequestData,
    GVCResponseStatusData,
    GVCVerifyTokenRequest,
    GVCVerifyTokenResponse
} from "../entities/gvc";
import { authenticate } from "./config/gvcMiddleware";
import { settings } from "../models/settings";
import { isEmpty } from "../utils/isEmpty";
import * as gvcErrors from "../gvcErrors";
import { INTEGRATION_TYPE } from "../../config/integrationType";

const log = logger(`${INTEGRATION_TYPE.GVC}:api`);
const router: Router = Router();

router.post("/casino/player/verifyToken",
    notSaveAnyDataMiddleware(verifyTokenResponder),
    authenticate,
    validateFieldsForEmpty([
        "partnerId",
        "accountId",
        "gameContext",
        "currencyCode",
        "secureToken",
        "gameCode",
        "channelId"
    ]),
    verifyToken);

router.post("/casino/player/balance",
    notSaveAnyDataMiddleware(balanceResponder),
    authenticate,
    validateFieldsForEmpty([
        "partnerId",
        "accountId",
        "gameContext",
        "currencyCode",
        "secureToken",
        "gameCode",
        "clientChannel"
    ]),
    getBalance);

router.put("/casino/player/funds",
    notSaveAnyDataMiddleware(paymentResponder),
    authenticate,
    validateFieldsForEmpty([
        "partnerId",
        "accountId",
        "gameContext",
        "currencyCode",
        "secureToken",
        "gameCode",
        "clientChannel",
        "gameSessionId",
        "gameRoundState",
        "gameRoundId",
        "transactionId",
        "transactionDetails"
    ]),
    makePayment);

router.delete("/casino/player/transaction",
    notSaveAnyDataMiddleware(cancelBetResponder),
    authenticate,
    validateFieldsForEmpty([
        "partnerId",
        "accountId",
        "clientChannel",
        "reason",
        "transactionId",
        "cancelledTransactionId"
    ]),
    cancelBet);

/**
 * MEMO: API controllers
 */

async function verifyToken(req: Request & GVCExtraRequestData, res: Response, next: NextFunction) {
    try {
        const data = req.body as GVCVerifyTokenRequest;
        const response: GVCVerifyTokenResponse = await getGVCService(req.merchant, req.customer).verifyToken(data);
        decorateResponse<GVCVerifyTokenResponse>(response);

        res.send(response);

    } catch (error) {
        next(error);
    }
}

async function getBalance(req: Request & GVCExtraRequestData, res: Response, next: NextFunction) {
    try {
        const data = req.body as GVCPlayerBalanceRequest;
        const response: GVCPlayerBalanceResponse = await getGVCService(req.merchant, req.customer).getBalance(data);
        decorateResponse<GVCPlayerBalanceResponse>(response);

        res.send(response);

    } catch (error) {
        next(error);
    }
}

async function makePayment(req: Request & GVCExtraRequestData, res: Response, next: NextFunction) {
    try {
        const data = req.body as GVCFundsTransferRequest;
        const response: GVCFundsTransferResponse = await getGVCService(req.merchant, req.customer).makePayment(data);
        decorateResponse<GVCFundsTransferResponse>(response);

        res.send(response);

    } catch (error) {
        next(error);
    }
}

async function cancelBet(req: Request & GVCExtraRequestData, res: Response, next: NextFunction) {
    try {
        const data = req.body as GVCVoidTransactionRequest;
        const response: GVCVoidTransactionResponse = await getGVCService(req.merchant, req.customer).cancelBet(data);
        decorateResponse<GVCVoidTransactionResponse>(response);

        res.send(response);

    } catch (error) {
        next(error);
    }
}

/**
 * MEMO: Auxiliary methods
 */

function notSaveAnyDataMiddleware(responder: (req: Request & GVCExtraRequestData) => object) {
    return (req: Request & GVCExtraRequestData, res: Response, next: NextFunction) => {
        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req));
            } catch (err) {
                return next(err);
            }
        }
        return next();
    };
}

function validateFieldsForEmpty(fields: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        for (const param of fields) {
            const value = req.body[param];
            if (isEmpty(value)) {
                return next(new gvcErrors.MandatoryParametersMissingError(`[${param}] is required`));
            }
        }
        return next();
    };
}

function decorateResponse<T extends GVCResponseStatusData>(response: T) {
    response.status = {
        statusCode: "0",
        statusMessage: "success"
    };
}

export default router;
