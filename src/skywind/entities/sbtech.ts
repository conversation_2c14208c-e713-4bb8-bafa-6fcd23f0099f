import { Customer, Merchant } from "../models/merchant";
import { Ticket } from "../models/ticket";

export interface SBTechExtraRequestData {
    merchant?: Merchant;
    customer?: Customer;
    tokenData?: Ticket;
}

export interface SBTechInitSessionRequest {
    providerKey: string;
    providerPass: string;
    clientToken: string;
    gameId: string;
    tableId?: string;
    playerIp: string;
    additionalInfo?: string;
}

export interface SBTechInitSessionResponse {
    playerId: string;
    brandId: string;
    isTestPlayer: boolean;
    currencyCode: string;
    countryCode: string;
    sessionId: string;
    jurisdiction: string; // available: com, uk, dk, mt, ro, pt, gg, agcc, gi, us
    balance: number;
    walletType?: WALLET_TYPE; // available: real, bonus
    message?: string; // to be shown to the player who opens the game
    timestamp: number; // unix time
    error?: string | null; //  '', 'OK' or NULL
}

export interface SBTechPaymentData {
    providerKey: string;
    providerPass: string;
    sessionId: string;
    gameId: string;
    tableId?: string;
    playerId: string;
    brandId: string;
}

export interface SBTechBalanceData {
    balance: number;
    timestamp: number;
}

export interface SBTechAdditionalInfo {
    additionalInfo?: string;

    [field: string]: string;
}

export interface SBTechDebitRequest extends SBTechPaymentData {
    roundId: string;
    trxId: string;
    amount: number;
    betType?: BET_TYPE;
    jackpotContribution?: number;
    jackpotId?: string;
    transactionDetails?: SBTechAdditionalInfo;
}

export interface SBTechDebitResponse extends SBTechBalanceData {
    trxId: string; // Long ?
    walletType?: WALLET_TYPE;
    message?: string;
    sessionId?: string;
    error?: string | null; // '', 'OK' or NULL
}

export enum WALLET_TYPE {
    REAL = "Real",
    BONUS = "Bonus"
}

export enum BET_TYPE {
    BET = "bet",
    TIP = "tip",
}

export interface SBTechCreditRequest extends SBTechPaymentData {
    roundId: string;
    trxId: string;
    debitTrxId: string;
    amount: number;
    isOffline?: boolean; // if transaction was sent offline
    jackpotAmount?: number;
    jackpotId?: string;
    roundEnded?: boolean;
    promotionId?: string;
    smResult?: string[];
    transactionDetails?: SBTechAdditionalInfo;
}

export interface SBTechCreditResponse extends SBTechBalanceData {
    trxId: string; // Long ?
    sessionId?: string;
    error?: string | null; // '', 'OK' or NULL
}

export interface SBTechEndRoundRequest extends SBTechPaymentData {
    roundId: string;
    trxId: string;
    promotionId?: string;
    smResult?: string[]; // for portugal regulation
    transactionDetails?: SBTechAdditionalInfo;
}

export interface SBTechEndRoundResponse extends SBTechBalanceData {
    trxId: string; // Long ?
    sessionId?: string;
    error?: string | null; // '', 'OK' or NULL
}

export interface SBTechRollbackTransactionRequest extends SBTechPaymentData {
    roundId: string;
    isOffline?: boolean; // if transaction was sent offline
    trxId: string;
    debitTrxId: string;
    roundEnded?: boolean; // if this is last action, the round should be ended
    transactionDetails?: SBTechAdditionalInfo;
}

export interface SBTechRollbackTransactionResponse extends SBTechBalanceData {
    trxId: string; // Long ?
    sessionId?: string;
    error?: string | null; // '', 'OK' or NULL
}

export interface SBTechGetBalanceRequest extends SBTechPaymentData {
}

export interface SBTechGetBalanceResponse extends SBTechBalanceData {
    sessionId?: string;
    error?: string | null; // '', 'OK' or NULL
}

export interface SBTechEndSessionRequest extends SBTechPaymentData {
}

export interface SBTechEndSessionResponse extends SBTechBalanceData {
    error?: string | null; // '', 'OK' or NULL
}

export interface SBTechError {
    error: string;
    timestamp: number;
    balance?: number;
}

export enum SBTECH_TRANSACTION_TYPE {
    DEBIT = "Debit",
    CREDIT = "Credit",
    ROLLBACK = "RollbackTransaction",
    END_ROUND = "EndRound",
}

export enum SBTECH_ROUND_STATE {
    FINISHED = "FINISHED",
    CANCELLED = "CANCELLED",
    IN_PROGRESS = "IN-PROGRESS"
}

export enum SBTECH_ASPECT_RATIO {
    STANDARD = "4:3",
    WIDESCREEN = "16:9"
}

export enum SBTECH_GAME_TECH_TYPE {
    HTML5 = "HTML5",
    FLASH = "Flash"
}

export enum SBTECH_GAME_MODE {
    REAL = "Real",
    DEMO = "Demo",
}

export enum SBTECH_PLATFORM {
    DESKTOP = "Desktop",
    MOBILE = "Mobile",
    CROSS = "CrossPlatform",
}
