{"swagger": "2.0", "info": {"description": "Skywind - Galaxy Pro - API V2", "version": "5.56", "title": "Skywind - Galaxy Pro - API V2"}, "basePath": "/v2", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-access-token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {"report": "Get Entity brand report", "report:jackpot": "Get Entity brand jackpot report", "report:jackpot:contributions": "Get Entity contributions to jackpots", "report:jackpot:contributions:logs": "Get Entity jackpot contribution logs", "report:jackpot:contributions:wins": "Get Entity jackpot wins logs", "keyentity:report": "Get keyEntity brand report", "keyentity:report:jackpot": "Get keyEntity brand jackpot report", "keyentity:report:jackpot:contributions": "Get keyEntity contributions to jackpots", "keyentity:report:jackpot:contributions:logs": "Get keyEntity jackpot contribution logs", "keyentity:report:jackpot:contributions:wins": "Get keyEntity jackpot wins logs", "keyentity:game": "Manage game", "keyentity:game:history": "Get game history report", "entity:game": "Manage game", "entity:game:history": "Get game history report", "player": "Player management", "player:view": "View player"}}}, "paths": {"/report/jackpot/contributions/logs": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:jackpot", "keyentity:report:jackpot:contributions", "keyentity:report:jackpot:contributions:logs"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot contribution logs for key entity", "description": "Method returns list of jackpot contribution logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP contribution log", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContributionLogReportV2"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/jackpot/contributions/logs": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:jackpot", "report:jackpot:contributions", "report:jackpot:contributions:logs"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot contribution logs by path", "description": "Method returns list of jackpot contribution logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP contribution log", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContributionLogReportV2"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/report/jackpot/contributions/wins": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:jackpot", "keyentity:report:jackpot:contributions", "keyentity:report:jackpot:contributions:wins"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot wins logs for key entity", "description": "Method returns list of jackpot wins logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP win logs", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpWinReportV2"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/report/jackpot/contributions/wins": {"get": {"security": [{"apiKey": []}, {"Permissions": ["report", "report:jackpot", "report:jackpot:contributions", "report:jackpot:contributions:wins"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot wins logs by path", "description": "Method returns list of jackpot wins logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/trxDate__gt"}, {"$ref": "#/parameters/trxDate__lt"}], "responses": {"200": {"description": "JP win logs", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpWinReportV2"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the key entity", "description": "This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission. Note: if round id is in query all other filters are ignored.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/internalRoundIdStrictEquality"}, {"$ref": "#/parameters/internalRoundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/recoveryType"}, {"$ref": "#/parameters/recoveryType__in"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: Array,\n- from: Number,\n- to: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number;\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the brand", "description": "This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission. Note: if round id is in query all other filters are ignored.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/internalRoundIdStrictEquality"}, {"$ref": "#/parameters/internalRoundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/recoveryType"}, {"$ref": "#/parameters/recoveryType__in"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number;\n- balanceBefore: Number;\n- balanceAfter: Number;\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}}, "definitions": {"GameHistory": {"type": "object", "properties": {"roundId": {"type": "string", "description": "Round public id", "example": "feE3Sb39"}, "internalRoundId": {"type": "string", "description": "Round id", "example": "200001"}, "brandId": {"type": "string", "description": "Brand public id", "example": "feE3Sb39"}, "playerCode": {"type": "string", "description": "Player code", "example": "PLAYER1"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_mrmnky"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 2}, "revenue": {"type": "number", "description": "Revenue", "example": -1.9}, "firstTs": {"type": "string", "description": "time of first action in round", "example": "2017-07-14T07:07:01.080Z"}, "ts": {"type": "string", "description": "time of last action in round", "example": "2017-07-14T07:07:11.930Z"}, "finished": {"type": "boolean", "description": "Whether the round has ended", "example": true}, "isTest": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "balanceBefore": {"type": "number", "description": "Player's balance before round", "example": 25000}, "balanceAfter": {"type": "number", "description": "Player's balance after round", "example": 24950}, "device": {"type": "string", "description": "Player's device", "example": "web"}, "recoveryType": {"type": "string", "enum": ["revert", "force-finish", "finalize"], "description": "Recovery type for rounds that were resolved manually", "example": "force-finish"}, "extraData": {"type": "object", "properties": {"extRoundId": {"type": "string", "description": "PT round id", "example": "extRound1"}}}}}, "JpReport": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "pt-allad-reel-jp"}, "gameId": {"type": "string", "description": "Game id", "example": "pt-allad-reel-jp"}, "roundId": {"type": "string", "description": "Round ID", "example": "ni9Gav0"}, "seedAmount": {"type": "number", "description": "Amount, contributed to the seed part of the JP", "example": 12.5}, "progressiveAmount": {"type": "number", "description": "Amount, contributed to the progressive part of the JP", "example": 6.5}, "currency": {"type": "string", "description": "currency", "example": "USD"}, "currencyRate": {"type": "number", "description": "currency rate", "example": 0.92123}, "playerCode": {"type": "string", "description": "player code", "example": "test"}, "trxDate": {"type": "string", "description": "transaction date", "example": "2018-01-03T17:00:00.000Z"}, "jackpotId": {"type": "string", "description": "jackpot id name", "example": "FIRE-reel"}, "pool": {"type": "string", "description": "pool of contribute", "example": "mega"}}}, "JpContributionLogReportV2": {"allOf": [{"$ref": "#/definitions/JpReport"}, {"type": "object", "properties": {"betAmount": {"type": "number", "description": "Full bet amount of game stakes used for the Jackpot replenishment (in EUR)", "example": 6.5}, "insertedAt": {"type": "string", "description": "Inserted at", "example": "2018-01-03T17:00:00.000Z"}, "externalId": {"type": "string", "description": "External id", "example": "123"}, "trxId": {"type": "string", "description": "Trx id", "example": "123"}}}]}, "JpWinReportV2": {"allOf": [{"$ref": "#/definitions/JpReport"}, {"type": "object", "properties": {"initialSeedAmount": {"type": "number", "description": "Initial seed", "example": 100}, "externalId": {"type": "string", "description": "External ID", "example": "enOQKQAJew8AAALDenOQKSmDW10"}, "eventId": {"type": "number", "description": "Spin number", "example": 1}, "totalSeedAmount": {"type": "number", "description": "Total Amount, contributed to the seed part of the JP", "example": 12.5}, "totalProgressiveAmount": {"type": "number", "description": "Total Amount, contributed to the progressive part of the JP", "example": 6.5}, "winAmount": {"type": "number", "description": "Full win amount of game stakes used for the Jackpot replenishment (in EUR)", "example": 6.5}, "winAmountCurrency": {"type": "number", "description": "Full win amount of game stakes used for the Jackpot replenishment (in player currency)", "example": 6.5}, "trxId": {"type": "string", "description": "Trx id", "example": "123"}}}]}, "PlayerChatInfo": {"type": "object", "properties": {"brandId": {"type": "string", "description": "player brand id", "example": "5"}, "brandTitle": {"type": "string", "description": "Brand name or title", "example": "Test"}, "nickname": {"type": "string", "description": "player nickname", "example": "<PERSON><PERSON><PERSON>"}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}, "playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "isTracked": {"type": "boolean", "description": "flag which indicates if this is a tracked playe", "example": false}, "isPublicChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "isPrivateChatBlock": {"type": "boolean", "description": "flag which indicates if this player should be blocked for sending chat messages.", "example": false}, "hasWarn": {"type": "boolean", "description": "flag which indicates if this player is warn", "example": false}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 60}, "message": {"type": "string", "description": "error message", "example": "Entity already exists"}}}}, "parameters": {"sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "queryFormat": {"name": "format", "in": "query", "description": "Desired format", "required": false, "type": "string", "enum": ["csv"]}, "trxDate__gt": {"in": "query", "name": "trxDate__gt", "description": "Trx date truncate to datetime greater than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": true, "type": "string"}, "trxDate__lt": {"in": "query", "name": "trxDate__lt", "description": "Trx date truncate to datetime lowest than in ISO 8601 timestamp format (e.g. 2016-12-10T16:47:38.887Z)", "required": true, "type": "string"}, "path": {"name": "path", "in": "path", "description": "Business entity path", "required": true, "type": "string"}, "isTest": {"in": "query", "name": "isTest", "type": "boolean", "required": false, "description": "is test entry", "enum": [true, false]}, "queryFinished": {"name": "finished", "in": "query", "description": "Get finished/unfinished rounds. true - finished, false - unfinished, undefined - all", "required": false, "type": "boolean"}, "bet": {"in": "query", "name": "bet", "description": "sharp value for bet", "required": false, "type": "number"}, "bet__gt": {"in": "query", "name": "bet__gt", "description": "bet is greater-than", "required": false, "type": "number"}, "bet__lt": {"in": "query", "name": "bet__lt", "description": "bet is less-than", "required": false, "type": "number"}, "bet__gte": {"in": "query", "name": "bet__gte", "description": "bet is greater-than-or-equal", "required": false, "type": "number"}, "bet__lte": {"in": "query", "name": "bet__lte", "description": "bet is less-than-or-equal", "required": false, "type": "number"}, "win": {"in": "query", "name": "win", "description": "sharp value for win", "required": false, "type": "number"}, "win__gt": {"in": "query", "name": "win__gt", "description": "win is greater-than", "required": false, "type": "number"}, "win__lt": {"in": "query", "name": "win__lt", "description": "win is less-than", "required": false, "type": "number"}, "win__gte": {"in": "query", "name": "win__gte", "description": "win is greater-than-or-equal", "required": false, "type": "number"}, "win__lte": {"in": "query", "name": "win__lte", "description": "win is less-than-or-equal", "required": false, "type": "number"}, "revenue": {"in": "query", "name": "revenue", "description": "sharp value for revenue", "required": false, "type": "number"}, "revenue__gt": {"in": "query", "name": "revenue__gt", "description": "revenue is greater-than", "required": false, "type": "number"}, "revenue__lt": {"in": "query", "name": "revenue__lt", "description": "revenue is less-than", "required": false, "type": "number"}, "revenue__gte": {"in": "query", "name": "revenue__gte", "description": "revenue is greater-than-or-equal", "required": false, "type": "number"}, "revenue__lte": {"in": "query", "name": "revenue__lte", "description": "revenue is less-than-or-equal", "required": false, "type": "number"}, "device": {"in": "query", "name": "device", "description": "code of player's device", "required": false, "type": "string"}, "balanceBefore": {"in": "query", "name": "balanceBefore", "description": "balance before round of revenue", "required": false, "type": "number"}, "balanceBefore__gt": {"in": "query", "name": "balanceBefore__gt", "description": "balance before round is greater-than", "required": false, "type": "number"}, "balanceBefore__lt": {"in": "query", "name": "balanceBefore__lt", "description": "balance before round is less-than", "required": false, "type": "number"}, "balanceBefore__gte": {"in": "query", "name": "balanceBefore__gte", "description": "balance before round is greater-than-or-equal", "required": false, "type": "number"}, "balanceBefore__lte": {"in": "query", "name": "balanceBefore__lte", "description": "balance before round is less-than-or-equal", "required": false, "type": "number"}, "balanceAfter": {"in": "query", "name": "balanceAfter", "description": "balance after round of revenue", "required": false, "type": "number"}, "balanceAfter__gt": {"in": "query", "name": "balanceAfter__gt", "description": "balance after round is greater-than", "required": false, "type": "number"}, "balanceAfter__lt": {"in": "query", "name": "balanceAfter__lt", "description": "balance after round is less-than", "required": false, "type": "number"}, "balanceAfter__gte": {"in": "query", "name": "balanceAfter__gte", "description": "balance after round is greater-than-or-equal", "required": false, "type": "number"}, "balanceAfter__lte": {"in": "query", "name": "balanceAfter__lte", "description": "balance after round is less-than-or-equal", "required": false, "type": "number"}, "ts": {"in": "query", "name": "ts", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__gte": {"in": "query", "name": "ts__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lte": {"in": "query", "name": "ts__lte", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__gt": {"in": "query", "name": "ts__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lt": {"in": "query", "name": "ts__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs": {"in": "query", "name": "firstTs", "description": "sharp first timestamp of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__gt": {"in": "query", "name": "firstTs__gt", "description": "first timestamp greater than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__lt": {"in": "query", "name": "firstTs__lt", "description": "first timestamp less than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__gte": {"in": "query", "name": "firstTs__gte", "description": "first timestamp greater than or equal in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "firstTs__lte": {"in": "query", "name": "firstTs__lte", "description": "first timestamp less than or equal in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "roundIdStrictEquality": {"in": "query", "name": "roundId", "description": "roundId equal to value", "required": false, "type": "string"}, "internalRoundIdStrictEquality": {"in": "query", "name": "internalRoundId", "description": "roundId equal to value", "required": false, "type": "string"}, "roundIdIn": {"in": "query", "name": "roundId__in", "description": "list of roundIds separated by commas", "required": false, "type": "string"}, "internalRoundIdIn": {"in": "query", "name": "internalRoundId__in", "description": "list of roundIds separated by commas", "required": false, "type": "string"}, "playerCodeStrictEquality": {"in": "query", "name": "playerCode", "description": "player code equal to value", "required": false, "type": "string"}, "playerCodeContains": {"in": "query", "name": "playerCode__contains", "description": "player code contains string", "required": false, "type": "string"}, "playerCodeNotContains": {"in": "query", "name": "playerCode__contains!", "description": "player code doesn't contain string", "required": false, "type": "string"}, "playerCodeIn": {"in": "query", "name": "playerCode__in", "description": "list of player codes separated by commas", "required": false, "type": "string"}, "gameCodeStrictEquality": {"in": "query", "name": "gameCode", "description": "game code equal to value", "required": false, "type": "string"}, "gameCodeContains": {"in": "query", "name": "gameCode__contains", "description": "game code contains string", "required": false, "type": "string"}, "gameCodeNotContains": {"in": "query", "name": "gameCode__contains!", "description": "game code doesn't contain string", "required": false, "type": "string"}, "gameCodeIn": {"in": "query", "name": "gameCode__in", "description": "list of game codes separated by commas", "required": false, "type": "string"}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "currencyStrictEquality": {"in": "query", "name": "currency", "description": "currency equal to value", "required": false, "type": "string"}, "currencyIn": {"in": "query", "name": "currency__in", "description": "currencies separated by commas", "required": false, "type": "string"}, "recoveryType": {"name": "recoveryType", "in": "query", "description": "Recovery type for rounds that were resolved manually", "required": false, "type": "string", "enum": ["revert", "force-finish", "finalize"]}, "recoveryType__in": {"in": "query", "name": "recoveryType__in", "description": "list of recovery types for rounds that were resolved manually", "required": false, "type": "string"}, "createdAt": {"in": "query", "name": "createdAt", "description": "sharp time in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__gt": {"in": "query", "name": "createdAt__gt", "description": "date in ISO 8601 timestamp greater than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__gte": {"in": "query", "name": "createdAt__gte", "description": "date in ISO 8601 timestamp greater than or equal(e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__lt": {"in": "query", "name": "createdAt__lt", "description": "date in ISO 8601 timestamp less than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "createdAt__lte": {"in": "query", "name": "createdAt__lte", "description": "date in ISO 8601 timestamp less than or equal (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt": {"in": "query", "name": "updatedAt", "description": "date in ISO 8601 timestamp greater than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__gt": {"in": "query", "name": "updatedAt__gt", "description": "date in ISO 8601 timestamp greater than or equal (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__gte": {"in": "query", "name": "updatedAt__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__lt": {"in": "query", "name": "updatedAt__lt", "description": "date in ISO 8601 timestamp less than (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "updatedAt__lte": {"in": "query", "name": "updatedAt__lte", "description": "date in ISO 8601 timestamp less than or equal (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "isPublicChatBlock": {"name": "isPublicChatBlock", "in": "query", "description": "if true retrun players with block public chat", "required": false, "type": "boolean"}, "isPrivateChatBlock": {"name": "isPrivateChatBlock", "in": "query", "description": "if true return players with block private chat", "required": false, "type": "boolean"}, "hasWarn": {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "if true return players that has warn in chat", "required": false, "type": "boolean"}, "playerNicknameContains": {"name": "nickname__contains", "in": "query", "type": "string", "description": "player nickname contains", "required": false}, "brandId": {"name": "brandId", "in": "query", "type": "number", "description": "brand id equal", "required": false}}}