import { NextFunction, Request, Response } from "express";
import { sleep } from "@skywind-group/sw-utils";
import config from "../config";
import logger from "./logger";
import { ValueIsMissing } from "../errors";
import { IncorrectPlayerIdOrToken } from "../popErrors";
import { settings } from "../models/settings";

const rn = require("random-number");

const log = logger("api");
const KEYS_TO_FIND_REQUEST_VALUE = ["query", "params", "body"];
const allowedHeaders = config.logParams.allowedHeaders.split(",");

export async function emulateLatency(req: Request, res: Response, next: NextFunction) {
    // Final latency = fixed latency + rand[0...spreading]
    const spreading = Number.isFinite(config.mockLatencySpreading) ? config.mockLatencySpreading : 0;
    const sleepTime = Math.round(config.mockLatency + Math.random() * spreading);
    await sleep(sleepTime);
    next();
}

export function sumMajorUnits(amount1: number, amount2: number): number {
    if (settings.aroundAmount) {
        const aggregator = Math.round(config.currencyUnitMultiplier * amount1) +
            Math.round(config.currencyUnitMultiplier * amount2);
        return aggregator / config.currencyUnitMultiplier;
    } else {
        return (amount1 + amount2);
    }
}

export function toMinorUnits(value: number): number {
    return Math.round(value * 100);
}

export function toMajorUnits(value: number): number {
    return value
           ? value / 100
           : 0;
}

export function parseBoolean(str) {
    if (typeof str === "string") {
        str = str.toLowerCase().trim();
    }

    switch (str) {
        case "true":
        case "1":
            return true;
        case "false":
        case "0":
        case null:
            return false;
        default:
            return Boolean(str);
    }
}

export function send(req: Request, res: Response, body: object) {
    if (req.header("Accept") === "application/json") {
        res.send(body);
        return null;
    }

    let responseString: string = "";
    for (const property in body) {
        if (!body.hasOwnProperty(property)) {
            continue;
        }
        responseString += `${property}=${body[property]}\n`;
    }
    res.send(responseString.slice(0, -1));
    return null;
}

export function getParamValues(req: Request, params: string[], optional: boolean = false): any[] {
    const values: any[] = [];
    for (const param of params) {
        const keyWithDefinedValue = KEYS_TO_FIND_REQUEST_VALUE.find(key => typeof req[key][param] !== "undefined");
        const value = req[keyWithDefinedValue] && req[keyWithDefinedValue][param];

        if (typeof value === "undefined" || value === null || value === "") {
            if (!optional) {
                if (config.serverName === "pop") {
                    throw new IncorrectPlayerIdOrToken();
                } else {
                    throw new ValueIsMissing(param);
                }
            }
        }
        values.push(value);
    }
    return values;
}

export function getTextResponse(body: object): string {
    if (typeof body === "string") {
        return body;
    }
    let responseString: string = "";

    for (const property in body) {
        if (!body.hasOwnProperty(property) || property === "responseStatus" || typeof body[property] === "undefined") {
            continue;
        }
        responseString += `${property}=${body[property]}\n`;
    }
    return responseString.slice(0, -1);
}

const HTTP_METHODS_TO_HIDE_INFO = new Set(["POST", "PATCH", "PUT"]);

export function buildBodyToLog(req: Request) {
    if (req.body && Object.keys(req.body).length > 0 &&
        HTTP_METHODS_TO_HIDE_INFO.has(req.method)) {

        return getSecuredObjectData(req.body);
    }
}

export function buildHeadersToLog(req: Request) {
    if (!allowedHeaders || !allowedHeaders.length) {
        return {};
    }

    return Object.keys(req.headers)
        .filter(header => allowedHeaders.includes(header))
        .reduce((result: object, header: string) => {
            return { ...result, [header]: req.headers[header] };
        }, {});
}

function getSecuredObjectData(dataObject: object) {
    const securedData = {};
    for (const key in dataObject) {
        if (!dataObject.hasOwnProperty(key)) {
            continue;
        }
        const value = dataObject[key];
        if (isSecurityProperty(key)) {
            if (value) {
                securedData[key] = "***";
            } else {
                securedData[key] = value;
            }
        } else if (value !== null && typeof value === "object") {
            securedData[key] = getSecuredObjectData(value);
        } else {
            securedData[key] = value;
        }
    }
    return securedData;
}

function isSecurityProperty(key: string) {
    return config.logParams.secureKeys.includes(key);
}

export function randomNumber(length: number = 4) {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length);
    return rn.generator({ min, max, integer: true })();
}

export const JSON_ACCEPT_TYPE = "application/json";
export const XML_TYPE = "text/xml";
export const XML_ACCEPT_TYPE = "application/xml";
export const TICKET_TYPE_TERMINAL = "terminal";
export const TICKET_TYPE_PLAYER = "player";
export const MOCK_RESPONSE_HEADER = "x-sw-mock-response";
