import {
    AdapterRequest,
    AuthenticateResponse,
    BalanceResponse,
    BaseResponse,
    ISB_ACTION,
    REQUEST_COMMAND,
    RequestEx,
    RESPONSE_STATUS,
} from "./entities";

import { extractAdapterRequestData } from "./utils";
import { Customer, Merchant } from "../../models/merchant";
import * as Transaction from "../../models/transaction";
import { WALLET_ACTION } from "../../models/transaction";
import * as Errors from "./errors";
import { createCustomerSession } from "../../service/session";
import * as SessionModel from "../../../skywind/models/session";
import { throwCustomError } from "../../service/customError";
import { extraDataHandler } from "../../service/extraData";

export class ISBService {
    private constructor(
        private merchant: Merchant,
        private customer: Customer
    ) {}

    private currentBalanceResonse(): BalanceResponse {
        return {
            status: RESPONSE_STATUS.SUCCESS,
            currency: this.customer.currency_code,
            balance: this.customer.balance.amount
        };
    }

    @throwCustomError({ action: ISB_ACTION.INIT })
    private async initSession(req: AdapterRequest): Promise<AuthenticateResponse> {
        const response: AuthenticateResponse = {
            status: RESPONSE_STATUS.SUCCESS,
            sessionid: createCustomerSession(this.merchant, this.customer),
            playerid: req.playerid,
            username: req.operator,
            currency: req.currency,
            balance: this.customer.balance.amount
        };
        if (this.customer.bet_limit) {
            response.max_bet = +this.customer.bet_limit;
        }
        return response;
    }

    @throwCustomError({ action: ISB_ACTION.TOKEN })
    private async token(req: AdapterRequest): Promise<any> {
        return {
            token: req.token,
            ISBskinid: req.ISBskinid,
            ISBgameid: req.ISBgameid,
        };
    }

    @throwCustomError({ action: ISB_ACTION.BALANCE })
    private async getBalance(): Promise<BalanceResponse> {
        return this.currentBalanceResonse();
    }

    @throwCustomError({ action: ISB_ACTION.BET })
    @extraDataHandler({ action: ISB_ACTION.BET })
    private async bet(req: AdapterRequest): Promise<BalanceResponse> {
        const action = WALLET_ACTION.debit;
        const amount = req.action.parameters.amount;

        if (this.customer.balance.amount < amount) {
            throw new Errors.InsufficientBalanceError();
        }

        const transactionId = req.action.parameters.transactionid;
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action,
                Math.abs(req.action.parameters.amount),
                this.customer.cust_id
            );
            this.customer.balance.amount = this.customer.balance.amount
                + Math.abs(req.action.parameters.amount) * action;
        }

        return this.currentBalanceResonse();
    }

    @throwCustomError({ action: ISB_ACTION.WIN })
    @extraDataHandler({ action: ISB_ACTION.WIN })
    private async win(req: AdapterRequest): Promise<any> {
        const action = WALLET_ACTION.credit;
        const transactionId = req.action.parameters.transactionid;
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action,
                Math.abs(req.action.parameters.amount),
                this.customer.cust_id
            );
            this.customer.balance.amount = this.customer.balance.amount
                + Math.abs(req.action.parameters.amount) * action;
        }

        return this.currentBalanceResonse();
    }

    @throwCustomError({ action: ISB_ACTION.DEPOSIT })
    @extraDataHandler({ action: ISB_ACTION.DEPOSIT })
    private async deposit(req: AdapterRequest): Promise<any> {
        const action = WALLET_ACTION.credit;
        const transactionId = req.action.parameters.transactionid;
        const transaction = Transaction.getById(transactionId, action);

        if (!transaction) {
            Transaction.setById(transactionId, action,
                Math.abs(req.action.parameters.amount),
                this.customer.cust_id
            );
            this.customer.balance.amount = this.customer.balance.amount
                + Math.abs(req.action.parameters.amount) * action;
        }

        return this.currentBalanceResonse();
    }

    @throwCustomError({ action: ISB_ACTION.CANCEL })
    @extraDataHandler({ action: ISB_ACTION.CANCEL })
    private async cancel(req: AdapterRequest): Promise<BalanceResponse> {
        const transactionId = req.action.parameters.transactionid;
        const rollbackTransaction = Transaction.getById(transactionId, WALLET_ACTION.rollback);

        if (!rollbackTransaction) {
            const originalTransaction = Transaction.getById(transactionId, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new Errors.InvalidRollbackError();
            }

            const [, , , isRollback] = originalTransaction;

            if (!isRollback) {
                Transaction.rollbackById(transactionId, WALLET_ACTION.debit);
                this.customer.balance.amount = this.customer.balance.amount
                    - Math.abs(req.action.parameters.amount);
            }

            Transaction.setById(transactionId, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        }

        return this.currentBalanceResonse();
    }

    @throwCustomError({ action: ISB_ACTION.END })
    @extraDataHandler({ action: ISB_ACTION.END })
    private async end(): Promise<BalanceResponse> {
        if (this.customer) {
            SessionModel.deleteCustomerSession(this.customer.cust_id);
            return this.currentBalanceResonse();
        } else {
            return {
                "status": RESPONSE_STATUS.SUCCESS,
                "currency": "EUR",
                "balance": 0
            };
        }
    }

    @throwCustomError({ action: ISB_ACTION.ROUNDDETAILS })
    @extraDataHandler({ action: ISB_ACTION.ROUNDDETAILS })
    private async rounddetails(req: AdapterRequest): Promise<BaseResponse> {
        return {
            "status": RESPONSE_STATUS.SUCCESS
        };
    }

    private dispatchCommand = async (req: AdapterRequest): Promise<BaseResponse> => {
        const cmd = req.action.command;
        if (cmd === REQUEST_COMMAND.INIT) {
            return this.initSession(req);
        } else if (cmd === REQUEST_COMMAND.BALANCE) {
            return this.getBalance();
        } else if (cmd === REQUEST_COMMAND.BET) {
            return this.bet(req);
        } else if (cmd === REQUEST_COMMAND.WIN) {
            return this.win(req);
        } else if (cmd === REQUEST_COMMAND.DEPOSIT) {
            return this.deposit(req);
        } else if (cmd === REQUEST_COMMAND.CANCEL) {
            return this.cancel(req);
        } else if (cmd === REQUEST_COMMAND.END) {
            return this.end();
        } else if (cmd === REQUEST_COMMAND.TOKEN) {
            return this.token(req);
        } else if (cmd === REQUEST_COMMAND.ROUNDDETAILS) {
            return this.rounddetails(req);
        }
    };

    public static async getResponse(req: RequestEx): Promise<BaseResponse> {
        return new ISBService(req.merchant, req.customer)
            .dispatchCommand(extractAdapterRequestData(req));
    }
}
