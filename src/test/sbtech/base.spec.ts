import { Application } from "express";
import { getApplication } from "../../skywind/serverSBTech";
import { getDefaultCustomer } from "../../skywind/service/customer";
import { SBTechTestFixtures } from "./fixtures";

import { Customer, setAll } from "../../skywind/models/merchant";
import * as Session from "../../skywind/models/session";
import * as TransactionModel from "../../skywind/models/transaction";
import { createActionError, RaiseType } from "../../skywind/models/customError";
import { createActionData } from "../../skywind/models/extraData";

export class SBTechBaseSpec {
    public urlPathToInitSession: string = "/api/initSession";
    public urlPathToGetBalance: string = "/api/getBalance";
    public urlPathToDebit: string = "/api/debit";
    public urlPathToCredit: string = "/api/credit";
    public urlPathToRollback: string = "/api/rollbackTransaction";
    public server: Application;
    public fixtures = new SBTechTestFixtures();

    public ticketExpirationTimeDefault: number = 5 * 60 * 1000; // 5 min

    public async before() {
        this.server = await getApplication();
        Session.clearAll();
        TransactionModel.setAll({});

        setAll({ [this.fixtures.merchantCode]: this.fixtures.merchant });
        this.addPlayerToMerchant(this.fixtures.playerCode);

        // this.sessionId = createCustomerSession(merchant, this.customer);
    }

    public addPlayerToMerchant(name: string): Customer {
        const customer = getDefaultCustomer(name, "USD", true, 100);
        this.fixtures.merchant.customers[customer.cust_id] = customer;

        return customer;
    }

    public createCustomError(httpStatus: number, errorCode: number, action: string) {
        createActionError(this.fixtures.merchantCode, this.fixtures.playerCode, action, RaiseType.AFTER, {
            http_response_status: httpStatus,
            errorCode: errorCode
        });
    }

    public createExtraData(data: object, action: string) {
        createActionData(this.fixtures.merchantCode, this.fixtures.playerCode, action, data);
    }
}
