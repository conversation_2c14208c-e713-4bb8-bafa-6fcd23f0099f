import { Customer, Merchant } from "../models/merchant";
import * as ExtraDataModel from "../models/extraData";
import * as deepmerge from "deepmerge";
import { RaiseType } from "../models/customError";

export function getExtraData(response: any, action: string, merchant: Merchant, customer: Customer) {
    // safely return the value from a nested key in an object
    const extraData = ExtraDataModel.getActionData(merchant.merch_id, customer.cust_id,
        action);

    if (!extraData) {
        return response;
    }
    ExtraDataModel.deleteActionData(merchant.merch_id, customer.cust_id, action);
    return deepmerge(response, extraData);
}

export interface ExtraDataOptions {
    action?: string;
}

// The function that this decorator is applied to MUST be an async function
export function extraDataHandler(options: ExtraDataOptions = {}) {
    return (target, key, descriptor) => {
        const originalMethod = descriptor.value;

        descriptor.value = async function(...args) {
            const result = await originalMethod.apply(this, args);

            const action = (options.action || key).toLowerCase();
            return getExtraData(result, action, this.merchant, this.customer);
        };

    };
}
