///<reference path="../../skywind/api/routers.ts"/>

import logger from "../../skywind/utils/logger";
import { Application } from "express";

const chai = require("chai");
chai.use(require("chai-shallow-deep-equal"));
const expect = chai.expect;

const log = logger("ipm-mock:tests");
const request = require("supertest");

export const createMerchant = async (server: Application,
                                     merchantId: string,
                                     merchantPassword: string) => {
    log.debug("Create merchant: " + merchantId);

    await request(server)
        .post("/v1/merchant")
        .send({
            merch_id: merchantId,
            merch_pwd: merchantPassword,
            multiple_session: true
        })
        .expect(201)
        .expect({
            merch_id: merchantId,
            merch_pwd: merchantPassword,
            customers: {},
            isPromoInternal: false,
            multiple_session: true,
        });
};

export const addMoneyForCustomer = async (server: Application,
                                 merchantId: string,
                                 customerId: string,
                                 balance: number,
                                 addBalance?: number) => {
    if (!addBalance) {
        addBalance = balance;
    }
    log.debug(`Add ${addBalance} to balance for customer: ${customerId}`);
    await request(server)
        .post(`/v1/merchant/${merchantId}/customer/${customerId}/balance/${addBalance}`)
        .expect(200)
        .then(response => {
            expect(response.body).to.have.property("balance");
            expect(response.body.balance).to.have.property("currency_code", "USD");
        });
};

export const createTicketForCustomer = async (server: Application,
                                     merchantId: string,
                                     customerId: string) => {
    let ticket: string;
    log.debug("Create ticket for customer: " + customerId);

    await request(server)
        .get(`/v1/merchant/${merchantId}/customer/${customerId}/ticket`)
        .expect(200)
        .then(response => {

            expect(response.text).to.not.be.null;
            ticket = response.text;
        });
    return ticket;
};

export const createCustomer = async (server: Application,
                            merchantId: string,
                            customerId: string) => {
    log.debug("Create customer: " + customerId);

    const data = {
        "cust_id": customerId,
        "cust_login": "login4" + customerId,
        "currency_code": "USD",
        "language": "en",
        "country": "CN",
        "test_cust": true,
        "first_name": "",
        "last_name": "",
        "date_of_birth": new Date().toJSON(),
        "status": "normal",
        "bet_limit": 1500
    };
    await request(server)
        .post(`/v1/merchant/${merchantId}/customer`)
        .send(data)
        .expect(201)
        .then(response => {
            data["balance"] = {
                "amount": 0,
                "currency_code": "USD",
            };
            data["bonusBalance"] = 0;
            expect(response.body).to.deep.equal(data);
        })

    ;
};

export const addFreebets = async (server: Application, merchantId: string, customerId: string) => {
    const data = {
        "freeBets": {
            "coin": 0.1,
            "count": 100
        }
    };
    await request(server)
        .patch(`/v1/merchant/${merchantId}/customer/${customerId}`)
        .send(data)
        .expect(200)
        .then(response => {
            data["freeBets"] = {
                "coin": 0.1,
                "count": 100
            };
        });
};

export const clearFreebets = async (server: Application, merchantId: string, customerId: string) => {
    const data = {
        "freeBets": null
    };
    await request(server)
        .patch(`/v1/merchant/${merchantId}/customer/${customerId}`)
        .send(data)
        .expect(200)
        .then(response => {
            expect(response.body["freeBets"]).eq(null);
        });
};

export const createError = async (server: Application,
                         merchantId: string,
                         customerId: string,
                         customAction: string,
                         customError: object) => {
    log.debug("Create error: " + customAction + " for customer " + customerId);

    await request(server)
        .post(`/v1/merchant/${merchantId}/customer/${customerId}/error/${customAction}/raiseType/after`)
        .send(customError)
        .expect(201)
        .expect({
            "message": "error created",
            "error": customError,
        });
};
