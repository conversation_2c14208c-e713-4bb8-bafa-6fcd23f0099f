import * as uuid from "uuid";
import {
    SBTechInitSessionRequest,
    SBTechInitSessionResponse,
    SBTechDebitRequest,
    SBTechDebitResponse,
    SBTechCreditRequest,
    SBTechCreditResponse,
    SBTechGetBalanceRequest,
    SBTechGetBalanceResponse,
    SBTechEndRoundRequest,
    SBTechEndRoundResponse,
    SBTechRollbackTransactionRequest,
    SBTechRollbackTransactionResponse,
    WALLET_TYPE
} from "../../entities/sbtech";
import * as SBTechErrors from "../../errorsSBTech";

import { Customer, Merchant } from "../../models/merchant";
import { throwCustomError } from "../customError";
import { extraDataHandler } from "../extraData";
import * as TransactionModel from "../../models/transaction";
import { sumMajorUnits } from "../../utils/helper";
import { TRANSACTION_INDEX, WALLET_ACTION } from "../../models/transaction";

export interface SBTechService {
    initSession(data: SBTechInitSessionRequest): Promise<SBTechInitSessionResponse>;

    debit(data: SBTechDebitRequest): Promise<SBTechDebitResponse>;

    credit(data: SBTechCreditRequest): Promise<SBTechCreditResponse>;

    getBalance(data: SBTechGetBalanceRequest): Promise<SBTechGetBalanceResponse>;

    endRound(data: SBTechEndRoundRequest): Promise<SBTechEndRoundResponse>;

    rollbackTransaction(data: SBTechRollbackTransactionRequest): Promise<SBTechRollbackTransactionResponse>;
}

export class SBTechServiceImpl implements SBTechService {
    constructor(public merchant: Merchant, public customer?: Customer) {
    }

    @throwCustomError({ action: "initSession" })
    @extraDataHandler()
    public async initSession(data: SBTechInitSessionRequest): Promise<SBTechInitSessionResponse> {
        return {
            playerId: this.customer.cust_id,
            brandId: this.merchant.merch_id,
            isTestPlayer: this.customer.test_cust,
            currencyCode: this.customer.currency_code,
            countryCode: this.customer.country,
            sessionId: uuid.v4(),
            jurisdiction: "",
            balance: this.toMinorUnits(this.customer.balance.amount),
            timestamp: this.getTimestamp(), // what format of date ???
        } as SBTechInitSessionResponse;
    }

    @throwCustomError({ action: "debit" })
    @extraDataHandler()
    public async debit(data: SBTechDebitRequest): Promise<SBTechDebitResponse> {
        const { trxId, amount } = data;

        this.validateTransaction(WALLET_ACTION.debit, trxId);
        this.checkPlayerBalance(
            this.toMinorUnits(this.customer.balance.amount), Math.abs(amount) * WALLET_ACTION.debit);

        const transaction = TransactionModel.setById(trxId, WALLET_ACTION.debit, amount, this.customer.cust_id);
        this.customer.balance.amount = sumMajorUnits(
            this.customer.balance.amount, this.toMajorUnits(amount * WALLET_ACTION.debit));

        return {
            trxId: transaction[TRANSACTION_INDEX.EXTERNAL_ID],
            walletType: WALLET_TYPE.REAL, // what type should be ???
            sessionId: data.sessionId,
            balance: this.toMinorUnits(this.customer.balance.amount),
            timestamp: this.getTimestamp(),
        } as SBTechDebitResponse;
    }

    @throwCustomError({ action: "credit" })
    @extraDataHandler()
    public async credit(data: SBTechCreditRequest): Promise<SBTechCreditResponse> {
        const { trxId, amount, debitTrxId } = data;

        // this.validateTransaction(WALLET_ACTION.debit, debitTrxId); // is this check necessary ???
        this.validateTransaction(WALLET_ACTION.credit, trxId);

        const transaction = TransactionModel.setById(trxId, WALLET_ACTION.credit, amount, this.customer.cust_id);
        this.customer.balance.amount = sumMajorUnits(
            this.customer.balance.amount, this.toMajorUnits(amount * WALLET_ACTION.credit));

        return {
            trxId: transaction[TRANSACTION_INDEX.EXTERNAL_ID],
            sessionId: data.sessionId,
            balance: this.toMinorUnits(this.customer.balance.amount),
            timestamp: this.getTimestamp(),
        } as SBTechCreditResponse;
    }

    @throwCustomError({ action: "rollbackTransaction" })
    @extraDataHandler()
    public async rollbackTransaction(data: SBTechRollbackTransactionRequest): Promise<SBTechRollbackTransactionResponse> {
        const { trxId, debitTrxId } = data;

        let rollbackTransaction = {};
        if (!TransactionModel.getById(trxId, WALLET_ACTION.rollback)) {
            const originalTransaction = TransactionModel.getById(debitTrxId, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new SBTechErrors.TransactionDoesntExistError();
            }

            const [_, amount, __, isRollback] = originalTransaction;
            if (isRollback) {
                throw new SBTechErrors.InternalError(debitTrxId);
            }

            TransactionModel.rollbackById(debitTrxId, WALLET_ACTION.debit);
            this.customer.balance.amount = this.toMajorUnits(
                this.toMinorUnits(this.customer.balance.amount) - amount);
            rollbackTransaction = TransactionModel.setById(trxId, WALLET_ACTION.rollback,
                0, this.customer.cust_id);
        }

        return {
            trxId: rollbackTransaction[TRANSACTION_INDEX.EXTERNAL_ID],
            sessionId: data.sessionId,
            balance: this.toMinorUnits(this.customer.balance.amount),
            timestamp: this.getTimestamp(),
        } as SBTechRollbackTransactionResponse;
    }

    @throwCustomError({ action: "getBalance" })
    @extraDataHandler()
    public async getBalance(data: SBTechGetBalanceRequest): Promise<SBTechGetBalanceResponse> {
        return {
            balance: this.toMinorUnits(this.customer.balance.amount),
            timestamp: this.getTimestamp(), // what format of date ???
            sessionId: data.sessionId,
        } as SBTechGetBalanceResponse;
    }

    @throwCustomError({ action: "endRound" })
    @extraDataHandler()
    public async endRound(data: SBTechEndRoundRequest): Promise<SBTechEndRoundResponse> {
        const { trxId } = data;

        this.validateTransaction(WALLET_ACTION.credit, trxId); // check ???

        const transaction = TransactionModel.setById(trxId, WALLET_ACTION.credit, 0, this.customer.cust_id);

        return {
            trxId: transaction[TRANSACTION_INDEX.EXTERNAL_ID],
            sessionId: data.sessionId,
            balance: this.toMinorUnits(this.customer.balance.amount),
            timestamp: this.getTimestamp(),
        } as SBTechEndRoundResponse;
    }

    private validateTransaction(type: WALLET_ACTION, trxId: string) {
        const transaction = TransactionModel.getById(trxId, type);

        if (transaction) {
            throw new SBTechErrors.InternalError();
        }
    }

    private checkPlayerBalance(playerBalance: number, amount: number) {
        if (playerBalance + amount < 0) {
            throw new SBTechErrors.InsufficientFundsRealMoneyError(); // InsufficientFundsError ???
        }
    }

    private toMinorUnits(value: number): number {
        return value * 100;
    }

    private toMajorUnits(value: number): number {
        return value
               ? value / 100
               : 0;
    }

    private getTimestamp() {
        return Date.parse(new Date().toString());
    }
}

export function getSBTechService(merchant: Merchant, customer?: Customer): SBTechServiceImpl {
    return new SBTechServiceImpl(merchant, customer);
}
