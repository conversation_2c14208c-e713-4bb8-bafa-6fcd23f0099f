import { Application, Request, Response, NextFunction } from "express";
import { measures } from "@skywind-group/sw-utils";
import config from "./config";
import { pipeline } from "node:stream/promises";

const compression = require("compression");
const bodyParser = require("body-parser");
const methodOverride = require("method-override");
const cookieParser = require("cookie-parser");
const expressValidator = require("express-validator");

export function setupBase(app: Application) {

    app.use(measures.measureProvider.instrumentFunction(cookieParser(), "express.cookieParser") as any);
    app.use(measures.measureProvider.instrumentFunction(compression({
        threshold: config.compressionThreshold
    }), "express.compression") as any);
    app.use(measures.measureProvider.instrumentFunction(bodyParser.urlencoded({
        extended: true, limit: config.bodyParserUrlLimit
    }), "express.urlencoded") as any);
    app.use(measures.measureProvider.instrumentFunction(bodyParser.json({
        limit: config.bodyParserJsonLimit
    }), "express.jsonBodyParser") as any);

    app.use(methodOverride());
}

function isPrometheusMonitoring() {
    return measures.providerName === "prometheus";
}

function isMemoryMonitoring() {
    return measures.providerName === "memory";
}

export function setupMetricHandlers(app: Application) {
    if (isPrometheusMonitoring()) {
        app.get("/metrics", async (req: Request, res: Response) => {
            measures.measureProvider.setTransaction("Prometheus metrics");
            const metrics = await measures.measureProvider.getMeasuresStream();
            await pipeline(metrics, res);
        });
    } else if (isMemoryMonitoring()) {
        app.use("/v1/", require("./api/measures").default);
    }
}

export function setupValidatorAndHeaders(app: Application): Application {

    app.use(validator);
    app.use("/*", addHeaderCORS);

    return app;
}

const DEFAULT_MAX_AGE_OPTIONS = (30 * 24 * 60 * 60).toString();

function addHeaderCORS(req: Request, res: Response, next: NextFunction) {
    if (res.headersSent) {
        next();
        return;
    }
    /* TODO:
     * we currently use "*" (all http origins),
     * it must be replaced with the origins white list, when implemented
     */
    res.setHeader("Access-Control-Allow-Origin", "*");
    if (req.method === "OPTIONS") {
        /* TODO:
         * list of headers must be defined
         */
        if (req.header("Access-Control-Request-Headers")) {
            res.setHeader("Access-Control-Allow-Headers", req.header("Access-Control-Request-Headers"));
        }
        res.setHeader("Access-Control-Max-Age", DEFAULT_MAX_AGE_OPTIONS);

        res.status(204).end();
    } else {
        next();
    }
}

const validator = expressValidator({
    customValidators: {
        isWord: value => {
            return /^[\w-]+$/.test(value);
        },
        isPassword: value => {
            // validate that password is longer than or equal 8 letters and
            // contains at least one letter, one uppercase letter and one digit
            return /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)^.{8,}$/.test(value);
        },
        isStatus: value => {
            return value === "normal" || value === "suspended";
        },
    },
});
