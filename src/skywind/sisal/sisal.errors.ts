import { BaseErrorToLog, ErrorInfoToLog } from "../errors";

export class SisalError extends Error implements BaseErrorToLog {
    constructor(public code: number, public message: string = "") {
        super(message);
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.code,
                message: this.message,
                stack: this.stack
            }
        };
    }
}

export class OperationExecutedError extends SisalError {
    constructor() {
        super(0, "Operation Executed");
    }
}

export class InvalidBetAmountError extends SisalError {
    constructor() {
        super(2000, "Invalid bet amount");
    }
}

export class ValidationError extends SisalError {
    constructor() {
        super(6000, "Validation error");
    }
}

export class InvalidXAuthError extends SisalError {
    constructor(message = "Invalid Xauth") {
        super(8000, message);
    }
}

export class PlayerNotFoundError extends SisalError {
    constructor() {
        super(9001, "Player not found");
    }
}

export class PlayerGameSessionNotFoundError extends SisalError {
    constructor() {
        super(9002, "Player’s Game Session not found");
    }
}

export class GameSessionNotFoundError extends SisalError {
    constructor() {
        super(9004, "Game Session not found");
    }
}

export class PlayerGamePhaseNotFoundError extends SisalError {
    constructor() {
        super(9005, "Player’s Game Phase not found");
    }
}

export class PlayerGamePhaseWagerNotFoundError extends SisalError {
    constructor() {
        super(9017, "Player’s Game Phase Wager not found");
    }
}

export class PlayerGameSessionNotOpenedError extends SisalError {
    constructor() {
        super(9018, "Player’s Game Session not Opened");
    }
}

export class InvalidCancelWagerError extends SisalError {
    constructor() {
        super(9020, "Invalid cancel wager");
    }
}

export class GamePhaseNotFoundError extends SisalError {
    constructor() {
        super(9021, "Game Phase not found");
    }
}

export class PlayerGamePhaseNotOpenedError extends SisalError {
    constructor() {
        super(9022, "Player’s Game Phase not Opened");
    }
}

export class InvalidGameForSessionError extends SisalError {
    constructor() {
        super(9100, "Invalid Game for Session");
    }
}

export class UnauthorizedError extends SisalError {
    constructor() {
        super(9900, "Unauthorized");
    }
}

export class GenericError extends SisalError {
    constructor() {
        super(9999, "Generic Error");
    }
}

export enum SISAL_ERROR_CODE {
    OPERATION_EXECUTED_ERROR = 0,
    INVALID_BET_AMOUNT_ERROR = 2000,
    VALIDATION_ERROR = 6000,
    INVALID_XAUTH_ERROR = 8000,
    PLAYER_NOT_FOUND_ERROR = 9001,
    PLAYER_GAME_SESSION_NOT_FOUND_ERROR = 9002,
    GAME_SESSION_NOT_FOUND_ERROR = 9004,
    PLAYER_GAME_PHASE_NOT_FOUND_ERROR = 9005,
    PLAYER_GAME_PHASE_WAGER_NOT_FOUND_ERROR = 9007,
    PLAYER_GAME_SESSION_NOT_OPENED_ERROR = 9018,
    INVALID_CANCEL_WAGER_ERROR = 9020,
    GAME_PHASE_NOT_FOUND_ERROR = 9021,
    PLAYER_GAME_PHASE_NOT_OPENED_ERROR = 9022,
    INVALID_GAME_FOR_SESSION_ERROR = 9100,
    UNAUTHORIZED_ERROR = 9900,
    GENERIC_ERROR = 9999
}
