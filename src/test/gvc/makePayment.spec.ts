import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");
import {
    FAKE_GAME_ROUND_ID,
    SKYWIND_PARTNER_ID,
    GVCFundsTransferResponse
} from "../../skywind/entities/gvc";

import { GVCBaseSpec } from "./baseSpec";
import { createTicket } from "../../skywind/service/ticket";

@suite("[GVC.payment]")
class GVCBalanceSpec extends GVCBaseSpec {

    @test("should make debit operation")
    public async testDebit() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });

        const expectedResponse: GVCFundsTransferResponse = {
            accountId: `${this.merchantCode}__${this.playerCode}`,
            partnerId: SKYWIND_PARTNER_ID,
            currencyCode: "USD",
            gameCode: "GAMECODE001",
            gameContext: "GAMECONTEXT001",
            balance: 9700, // minor units
            bpGameRoundId: FAKE_GAME_ROUND_ID,
            transactionId: "PQ+OKOAAChlsAKALXPQ+OA=_bet",
            status: {
                statusCode: "0",
                statusMessage: "success",
            }
        };

        const actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);

        delete actualResponse.body.bpTransactionId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make credit operation")
    public async testCredit() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedCreditRequest = Object.assign(this.creditRequest, { secureToken: ticket.id });

        const expectedResponse: GVCFundsTransferResponse = {
            accountId: `${this.merchantCode}__${this.playerCode}`,
            partnerId: SKYWIND_PARTNER_ID,
            currencyCode: "USD",
            gameCode: "GAMECODE001",
            gameContext: "GAMECONTEXT001",
            balance: 10500, // minor units
            bpGameRoundId: FAKE_GAME_ROUND_ID,
            transactionId: "PQ+OKOAAChlsAKALXPQ+OA=_win",
            status: {
                statusCode: "0",
                statusMessage: "success",
            }
        };

        const actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedCreditRequest)
            .expect(200);

        delete actualResponse.body.bpTransactionId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should make debit and credit operations")
    public async testDebitAndCredit() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });
        const updatedCreditRequest = Object.assign(this.creditRequest, { secureToken: ticket.id });

        let expectedBalanceAfterDebit = 9700;
        let actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);
        expect(actualResponse.body.balance).to.be.equal(expectedBalanceAfterDebit);

        let expectedBalanceAfterCredit = 10200;
        actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedCreditRequest)
            .expect(200);
        expect(actualResponse.body.balance).to.be.equal(expectedBalanceAfterCredit);

        expectedBalanceAfterDebit = 9900;
        updatedDebitRequest.transactionId = "1PQ+OKOAAChlsAKALXPQ+OA=_bet";
        actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);
        expect(actualResponse.body.balance).to.be.equal(expectedBalanceAfterDebit);

        expectedBalanceAfterDebit = 9600;
        updatedDebitRequest.transactionId = "2PQ+OKOAAChlsAKALXPQ+OA=_bet";
        actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);
        expect(actualResponse.body.balance).to.be.equal(expectedBalanceAfterDebit);

        expectedBalanceAfterCredit = 10100;
        updatedCreditRequest.transactionId = "1PQ+OKOAAChlsAKALXPQ+OA=_win";
        actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedCreditRequest)
            .expect(200);
        expect(actualResponse.body.balance).to.be.equal(expectedBalanceAfterCredit);

        expectedBalanceAfterCredit = 10600;
        updatedCreditRequest.transactionId = "2PQ+OKOAAChlsAKALXPQ+OA=_win";
        actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedCreditRequest)
            .expect(200);
        expect(actualResponse.body.balance).to.be.equal(expectedBalanceAfterCredit);
    }

    @test("should throw UnknownTransactionError when operation not found")
    public async testUnknownOperation() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedCreditRequest = Object.assign(this.creditRequest, { secureToken: ticket.id });

        updatedCreditRequest.transactionId = "1PQ+OKOAAChlsAKALXPQ+OA="; // no _bet or _win

        const actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedCreditRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(200);
    }

    @test("should throw DuplicateTransactionRequestError when such transaction has already processed")
    public async testDuplicateTransaction() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedCreditRequest = Object.assign(this.creditRequest, { secureToken: ticket.id });

        await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedCreditRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedCreditRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(120);
    }

    @test("should throw InsufficientBalanceError")
    public async testInsufficientBalance() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });
        this.customer.balance.amount = 1;

        const actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(104);
        this.customer.balance.amount = 100; // revert default player balance
    }

    @test("should throw custom error")
    public async testCustomError() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });

        this.createCustomError(200, 109, "makepayment");

        const actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(109);
    }

    @test("should return extra data")
    public async testExtraData() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });

        this.createExtraData({ isTest: true }, "makepayment");

        const actualResponse = await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);

        expect(actualResponse.body.isTest).to.be.true;
    }
}
