import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { RELAX_JURISDICTION, VerifyTokenRequest } from "../../skywind/entities/relax";
import config from "../../skywind/config";
import { RelaxBaseSpec } from "./base.spec";
import { createTicket } from "../../skywind/service/ticket";
import { INTEGRATION_VERSION } from "../../config/integrationVersion";

const request = require("supertest");

@suite
class RelaxVerifyTokenSpec extends RelaxBaseSpec {
    @test
    public async verifyTokenSuccess() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const verifyTokenRequest: VerifyTokenRequest = {
            channel: "web",
            clientid: "test",
            token: ticket.id,
            gameref: "rlx.sw.sw.sw_mrmnky",
            partnerid: this.merchantCode,
            ip: "***********"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/verifytoken`)
            .auth(config.auth.username, config.auth.password)
            .send(verifyTokenRequest)
            .expect(200);

        expect(response.body.jurisdiction).to.be.equal(RELAX_JURISDICTION.MT);
        expect(response.body.playerid).to.be.exist;
        expect(response.body.balance).to.be.equal(config.defaultSettings.amount);
    }

    @test
    public async verifyTokenSuccessSEJurisdiction() {
        const playerSE = this.addPlayerToMerchant("chebureckSE", RELAX_JURISDICTION.SE);
        const ticket = createTicket(this.merchantCode, playerSE.cust_id);
        const verifyTokenRequest: VerifyTokenRequest = {
            channel: "web",
            clientid: "test",
            token: ticket.id,
            gameref: "rlx.sw.sw.sw_mrmnky",
            partnerid: this.merchantCode,
            ip: "***********"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/verifytoken`)
            .auth(config.auth.username, config.auth.password)
            .send(verifyTokenRequest)
            .expect(200);

        expect(response.body.jurisdiction).to.be.equal(RELAX_JURISDICTION.SE);
        expect(response.body.playerid).to.be.exist;
        expect(response.body.balance).to.be.equal(config.defaultSettings.amount);
    }

    @test
    public async verifyTokenWithoutRequiredFields() {
        const verifyTokenWithoutRequiredFields = {
            channel: "web",
            clientid: "test",
            gameref: "rlx.sw.sw.sw_mrmnky",
            partnerid: this.merchantCode,
            ip: "***********"
        };

        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/verifytoken`)
            .auth(config.auth.username, config.auth.password)
            .send(verifyTokenWithoutRequiredFields)
            .expect(400);

        expect(response.body.errorcode).to.be.equal("INVALID_PARAMETERS");
    }

    @test
    public async verifyTokenMerchantNotFound() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/11/verifytoken`)
            .auth(config.auth.username, config.auth.password)
            .send({})
            .expect(500);

        expect(response.body.errorcode).to.be.equal("UNHANDLED");
    }

    @test
    public async verifyTokenAuthFail() {
        const response = await request(this.server)
            .post(`/p2p/${INTEGRATION_VERSION.RELAX}/${this.merchantCode}/verifytoken`)
            .auth("invalid", "invalid")
            .send({})
            .expect(401);

        expect(response.body.errorcode).to.be.equal("UNAUTHORIZED");
    }
}
