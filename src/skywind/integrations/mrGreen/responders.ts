import {
    CancelWagerResponse,
    GetAccountResponse,
    GetBalanceResponse,
    ResultResponse,
    WagerResponse
} from "./entities";
import { Request } from "express";
import { getDataFromTemplateTicket } from "../../service/ticket";

export const getAccountResponder = (req: Request): GetAccountResponse => {
    const token = req.query.sessionid as string || req.query.session as string;
    if (token.split("__").length === 4 || token.split("__").length === 5) {
        // if it's right format
        const [custId, currency, jurisdiction, country] = getDataFromTemplateTicket(token);

        return {
            accountId: custId,
            sessionId: token,
            currency: currency,
            country: country || "Belarus",
            details: {
                sex: "M",
                city: "Goteborg",
                firstName: "Peter",
                lastName: "Forsberg"
            }
        };
    }

    return {
        accountId: token,
        sessionId: token,
        currency: "SEK",
        country: "Belarus",
        details: {
            sex: "M",
            city: "Goteborg",
            firstName: "<PERSON>",
            lastName: "Forsberg"
        }
    };
};

export const getBalanceResponder = (req: Request): GetBalanceResponse => {
    return {
        sessionId: req.params.sessionid,
        balance: 10500.00,
        details: {
            realBalance: 10500.00,
            bonusBalance: 0.00,
        }
    };
};

export const wagerResponder = (req: Request): WagerResponse => {
    return {
        sessionId: req.params.sessionid,
        balance: 10500.00,
        walletTransactionId: 1123124,
        alreadyProcessed: false,
        details: {
            realBalance: 10500.00,
            bonusBalance: 0.00,
            baseCurrency: "SEK",
            baseCurrencyRate: 0.05,
            bonusMoneyBet: 0.00,
            realMoneyBet: 10.00,
        }
    };
};

export const resultResponder = (req: Request): ResultResponse => {
    return {
        sessionId: req.params.sessionid,
        balance: 10500.00,
        walletTransactionId: 1123124,
        alreadyProcessed: false,
        details: {
            realBalance: 10500.00,
            bonusBalance: 0.00,
            baseCurrency: "SEK",
            baseCurrencyRate: 0.05
        }
    };
};

export const cancelWagerResponder = (req: Request): CancelWagerResponse => {
    return {
        sessionId: req.params.sessionid,
        balance: 10500.00,
        alreadyProcessed: false,
        walletTransactionId: 213123123,
        transactionId: 123123
    };
};
