<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Sample Site</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
          integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">

    <style>
        body {
            padding-top: 50px;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="jumbotron">
        <h1>Mock login page</h1>
        <form class="form-inline" id="loginForm">
            <label class="sr-only" for="inlineFormInputName2">Login</label>
            <input type="text" class="form-control mb-2 mr-sm-2" id="inlineFormInputName2" name="login"
                   placeholder="Login">

            <label class="sr-only" for="inlineFormInputGroupUsername2">Entity</label>
            <div class="input-group mb-2 mr-sm-2">
                <input type="text" class="form-control" id="inlineFormInputGroupUsername2" name="entity"
                       placeholder="Entity">
            </div>


        </form>
        <button id="submitButton" class="btn btn-primary mb-2" onclick="submitData();">Submit</button>
    </div>
</div>
<script>
    function submitData() {
        const {loginForm} = document.forms;
        const fd = new FormData(loginForm);
        const login = fd.get('login');
        const entity = fd.get('entity');

        fetch(`/v1/merchant/${entity}/customer/${login}/ticket`, {
            'headers': {
                'accept': 'text/plain',
            },
            'body': null,
            'method': 'GET',
            'mode': 'cors'
        })
            .then(data => data.text())
            .then(ticket => {
                window.top.postMessage({
                    type: 'tokenInfo',
                    ticket
                }, '*');
                console.log("ticket: ", ticket)
            })
            .catch((err) => {
                console.error(err);
            })
    }
</script>
</body>
</html>
