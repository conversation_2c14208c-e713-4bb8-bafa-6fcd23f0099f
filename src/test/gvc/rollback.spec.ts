import { suite, test } from "mocha-typescript";
import { expect } from "chai";

const request = require("supertest");
import {
    FAKE_GAME_ROUND_ID, GVCVoidTransactionResponse,
    SKYWIND_PARTNER_ID
} from "../../skywind/entities/gvc";

import * as TransactionModel from "../../skywind/models/transaction";
import { GVCBaseSpec } from "./baseSpec";
import { createTicket } from "../../skywind/service/ticket";
import { WALLET_ACTION } from "../../skywind/models/transaction";

@suite("[GVC.rollback]")
class GVCBalanceSpec extends GVCBaseSpec {

    @test("should cancel bet")
    public async testCancelBet() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });
        const updatedRollbackRequest = Object.assign(this.rollbackRequest, {});

        const expectedResponse: GVCVoidTransactionResponse = {
            accountId: `${this.merchantCode}__${this.playerCode}`,
            partnerId: SKYWIND_PARTNER_ID,
            currencyCode: "USD",
            balance: 10000, // minor units
            bpGameRoundId: FAKE_GAME_ROUND_ID,
            transactionId: "PQ+OKOAAChlsAKALXPQ+OA=",
            status: {
                statusCode: "0",
                statusMessage: "success",
            }
        };

        await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .delete(this.urlPathToRollback)
            .send(updatedRollbackRequest)
            .expect(200);

        delete actualResponse.body.bpTransactionId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should return the same balance when rollback has already been processed")
    public async testNonProcessedRollback() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });
        const updatedRollbackRequest = Object.assign(this.rollbackRequest, {});

        const expectedResponse: GVCVoidTransactionResponse = {
            accountId: `${this.merchantCode}__${this.playerCode}`,
            partnerId: SKYWIND_PARTNER_ID,
            currencyCode: "USD",
            balance: 10000, // minor units
            bpGameRoundId: FAKE_GAME_ROUND_ID,
            transactionId: "PQ+OKOAAChlsAKALXPQ+OA=",
            status: {
                statusCode: "0",
                statusMessage: "success",
            }
        };

        await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);

        await request(this.server)
            .delete(this.urlPathToRollback)
            .send(updatedRollbackRequest)
            .expect(200);

        const actualResponse = await request(this.server)
            .delete(this.urlPathToRollback)
            .send(updatedRollbackRequest)
            .expect(200);

        delete actualResponse.body.bpTransactionId;
        expect(actualResponse.body).deep.equal(expectedResponse);
    }

    @test("should throw TransactionToCancelNotFoundError")
    public async testTransactionToCancelNotFound() {
        const updatedRollbackRequest = Object.assign(this.rollbackRequest, {});

        const actualResponse = await request(this.server)
            .delete(this.urlPathToRollback)
            .send(updatedRollbackRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(125);
    }

    @test("should throw TransactionAlreadyCancelledError")
    public async testTransactionAlreadyCancelled() {
        const ticket = createTicket(this.merchantCode, this.playerCode);
        const updatedDebitRequest = Object.assign(this.debitRequest, { secureToken: ticket.id });
        const updatedRollbackRequest = Object.assign(this.rollbackRequest, {});

        await request(this.server)
            .put(this.urlPathToMakePayment)
            .send(updatedDebitRequest)
            .expect(200);

        TransactionModel.rollbackById("PQ+OKOAAChlsAKALXPQ+OA=_bet", WALLET_ACTION.debit);

        const actualResponse = await request(this.server)
            .delete(this.urlPathToRollback)
            .send(updatedRollbackRequest)
            .expect(200);

        expect(actualResponse.body.errorCode).to.be.equal(127);
    }
}
