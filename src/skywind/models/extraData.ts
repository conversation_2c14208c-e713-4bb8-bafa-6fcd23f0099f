import { InvalidRequest } from "../errors";
import { RaiseType } from "./customError";

interface CustomerExtraData {
    [merchantId: string]: {
        [customerId: string]: any
    };
}

let extraData = {};

export const getAll = () => extraData;

export const setAll = (newExtraData) => extraData = newExtraData;

export const getMerchantData = (merchantId: string) => extraData[merchantId];

export const getCustomerData = (merchantId: string, customerId: string) => {
    return extraData?.[merchantId]?.[customerId];
};

export const getActionData = (merchantId: string, customerId: string, action: string) => {
    return extraData?.[merchantId]?.[customerId]?.[action];
};

export const deleteActionData = (merchantId: string, customerId: string, action: string) => {
    if (extraData?.[merchantId]?.[customerId]?.[action] === undefined) {
        throw new InvalidRequest("extra data is missing");
    }
    delete extraData[merchantId][customerId][action];
};

export const createActionData = (merchantId: string, customerId: string,
                                 action: string, data: any) => {
    extraData[merchantId] = extraData[merchantId] || {};
    extraData[merchantId][customerId] = extraData[merchantId][customerId] || {};
    extraData[merchantId][customerId][action] = extraData[merchantId][customerId][action] || {};
    extraData[merchantId][customerId][action] = data;

    return data;
};
