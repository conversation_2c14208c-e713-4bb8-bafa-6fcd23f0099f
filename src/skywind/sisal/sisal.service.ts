import {
    SisalBalanceResponse,
    SisalCancelWagerRequest,
    SisalEndWagerRequest,
    SisalWagerRequest,
    SisalWagerResponse
} from "./sisal.entities";
import * as SisalErrors from "./sisal.errors";

import {Customer, Merchant} from "../models/merchant";
import {throwCustomError} from "../service/customError";
import {extraDataHandler} from "../service/extraData";
import * as TransactionModel from "../models/transaction";
import {Transaction, WALLET_ACTION} from "../models/transaction";
import {randomNumber, sumMajorUnits, toMajorUnits, toMinorUnits} from "../utils/helper";
import {PAYMENT_TYPE} from "../entities/common";

export interface SisalService {
    balance(): Promise<SisalBalanceResponse>;

    sessionBalance(): Promise<SisalBalanceResponse>;

    wager(data: SisalWagerRequest): Promise<SisalWagerResponse>;

    endWager(data: SisalEndWagerRequest): Promise<SisalWagerResponse>;

    cancelWager(data: SisalCancelWagerRequest): Promise<SisalWagerResponse>;
}

export class SisalServiceImpl implements SisalService {
    constructor(public merchant: Merchant, public customer?: Customer) {
    }

    @throwCustomError({action: "sessionbalance"})
    @extraDataHandler()
    public async sessionBalance(): Promise<SisalBalanceResponse> {
        return this.getBalance();
    }

    @throwCustomError({action: "balance"})
    @extraDataHandler()
    public async balance(): Promise<SisalBalanceResponse> {
        return this.getBalance();
    }

    @throwCustomError({action: "wager"})
    @extraDataHandler()
    public async wager(data: SisalWagerRequest): Promise<SisalWagerResponse> {
        // betAmount - used in real game session
        // betAmountFromPlayBonus - used in bonus game session
        // Both parameters should change only balance value if bonusBalance is empty
        const {txId, betAmount, betAmountFromPlayBonus} = data;

        // Defines how much should be decreased in bonus and real balance
        // if bet > bonusBalance: bonusBet = bonusBalance, realBet = bet - bonusBalance
        // otherwise: bonusBet = bet, realBet = 0
        const {bet, bonusBet} = this.getBet(betAmount || betAmountFromPlayBonus);

        this.checkPlayerBalance(toMinorUnits(this.customer.balance.amount), Math.abs(bet) * WALLET_ACTION.debit);
        // The bonus balance is changed first and not depends on how much amount in there
        // The rest bet is used in the real balance
        if (bonusBet) {
            // Bonus balance transfer is marked as bonus transaction
            this.customer.bonusBalance =
                this.processWager(this.getBonusWagerTransactionId(txId), bonusBet, this.customer.bonusBalance);
        }
        if (bet) {
            this.customer.balance.amount = this.processWager(txId, bet, this.customer.balance.amount);
        }

        return {
            gamePhaseId: data.gamePhaseId, // sisal round id
            inPhaseSeqNumber: randomNumber(2),
            playerGamePhaseId: randomNumber(),
            playerGamePhaseWagerId: randomNumber(),
            sessionBalance: this.getBalance()
        } as SisalWagerResponse;
    }

    private processWager(txId: string, bet: number, balance: number): number {
        const transaction = TransactionModel.getById(txId, WALLET_ACTION.debit);

        if (transaction) {
            return balance;
        }
        TransactionModel.setById(txId, WALLET_ACTION.debit, bet, this.customer.cust_id);
        return sumMajorUnits(balance, toMajorUnits(bet * WALLET_ACTION.debit));
    }

    @throwCustomError({action: "endwager"})
    @extraDataHandler()
    public async endWager(data: SisalEndWagerRequest): Promise<SisalWagerResponse> {
        const {txId, totalWinAmount, playBonusWinAmount} = data;
        let win = totalWinAmount || playBonusWinAmount || 0;
        win += data.jackpotWinAmount || data.jackpots &&
            data.jackpots.reduce((result, jp) => result + jp.amount, 0) || 0;

        // Win goes to balance only, bonusBalance remains the same
        this.customer.balance.amount = this.processEndWager(txId, win, this.customer.balance.amount);

        return {
            gamePhaseId: data.gamePhaseId, // sisal round id
            inPhaseSeqNumber: randomNumber(2),
            playerGamePhaseId: randomNumber(),
            playerGamePhaseWagerId: randomNumber(),
            sessionBalance: this.getBalance(),
            ...this.getExtraData()
        } as SisalWagerResponse;
    }

    private processEndWager(txId: string, win: number, balance: number): number {
        const transaction = TransactionModel.getById(txId, WALLET_ACTION.credit);

        // TODO: remove if JP win request is processed correctly.
        // The code commented because the mock was returning a balance with no JP amount,
        // although previous win amounts were zero and aren't supposed to be sent to mock by wallet.
        // Actually it was happening somehow.
        // Note: theoretically might be issues with win retries.
        /*if (transaction) {
            return balance;
        }*/

        TransactionModel.setById(txId, WALLET_ACTION.credit, win, this.customer.cust_id);
        return sumMajorUnits(balance, toMajorUnits(win * WALLET_ACTION.credit));
    }

    @throwCustomError({action: "cancelwager"})
    @extraDataHandler()
    public async cancelWager(data: SisalCancelWagerRequest): Promise<SisalWagerResponse> {
        const {txId} = data;
        // No possible to know which balance is changed on wager
        // Take both possible transactions to check for existence
        // Anyway one/two of them should be presented and executed
        const wagerTxId = this.getWagerTransactionId(txId, PAYMENT_TYPE.BET);
        const bonusWagerTxId = this.getBonusWagerTransactionId(wagerTxId);

        if (!TransactionModel.getById(txId, WALLET_ACTION.rollback)) {
            const debitTransaction = TransactionModel.getById(wagerTxId, WALLET_ACTION.debit);
            const bonusTransaction = TransactionModel.getById(bonusWagerTxId, WALLET_ACTION.debit);

            if (!debitTransaction && !bonusTransaction) {
                throw new SisalErrors.InvalidCancelWagerError();
            }

            if (debitTransaction) {
                this.customer.balance.amount =
                    this.processCancelWager(txId,
                        wagerTxId,
                        debitTransaction,
                        WALLET_ACTION.debit,
                        this.customer.balance.amount);

            }
            if (bonusTransaction) {
                this.customer.bonusBalance =
                    this.processCancelWager(txId,
                        bonusWagerTxId,
                        bonusTransaction,
                        WALLET_ACTION.debit,
                        this.customer.bonusBalance);
            }
        }

        return {
            gamePhaseId: data.gamePhaseId, // sisal round id
            inPhaseSeqNumber: randomNumber(2),
            playerGamePhaseId: randomNumber(),
            playerGamePhaseWagerId: randomNumber(),
            sessionBalance: this.getBalance(),
            ...this.getExtraData()
        } as SisalWagerResponse;
    }

    private processCancelWager(txId: string,
                               wagerTxId: string,
                               transaction: Transaction,
                               action: WALLET_ACTION,
                               balance: number): number {
        const [_, amount, __, isRollback] = transaction;
        if (isRollback) {
            throw new SisalErrors.InvalidCancelWagerError(/*debitTrxId*/);
        }

        TransactionModel.rollbackById(wagerTxId, action);
        TransactionModel.setById(txId, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        return toMajorUnits(toMinorUnits(balance) - amount);
    }

    private checkPlayerBalance(playerBalance: number, amount: number) {
        if (playerBalance + amount < 0) {
            throw new SisalErrors.InvalidBetAmountError();
        }
    }

    private getWagerTransactionId(txId: string, type: PAYMENT_TYPE): string {
        return txId.split("_")[0] + "_" + type;
    }

    private getBonusWagerTransactionId(txId: string): string {
        return txId += "_bonus";
    }

    private getBet(betAmount: number): { bet: number, bonusBet: number } {
        if (betAmount > toMinorUnits(this.customer.bonusBalance)) {
            return {
                bet: betAmount - toMinorUnits(this.customer.bonusBalance),
                bonusBet: toMinorUnits(this.customer.bonusBalance)
            };
        }
        return {
            bet: 0,
            bonusBet: betAmount
        };
    }

    private getExtraData(): Partial<SisalWagerResponse> {
        if (!this.customer.extraData) {
            return {};
        }
        return {
            messageBodyHtml: this.customer.extraData.messageBodyHtml,
            messageBody: this.customer.extraData.messageBody,
            messageTitle: this.customer.extraData.messageTitle,
        };
    }

    private getBalance(): SisalBalanceResponse {
        return {
            playerId: this.customer.cust_id,
            bonusBalance: toMinorUnits(this.customer.bonusBalance) || 0,
            balance: toMinorUnits(this.customer.balance.amount)
        } as SisalBalanceResponse;
    }
}

export function getSisalService(merchant: Merchant, customer?: Customer): SisalServiceImpl {
    return new SisalServiceImpl(merchant, customer);
}
