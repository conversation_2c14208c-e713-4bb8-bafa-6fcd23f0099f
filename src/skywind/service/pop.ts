import { Customer, Merchant } from "../models/merchant";
import { generateTicketForUser, getPOPTicket as getTicket, isSpecialTicket } from "./ticket";
import {
    validateGameCycleId,
    validateGameType,
    validateJackpot,
    validateMoneyTransactions,
    validateTransaction
} from "../api/config/popValidator";
import { sumMajorUnits } from "../utils/helper";
import config from "../config";
import * as Errors from "../popErrors";
import { TransactionToCancelNotFound } from "../popErrors";
import {
    CancelTransactionsRequest,
    CancelTransactionsResponse,
    MoneyAck,
    MoneyTransaction,
    MoneyTransactionDetails,
    MoneyTransactionRequest,
    MoneyTransactionResponse,
    PlayerBalanceRequest,
    PlayerBalanceResponse,
    PlayerInfoRequest,
    PlayerInfoResponse,
    VerifyPlayerSessionRequest,
    VerifyPlayerSessionResponse
} from "../scheme/pop/protocol";

import { throwCustomError } from "./customError";
import { createCustomerSession } from "./session";
import { settings } from "../models/settings";
import * as TransactionModel from "../models/transaction";
import { getById, WALLET_ACTION } from "../models/transaction";
import { extraDataHandler } from "./extraData";
import * as RoundModel from "../models/round";
import { Ticket } from "../models/ticket";
import { createBrokenGame } from "./brokenGames";

const uuid = require("uuid");

export class PopService {
    constructor(public readonly merchant: Merchant, public customer?: Customer) {
    }

    public getName(): string {
        return "pop";
    }

    private getTicketOrCreateTicket(merchant: Merchant, ticketId: string): Ticket {
        if (isSpecialTicket(ticketId)) {
            return generateTicketForUser(merchant, uuid.v4(), "CNY", false, false, 1000000);
        } else {
            return getTicket(ticketId, config.expirationTime.ticket);
        }
    }

    @throwCustomError({ action: "verifyplayersession" })
    @extraDataHandler({ action: "verifyplayersession" })
    public async verifyPlayerSession(request: VerifyPlayerSessionRequest): Promise<VerifyPlayerSessionResponse> {
        const ticket = this.getTicketOrCreateTicket(this.merchant, request.secureToken);

        const customer: Customer = this.merchant.customers[ticket.custId];
        if (!customer) {
            throw new Errors.EntityNotFound("customer", ticket.custId);
        }
        const sessionId: string = createCustomerSession(this.merchant, customer);
        this.setCustomer(customer);

        const response: VerifyPlayerSessionResponse = {
            secureToken: sessionId,
            accountBalance: {
                playerId: ticket.custId,
                accountId: "",
                currencyCode: customer.currency_code,
                balanceArray: [
                    {
                        balanceType: "cashable",
                        balanceAmt: customer.balance.amount
                    }
                ]
            }
        };
        if (customer.bet_limit) {
            response.sessionData = { maxAllowedBetAmt: +customer.bet_limit };
        }
        return response;
    }

    private setCustomer(customer: Customer) {
        this.customer = customer;
    }

    @throwCustomError({ action: "getplayerinfo" })
    @extraDataHandler({ action: "getplayerinfo" })
    public async getPlayerInfo(request: PlayerInfoRequest): Promise<PlayerInfoResponse> {
        return {
            playerId: this.customer.cust_id,
            currencyCode: this.customer.currency_code,
            countryCode: this.customer.country
        };
    }

    public async moneyTransactions(request: MoneyTransactionRequest): Promise<MoneyTransactionResponse> {
        const moneyTrans = request.moneyTransArray;
        const playerId = request.playerId;
        const gameCycleId = request.gameCycleId;
        validateMoneyTransactions(moneyTrans);
        validateJackpot(request);
        validateGameCycleId(request.gameCycleId, !moneyTrans.length);
        const isBet = moneyTrans.length > 0 && moneyTrans[0].transType === "debit";
        return isBet ?
               this.commitBet(moneyTrans, gameCycleId, playerId) :
               this.commitWin(moneyTrans, gameCycleId, playerId);
    }

    @throwCustomError({ action: "bet" })
    @extraDataHandler({ action: "bet" })
    private async commitBet(moneyTrans: MoneyTransaction[], gameCycleId: string, playerId: string): Promise<MoneyTransactionResponse> {
        const moneyAck = this.commitMoneyTransactions(moneyTrans, gameCycleId);

        return {
            gameCycleId,
            playerId,
            accountBalance: {
                playerId,
                accountId: "",
                currencyCode: this.customer.currency_code,
                balanceArray: [
                    {
                        balanceType: "cashable",
                        balanceAmt: this.customer.balance.amount
                    },
                    {
                        balanceType: "freespin",
                        balanceAmt: (this.customer.freeBets && this.customer.freeBets.count) || 0
                    }
                ]
            },
            errorCode: "",
            errorMsg: "",
            logout: false,
            moneyAckArray: moneyAck
        };
    }

    @throwCustomError({ action: "win" })
    @extraDataHandler({ action: "win" })
    private async commitWin(moneyTrans: MoneyTransaction[], gameCycleId: string, playerId: string): Promise<MoneyTransactionResponse> {
        const moneyAck = this.commitMoneyTransactions(moneyTrans, gameCycleId);

        return {
            gameCycleId,
            playerId,
            accountBalance: {
                playerId,
                accountId: "",
                currencyCode: this.customer.currency_code,
                balanceArray: [
                    {
                        balanceType: "cashable",
                        balanceAmt: this.customer.balance.amount
                    },
                    {
                        balanceType: "freespin",
                        balanceAmt: (this.customer.freeBets && this.customer.freeBets.count) || 0
                    }
                ]
            },
            errorCode: "",
            errorMsg: "",
            logout: false,
            moneyAckArray: moneyAck
        };
    }

    @throwCustomError({ action: "getplayerbalance" })
    @extraDataHandler({ action: "getplayerbalance" })
    public async getPlayerBalance(request: PlayerBalanceRequest): Promise<PlayerBalanceResponse> {
        return {
            accountBalance: {
                playerId: this.customer.cust_id,
                accountId: "",
                currencyCode: this.customer.currency_code,
                balanceArray: [
                    {
                        balanceType: "cashable",
                        balanceAmt: this.customer.balance.amount
                    },
                    {
                        balanceType: "freespin",
                        balanceAmt: (this.customer.freeBets && this.customer.freeBets.count) || 0
                    }
                ]
            }
        };
    }

    @throwCustomError({ action: "canceltransactions" })
    @extraDataHandler({ action: "canceltransactions" })
    public async cancelTransactions(request: CancelTransactionsRequest): Promise<CancelTransactionsResponse> {
        const gameCycleId = request.gameCycleId;
        const playerId = request.playerId;

        validateGameCycleId(gameCycleId);

        for (const { transId: rollbackTrxId, referenceId: debitTrxId, transCategory } of request.cancelTransArray) {
            const debitTransaction = TransactionModel.getById(debitTrxId, WALLET_ACTION.debit);
            if (!debitTransaction) {
                throw new TransactionToCancelNotFound();
            }

            if (!getById(rollbackTrxId, WALLET_ACTION.rollback)) {
                const [_, amount, __, isRollback] = debitTransaction;
                if (!isRollback) {
                    TransactionModel.rollbackById(debitTrxId, WALLET_ACTION.debit);
                    if (transCategory === "freeSpinCancel") {
                        this.customer.freeBets.count += 1;
                    } else {
                        this.customer.balance.amount = sumMajorUnits(this.customer.balance.amount, -amount);
                    }
                }

                TransactionModel.setById(rollbackTrxId, WALLET_ACTION.rollback, 0, playerId);
            }
        }

        return {
            accountBalance: {
                playerId,
                accountId: "",
                currencyCode: this.customer.currency_code,
                balanceArray: [
                    {
                        balanceType: "cashable",
                        balanceAmt: this.customer.balance.amount
                    },
                    {
                        balanceType: "freespin",
                        balanceAmt: (this.customer.freeBets && this.customer.freeBets.count) || 0
                    }
                ]
            },
            errorCode: "",
            errorMsg: "",
        };
    }

    @throwCustomError({ action: "createbrokengame" })
    @extraDataHandler({ action: "createbrokengame" })
    public async createBrokenGame(roundId: string): Promise<void> {
        createBrokenGame(roundId);
    }

    private commitMoneyTransactions(transactions: MoneyTransaction[],
                                    gameCycleId: string): MoneyAck[] {
        if (transactions.length === 0) {
            return [];
        }

        const result: MoneyAck[] = [];

        for (const trx of transactions) {
            const { transId: trxId, transAmt: amount, transType: type, transDesc, transCategory } = trx;
            const action = WALLET_ACTION[type];

            if (transCategory === "freeSpinBet") {
                validateTransaction(type, trxId, this.customer.freeBets.count, -1);
                this.customer.freeBets.count -= 1;
            } else {
                validateTransaction(type, trxId, this.customer.balance.amount, amount * action);
                this.customer.balance.amount = sumMajorUnits(this.customer.balance.amount, amount * action);
            }

            // Do not save transactions to decrease the load
            if (!settings.decreaseLoad) {
                validateGameType(this.merchant.merch_id, this.customer.cust_id, gameCycleId, transDesc);

                RoundModel.createOrUpdate({
                    merchantId: this.merchant.merch_id,
                    customerId: this.customer.cust_id,
                    gameId: gameCycleId
                }, WALLET_ACTION[type], amount);
                TransactionModel.setById(trxId, action, +amount, this.customer.cust_id);
            }

            const details: MoneyTransactionDetails = {
                balanceType: "cashable",
                detailAmt: amount * action,
                detailType: type,
                balanceAmt: this.customer.balance.amount
            };

            const ackTrx: MoneyAck = {
                ...trx,
                moneyDetailArray: [details],
                errorCode: "",
                errorMsg: ""
            };

            result.push(ackTrx);
        }

        return result;

    }
}

export class MoorgateService extends PopService {

    public getName(): string {
        return "moorgate";
    }
}
