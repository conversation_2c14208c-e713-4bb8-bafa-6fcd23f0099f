import { authenticate as authBase, authenticateCustomer as authCustomerBase } from "./middleware";
import { SessionExpired } from "../../popErrors";
import { NextFunction, Request, Response } from "express";
import { CustomerHolder, MerchantHolder } from "../../entities/common";

export const authenticate = authBase((req) => req?.body?.skinId);

export const authenticateCustomer = authCustomerBase(
    (req) => ({
        code: req.body.playerId,
        sessionId: req.body.secureToken
    }), new SessionExpired(), shouldThrowSessionValidationError);

export const authenticateCustomerIgnoringValidation = authCustomerBase(
    (req) => ({
        code: req.body.playerId,
        sessionId: req.body.secureToken
    }), undefined, () => false);

function shouldThrowSessionValidationError(request): boolean {
    return !isWinOnRoundEnd(request);
}

function isWinOnRoundEnd(request): boolean {
    return request.body && request.body.moneyTransArray && request.body.gameCycleFinished === true;
}

export const authCustomerForVerifySession = (req: Request & MerchantHolder & CustomerHolder,
                                             res: Response,
                                             next: NextFunction) => {
    req.customer = req.merchant.customers[req.body.playerId];
    return next();
};
