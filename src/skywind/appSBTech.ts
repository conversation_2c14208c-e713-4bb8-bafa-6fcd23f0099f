// this should be the first line
import { measures, logging } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import config from "./config";

logging.setUpOutput({ type: config.loggingOutput, logLevel: config.logLevel });
logging.setRootLogger("ipm-mock");

import startServer from "./serverSBTech";
import { startServer as startInternalServer } from "./serverInternal";
import { INTEGRATION_TYPE } from "../config/integrationType";

(async () => {
    await startServer();
    await startInternalServer(INTEGRATION_TYPE.SBTECH);
})();
