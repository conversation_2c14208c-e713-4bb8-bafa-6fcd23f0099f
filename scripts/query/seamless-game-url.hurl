# Comprehensive Seamless Wallet + IMP-Mock Game Flow Test
# This script demonstrates a complete game flow from user registration to gameplay
# using seamless wallet integration and mock services
#
# Fixed: Creates seamless merchant type, game provider, game, and merchant entity in MAPI

# Environment Variables Setup
# MAPI_URL: Management API URL (default: http://localhost:3000)
# MOCK_URL: IMP Mock Service URL (default: http://localhost:8000)
# TERMINAL_URL: Terminal Service URL (default: http://localhost:3004)
# WALLET_URL: Wallet Service URL (default: http://localhost:3005)
# MAPI_PASSWORD: Management API password
# GAME_CODE: Game to test (default: sw_mrmnky)

## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 3: Setup IMP-Mock Merchant
POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "seamless_test_merchant",
  "merch_pwd": "SecurePassword123!",
  "isPromoInternal": false,
  "multiple_session": true,
  "serverUrl": "{{MOCK_URL}}",
  "type": "seamless"
}
HTTP 201
[Asserts]
jsonpath "$.merch_id" == "seamless_test_merchant"

## Step 4: Create Test Customer in IMP-Mock
POST {{MOCK_URL}}/v1/merchant/seamless_test_merchant/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "test_player_001",
  "cust_login": "TESTPLAYER001",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "bet_limit": 1000,
  "first_name": "Test",
  "last_name": "Player",
  "email": "<EMAIL>",
  "jurisdiction": "COM",
  "balance": {
    "amount": 100000,
    "currency_code": "USD"
  }
}
HTTP 201
[Asserts]
jsonpath "$.cust_id" == "test_player_001"
jsonpath "$.cust_login" == "TESTPLAYER001"
jsonpath "$.balance.amount" == 0

## Step 5: Generate Customer Ticket
GET {{MOCK_URL}}/v1/merchant/seamless_test_merchant/customer/test_player_001/ticket
accept: text/plain
HTTP 200
[Captures]
playerTicket: body

## Step 5.1: Update/Create Seamless Merchant Type
POST {{MAPI_URL}}/v1/merchant-types
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "type": "seamless",
  "url": "{{WALLET_URL}}",
  "schema": {
    "serverUrl": {
      "type": "text",
      "title": "Server URL",
      "defaultValue": "http://localhost:8000"
    },
    "password": {
      "type": "password",
      "title": "Password",
      "defaultValue": ""
    }
  }
}
HTTP *
[Asserts]
status >= 200
status < 500

## Step 5.1b: Update Seamless Merchant Type URL (ensure correct URL)
PATCH {{MAPI_URL}}/v1/merchant-types/seamless
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "url": "{{WALLET_URL}}"
}
HTTP 200
[Asserts]
jsonpath "$.type" == "seamless"
jsonpath "$.url" == "{{WALLET_URL}}"

## Step 5.2: Verify Seamless Merchant Type is properly configured
GET {{MAPI_URL}}/v1/merchant-types/seamless
accept: application/json
x-access-token: {{accessToken}}
HTTP 200
[Asserts]
jsonpath "$.type" == "seamless"
jsonpath "$.url" == "{{WALLET_URL}}"

## Step 5.3: Create Game Provider (allow existing)
POST {{MAPI_URL}}/v1/gameproviders
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "user": "skywind_test",
  "code": "sw_test",
  "title": "Skywind Test Provider",
  "secret": "test_secret_123",
  "isTest": true
}
HTTP *
[Asserts]
status >= 200
status < 500

## Step 5.3b: Get Game Provider ID (in case it already existed)
GET {{MAPI_URL}}/v1/gameproviders
accept: application/json
x-access-token: {{accessToken}}
HTTP 200
[Captures]
providerId: jsonpath "$[?(@.code=='sw_test')].id" nth 0

## Step 5.4: Create Game sw_mrmnky (allow existing)
POST {{MAPI_URL}}/v1/game/slot
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "type": "slot",
  "title": "Monkey Slot",
  "url": "http://gameserver.test.com/games/sw_mrmnky?token={startGameToken}",
  "gameCode": "sw_mrmnky",
  "providerGameCode": "sw_mrmnky",
  "defaultInfo": {
    "name": "Monkey Slot",
    "description": "A fun monkey-themed slot game"
  },
  "info": {
    "EN": {
      "name": "Monkey Slot",
      "description": "A fun monkey-themed slot game"
    }
  },
  "limits": {
    "USD": {
      "coinsRate": 0.01
    }
  },
  "providerId": "{{providerId}}"
}
HTTP *
[Asserts]
status >= 200
status < 500

## Step 5.5: Create Merchant Entity in MAPI (allow existing)
POST {{MAPI_URL}}/v1/merchantentities
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "name": "SeamlessTestMerchant",
  "code": "seamless_test_merchant",
  "type": "seamless",
  "defaultCountry": "US",
  "defaultCurrency": "USD",
  "defaultLanguage": "en",
  "jurisdictionCode": "COM",
  "webSiteUrl": "http://localhost:3000",
  "params": {
    "serverUrl": "{{MOCK_URL}}",
    "password": "SecurePassword123!"
  }
}
HTTP *
[Asserts]
status >= 200
status < 500

## Step 5.5b: Get Merchant Entity Info (in case it already existed)
GET {{MAPI_URL}}/v1/merchantentities
accept: application/json
x-access-token: {{accessToken}}
HTTP 200
[Captures]
merchantPath: jsonpath "$.entity.path"

## Step 5.6: Add Game to Merchant Entity
POST {{MAPI_URL}}/v1/entities/{{merchantPath}}/games/sw_mrmnky
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "status": "normal",
  "settings": {}
}
HTTP 201
[Asserts]
jsonpath "$.code" == "sw_mrmnky"
jsonpath "$.status" == "normal"

## Step 6: Get Game URL for Seamless Integration
POST {{MAPI_URL}}/v1/merchants/game/url
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "merchantType": "seamless",
  "merchantCode": "seamless_test_merchant",
  "gameCode": "{{GAME_CODE}}",
  "playmode": "real",
  "language": "en",
  "ip": "127.0.0.1",
  "lobby": "http://localhost:3000/lobby",
  "cashier": "http://localhost:3000/cashier",
  "ticket": "{{playerTicket}}",
  "currency": "USD"
}
HTTP 200
[Captures]
gameUrl: jsonpath "$.url"
gameToken: jsonpath "$.token"
[Asserts]
jsonpath "$.url" exists
jsonpath "$.token" exists
jsonpath "$.currency" == "USD"
