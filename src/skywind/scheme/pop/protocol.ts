export type TransactionType = "credit" | "debit";
export type BalanceType = "cashable" | "freespin";
export type ChannelType = "web";
export type TransactionCategory = "win" | "freeSpinBet" | "freeSpinWin" | "freeSpinCancel";

export interface MoneyTransactionRequest {
    _meta?: Meta;
    secureToken: string;
    playerId: string;
    accountId?: string;
    localeCode: string;
    gameId: string;
    gameCycleId: string;
    skinId: string;
    currencyCode: string;
    moneyTransArray: MoneyTransaction[];
    gameCycleStarted: boolean;
    gameCycleFinished: boolean;
    gameCycleStartDateTime?: string;
    jackpot?: Jackpot;
}

export interface MoneyTransactionResponse {
    gameCycleId: string;
    playerId: string;
    accountBalance: AccountBalance;
    errorCode?: string;
    errorMsg?: string;
    logout: boolean;
    moneyAckArray: MoneyAck[];
    [field: string]: any;
}

export interface VerifyPlayerSessionRequest {
    _meta?: Meta;
    localeCode: string;
    gameId: string;
    accountId: string;
    secureToken: string;
    skinId: string;
    currencyCode: string;
    playerId: string;
    channelType: ChannelType;
}

export interface VerifyPlayerSessionResponse {
    secureToken: string;
    accountBalance: AccountBalance;
    sessionData?: {
        unfinishedGameCycleId?: string;
        bonusRoundRemaining?: number;
        maxAllowedBetAmt?: number;
    };
}

export interface PlayerInfoRequest {
    _meta?: Meta;
    skinId: string;
    playerId: string;
    secureToken: string;
}

export interface PlayerInfoResponse {
    playerId: string;
    currencyCode: string;
    countryCode: string;
}

export interface PlayerBalanceRequest {
    _meta?: Meta;
    secureToken: string;
    playerId: string;
    skinId: string;
    localeCode: string;
    accountId: string;
}

export interface PlayerBalanceResponse {
    accountBalance: AccountBalance;
}

export interface CancelTransactionsRequest {
    gameId: string;
    gameCycleId: string;

    cancelTransArray?: [CancelMoneyTransaction];
    gameCycleFinished: boolean;
    gameCycleFinishDateTime?: string;

    _meta?: Meta;
    secureToken: string;
    playerId: string;
    accountId?: string;
    skinId: string;
    currencyCode: string;
    gameCycleStarted: boolean;
    gameCycleStartDateTime?: string;

}

export interface CancelTransactionsResponse extends PlayerBalanceResponse {
    errorCode: string;
    errorMsg: string;
}

export interface Meta {
    requestId: string;
    gpId: string;
    gsId: string;
    clientPlatform: string;
    clientType: string;
}

export interface MoneyTransaction {
    transSeq: number;
    transId: string;
    transAmt: number;
    transType: TransactionType;
    transDateTime: string;
    transCategory: TransactionCategory;
    transDesc: string;
}

export interface CancelMoneyTransaction {
    transSeq: number;
    transId: string;
    transDateTime: string;
    transCategory?: TransactionCategory;
    referenceId: string;
    originalTransDate: string;
}

export interface MoneyTransactionDetails {
    balanceType: BalanceType;
    detailAmt: number;
    detailType: TransactionType;
    balanceAmt: number;
}

export interface MoneyAck extends MoneyTransaction {
    moneyDetailArray?: MoneyTransactionDetails[];
    errorCode?: string;
    errorMsg?: string;
}

export interface Jackpot {
    contribAmt: number;
    awardAmt: number;
}

export interface AccountBalance {
    playerId: string;
    accountId: string;
    currencyCode: string;
    balanceArray: Balance[];
}

export interface Balance {
    balanceType: BalanceType;
    balanceAmt: number;
}
