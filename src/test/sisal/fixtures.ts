import {
    SisalWagerRequest,
    SisalEndWagerRequest,
    SisalCancelWagerRequest,
    SisalHeaders
} from "../../skywind/sisal/sisal.entities";
import { Customer, Merchant } from "../../skywind/models/merchant";
import { getDefaultCustomer } from "../../skywind/service/customer";

export class SisalTestFixtures {
    public merchantCode = "sisal";
    public playerCode = "sisal_player";
    public merchant: Merchant = {
        merch_id: this.merchantCode,
        merch_pwd: "shared_key",
        multiple_session: true,
        customers: {},
    };
    public debitRequest: SisalWagerRequest = {
        "betAmount": 1000,
        "gamePhaseId": "round1",
        "timestamp": 1588843403000,
        "txId": "trxId_bet",
    };
    public creditRequest: SisalEndWagerRequest = {
        "finalPhaseWager": false,
        "finalWager": true,
        "gamePhaseId": "round1",
        "timestamp": 1588843403000,
        "totalWinAmount": 2000,
        "txId": "trxId_win",
    };
    public rollbackRequest: SisalCancelWagerRequest = {
        "gamePhaseId": "round1",
        "timestamp": 1588843403000,
        "txId": "trxId_rollback"
    };
    public headers: SisalHeaders = {
        // tslint:disable-next-line:max-line-length
        authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE1MTYyMzkwMjJ9.tbDepxpstvGdW8TC3G8zg4B6rUYAOvfzdceoH48wgRQ", // sisalToken
        gameCode: "Some Game",
        xClientId: "X-Client-ID",
    };
}
