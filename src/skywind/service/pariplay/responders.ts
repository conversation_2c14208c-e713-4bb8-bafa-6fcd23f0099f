import { Request } from "express";
import { MerchantHolder } from "../../entities/common";
import { getDataFromTemplateTicket } from "../ticket";
import { settings } from "../../models/settings";
import * as Session from "../session";
import {
    PariplayBalanceRequest, PariplayBalanceResponse, PariplayErrorContainer, PariplayPaymentResponse,
    PariplayVerifyTokenRequest,
    PariplayVerifyTokenResponse
} from "../../entities/pariplay";

export const authResponder = (req: Request & MerchantHolder): PariplayVerifyTokenResponse => {
    const { ClientToken } = req.body as PariplayVerifyTokenRequest;
    const [custId, currency, jurisdiction, country] = getDataFromTemplateTicket(ClientToken);
    return {
        PlayerId: custId,
        Token: Session.generateCustomerSessionId(custId),
        Balance: settings.amount,
        CurrencyCode: currency,
        CountryCode: country || "GB",
        OperatorCode: req?.body?.Account?.Username,
        ErrorCode: 0
    };
};

export const balanceResponder =
    (req: Request & MerchantHolder): PariplayBalanceResponse => ({
        Balance: settings.amount,
        ErrorCode: 0
    });

export const paymentResponder =
    (req: Request & MerchantHolder): PariplayPaymentResponse => ({
        Balance: settings.amount,
        TransactionId: Date.now().toString(),
        ErrorCode: 0
});

export const notificationResponder = (req: Request & MerchantHolder): PariplayErrorContainer => ({
    ErrorCode: 0
});
