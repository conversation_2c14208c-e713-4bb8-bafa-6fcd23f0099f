import {
    SBTechInitSessionRequest,
    SBTechDebitRequest,
    SBTechCreditRequest,
    SBTechGetBalanceRequest,
    SBTechRollbackTransactionRequest,
} from "../../skywind/entities/sbtech";
import { Customer, Merchant } from "../../skywind/models/merchant";
import { getDefaultCustomer } from "../../skywind/service/customer";

export class SBTechTestFixtures {
    public merchantCode = "sbtech";
    public playerCode = "sbtech_player";
    public merchant: Merchant = {
        merch_id: this.merchantCode,
        merch_pwd: "test_password",
        multiple_session: true,
        customers: {}
    };
    public initSessionRequest: SBTechInitSessionRequest = {
        "providerKey": "sbtech_key",
        "providerPass": "sbtech_Pa22",
        "clientToken": "empty",
        "gameId": "GAME001",
        "playerIp": "*******"
    };
    public getBalanceRequest: SBTechGetBalanceRequest = {
        "providerKey": "sbtech_key",
        "providerPass": "sbtech_Pa22",
        "sessionId": "test_session",
        "gameId": "GAME001",
        "playerId": this.playerCode,
        "brandId": this.merchantCode,
    };
    public debitRequest: SBTechDebitRequest = {
        "providerKey": "sbtech_key",
        "providerPass": "sbtech_Pa22",
        "sessionId": "test_session",
        "gameId": "GAME001",
        "playerId": this.playerCode,
        "brandId": this.merchantCode,
        "roundId": "1",
        "trxId": "1",
        "amount": 5000
    };
    public creditRequest: SBTechCreditRequest = {
        "providerKey": "sbtech_key",
        "providerPass": "sbtech_Pa22",
        "sessionId": "test_session",
        "gameId": "GAME001",
        "playerId": this.playerCode,
        "brandId": this.merchantCode,
        "roundId": "1",
        "trxId": "2",
        "debitTrxId": "1",
        "amount": 5000
    };
    public rollbackRequest: SBTechRollbackTransactionRequest = {
        "providerKey": "sbtech_key",
        "providerPass": "sbtech_Pa22",
        "sessionId": "test_session",
        "gameId": "GAME001",
        "playerId": this.playerCode,
        "brandId": this.merchantCode,
        "roundId": "1",
        "trxId": "3",
        "debitTrxId": "1"
    };
}
