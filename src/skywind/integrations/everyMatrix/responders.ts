import { RequestAdditionalInfo } from "./middleware";
import { BaseResponse, EMAuthenticateResponse, EVERY_MATRIX_JURISDICTION } from "./entities";
import { Request } from "express";
import { settings } from "../../models/settings";
import { getDataFromTemplateTicket } from "../../service/ticket";

const uuid = require("uuid");

export const authenticateResponder = (req: Request & RequestAdditionalInfo): EMAuthenticateResponse => {
    const launchToken = req.body.LaunchToken;

    if (launchToken.split("__").length === 4 || launchToken.split("__").length === 5) {
        // if it's right format
        const [custId, currency, jurisdiction, country] = getDataFromTemplateTicket(launchToken);

        return {
            Token: launchToken,
            TotalBalance: settings.amount.toString(),
            Currency: currency,
            UserName: "Andrew",
            UserId: custId,
            Country: country || "Ukraine",
            Age: "26",
            Sex: "female",
            Status: "Ok",
            Jurisdiction: jurisdiction
        };
    }

    return {
        Token: launchToken,
        TotalBalance: settings.amount.toString(),
        Currency: "EUR",
        UserName: "Andrew",
        UserId: uuid.v4().replace(/-/g, ""),
        Country: "Ukraine",
        Age: "26",
        Sex: "female",
        Status: "Ok",
        Jurisdiction: EVERY_MATRIX_JURISDICTION.UK
    };
};

/**
 * Responder for bet, win, cancel, getbalance
 * @param req
 */
export const basicResponder = (req: Request & RequestAdditionalInfo): BaseResponse => {
    return {
        TotalBalance: settings.amount.toString(),
        Currency: "EUR",
        Status: "Ok",
    };
};
