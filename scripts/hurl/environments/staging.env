# Staging Environment Configuration for Hurl Tests
# This file contains environment variables for running Hurl tests against staging services

# Management API Configuration
MAPI_URL=https://staging-mapi.skywindgroup.com
MAPI_PASSWORD=${STAGING_MAPI_PASSWORD}

# IMP-Mock Service Configuration
MOCK_URL=https://staging-mock.skywindgroup.com

# Terminal Service Configuration
TERMINAL_URL=https://staging-terminal.skywindgroup.com

# Wallet Service Configuration
WALLET_URL=https://staging-wallet.skywindgroup.com

# Game Provider Service Configuration
GAMEPROVIDER_URL=https://staging-gameprovider.skywindgroup.com

# Game Auth Service Configuration
GAMEAUTH_URL=https://staging-gameauth.skywindgroup.com

# Default Game Configuration
GAME_CODE=sw_mrmnky
GAME_PROVIDER=skywind
ALTERNATIVE_GAME_CODE=sw_ss

# Currency and Locale Settings
DEFAULT_CURRENCY=USD
DEFAULT_LANGUAGE=en
DEFAULT_COUNTRY=US
DEFAULT_JURISDICTION=COM

# Test Player Configuration
TEST_PLAYER_ID=staging_test_player_001
TEST_PLAYER_LOGIN=STAGINGPLAYER001
TEST_PLAYER_EMAIL=<EMAIL>
TEST_PLAYER_BALANCE=100000

# Merchant Configuration
TEST_MERCHANT_ID=staging_seamless_merchant
TEST_MERCHANT_PASSWORD=${STAGING_MERCHANT_PASSWORD}
PROMO_MERCHANT_ID=staging_promo_merchant
PROMO_MERCHANT_PASSWORD=${STAGING_PROMO_PASSWORD}

# Bonus and Promotional Settings
BONUS_BALANCE=25000
FREE_BET_BALANCE=10000
BET_LIMIT=1000

# Transaction Settings
DEFAULT_BET_AMOUNT=10.00
DEFAULT_WIN_AMOUNT=25.00
HIGH_BET_AMOUNT=100.00
EXCESSIVE_BET_AMOUNT=1000.00

# Session and Token Settings
SESSION_TIMEOUT=300
TOKEN_EXPIRY=3600

# Error Testing Configuration
INVALID_MERCHANT_ID=invalid_staging_merchant
INVALID_GAME_CODE=invalid_game_code
INVALID_CURRENCY=INVALID_CURRENCY
BLOCKED_PLAYER_STATUS=blocked
INSUFFICIENT_BALANCE=0

# Network and Timeout Settings
REQUEST_TIMEOUT=60
CONNECTION_TIMEOUT=30
RETRY_COUNT=3

# Logging and Debug Settings
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_RESPONSE_LOGGING=false

# Security Settings
ENABLE_SSL=true
SSL_VERIFY=true
API_KEY_HEADER=x-api-key

# Performance Testing Settings
CONCURRENT_USERS=5
TEST_DURATION=30
RAMP_UP_TIME=5

# Monitoring and Metrics
ENABLE_METRICS=true
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health

# Feature Flags
ENABLE_BONUS_FEATURES=true
ENABLE_FREE_BET_FEATURES=true
ENABLE_MULTI_CURRENCY=true
ENABLE_JACKPOT_FEATURES=true
ENABLE_TOURNAMENT_FEATURES=true

# Regulation Settings
DEFAULT_REGULATION=default
ITALIAN_REGULATION=italian
UK_REGULATION=uk
MALTA_REGULATION=malta

# Game Provider Settings
SKYWIND_PROVIDER_CODE=skywind
ITG_PROVIDER_CODE=itg
QUICKSPIN_PROVIDER_CODE=quickspin

# Jackpot Configuration
JACKPOT_POOL_ID=SW-SUPER-LION
JACKPOT_CURRENCY=EUR
JACKPOT_MIN_BET=1.00

# Tournament Configuration
TOURNAMENT_ID=staging_tournament_001
TOURNAMENT_ENTRY_FEE=5.00
TOURNAMENT_PRIZE_POOL=1000.00

# Limits Configuration
MIN_BET_AMOUNT=0.01
MAX_BET_AMOUNT=500.00
MAX_WIN_AMOUNT=500000.00
DAILY_LOSS_LIMIT=1000.00

# Country Restrictions
RESTRICTED_COUNTRIES=US,FR,IT
ALLOWED_COUNTRIES=GB,DE,SE,NO

# Game Features
ENABLE_AUTOPLAY=true
ENABLE_TURBO_MODE=true
ENABLE_SOUND=true
ENABLE_FULLSCREEN=true

# Cache Configuration
CACHE_TTL=3600
ENABLE_REDIS_CACHE=true
CACHE_PREFIX=sw_staging_

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_ENABLED=true

# Test Environment Specific
TEST_MODE=true
MOCK_EXTERNAL_SERVICES=false
ENABLE_TEST_ENDPOINTS=true
BYPASS_AUTHENTICATION=false

# Staging Specific Settings
STAGING_DB_HOST=staging-db.skywindgroup.com
STAGING_REDIS_HOST=staging-redis.skywindgroup.com
STAGING_KAFKA_HOST=staging-kafka.skywindgroup.com

# External Service URLs (Staging)
PAYMENT_GATEWAY_URL=https://staging-payments.skywindgroup.com
FRAUD_DETECTION_URL=https://staging-fraud.skywindgroup.com
KYC_SERVICE_URL=https://staging-kyc.skywindgroup.com

# Webhook Configuration
WEBHOOK_URL=https://staging-webhooks.skywindgroup.com
WEBHOOK_SECRET=${STAGING_WEBHOOK_SECRET}
ENABLE_WEBHOOKS=true
