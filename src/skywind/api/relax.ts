import logger from "../utils/logger";
import { NextFunction, Request, Response, Router } from "express";
import { isEmpty } from "../utils/isEmpty";
import { RelaxInvalidParameterError, RelaxUnauthorizedError } from "../errorRelax";
import { validateBaseAuth } from "./config/middleware";
import { getRelaxService } from "../service/relax";
import {
    ackPromotionResponder,
    getBalanceResponder,
    paymentResponder,
    rollbackResponder,
    verifyTokenResponder
} from "../service/relaxResponders";
import { CustomerHolder, MerchantHolder } from "../entities/common";
import { authenticate, authenticateCustomer } from "./config/relaxMiddleware";
import { authenticateCustomer as authCustomerBase } from "./config/middleware";
import { notSaveAnyDataMiddleware } from "./notSaveAnyData/not-save-any-data.middleware";

const log = logger("relax-mock:api");

const router: Router = Router();

function validateRequestFields(...fields: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        for (const param of fields) {
            const value = req.body[param];
            if (isEmpty(value)) {
                return next(new RelaxInvalidParameterError(`[${param}] is required`));
            }
        }

        return next();
    };
}

router.post("/:partnerId/verifytoken",
    notSaveAnyDataMiddleware(verifyTokenResponder),
    validateBaseAuth(new RelaxUnauthorizedError()),
    authenticate,
    validateRequestFields("token", "partnerid", "gameref", "channel", "ip"),
    async (req: Request & MerchantHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getRelaxService(req.merchant).verifyToken(req.body);
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/:partnerId/getbalance",
    notSaveAnyDataMiddleware(getBalanceResponder),
    validateBaseAuth(new RelaxUnauthorizedError()),
    authenticate,
    validateRequestFields("sessionid", "playerid", "gameref", "currency"),
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getRelaxService(req.merchant, req.customer).getBalance(req.body);
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/:partnerId/withdraw",
    notSaveAnyDataMiddleware(paymentResponder),
    validateBaseAuth(new RelaxUnauthorizedError()),
    authenticate,
    validateRequestFields("txtype", "sessionid", "txid", "roundid",
        "playerid", "gameref", "amount", "channel", "currency", "ended"),
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getRelaxService(req.merchant, req.customer).debit(req.body);
            res.send(result);
        } catch (err) {
            next(err);
        }
});

router.post("/:partnerId/deposit",
    notSaveAnyDataMiddleware(paymentResponder),
    validateBaseAuth(new RelaxUnauthorizedError()),
    authenticate,
    validateRequestFields("txtype", "sessionid", "txid", "roundid",
        "playerid", "gameref", "amount", "channel", "currency", "ended"),
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getRelaxService(req.merchant, req.customer).credit(req.body);
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

router.post("/:partnerId/rollback",
    notSaveAnyDataMiddleware(rollbackResponder),
    validateBaseAuth(new RelaxUnauthorizedError()),
    authenticate,
    validateRequestFields("sessionid", "txid", "roundid",
        "playerid", "gameref", "amount", "currency", "originaltxid"),
    authenticateCustomer,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = await getRelaxService(req.merchant, req.customer).rollback(req.body);
            res.send(result);
        } catch (err) {
            next(err);
        }
});

router.post("/:partnerId/ackpromotionadd",
    notSaveAnyDataMiddleware(ackPromotionResponder),
    validateBaseAuth(new RelaxUnauthorizedError()),
    authenticate,
    authCustomerBase((req) => ({
        code: req.body.promotions[0].playerid,
        sessionId: ""
    }) , new Error(), () => false),
    (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            const result = getRelaxService(req.merchant, req.customer).ackPromotion(req.body);
            res.send(result);
        } catch (err) {
            next(err);
        }
    });

export default router;
