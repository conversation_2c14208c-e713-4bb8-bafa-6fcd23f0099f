import {
    AckPromotionRequest,
    AckPromotionResponse,
    BalanceRequest,
    BalanceResponse,
    PaymentRequest,
    PaymentResponse,
    RELAX_ACTION,
    RELAX_JURISDICTION,
    RelaxCustomer,
    RollbackRequest,
    RollbackResponse,
    VerifyTokenRequest,
    VerifyTokenResponse
} from "../entities/relax";
import { Customer, Merchant } from "../models/merchant";
import {
    CurrencySessionMismatch,
    RelaxInsufficientBalanceError,
    RelaxInvalidTokenError,
    RelaxSessionExpiredError,
    RelaxTransactionNotFound,
} from "../errorRelax";
import * as Transaction from "../models/transaction";
import { TRANSACTION_INDEX, WALLET_ACTION } from "../models/transaction";
import { throwCustomError, throwCustomErrorIfExists } from "./customError";
import { getTicket } from "./ticket";
import config from "../config";
import { createCustomerSession } from "./session";

export interface RelaxWalletAPI {
    verifyToken(data: VerifyTokenRequest): Promise<VerifyTokenResponse>;

    getBalance(data: BalanceRequest): Promise<BalanceResponse>;

    debit(data: PaymentRequest): Promise<PaymentResponse>;

    credit(data: PaymentRequest): Promise<PaymentResponse>;

    rollback(data: RollbackRequest): Promise<RollbackResponse>;

    ackPromotion(data: AckPromotionRequest): AckPromotionResponse;
}

export class RelaxService implements RelaxWalletAPI {
    constructor(public merchant: Merchant, public customer?: Customer) {
    }

    public async verifyToken(data: VerifyTokenRequest): Promise<VerifyTokenResponse> {
        const ticket = getTicket(data.token, config.expirationTime.ticket, RelaxInvalidTokenError);

        const customer = this.merchant.customers[ticket.custId] as RelaxCustomer;

        if (!customer) {
            throw new RelaxSessionExpiredError();
        }

        await throwCustomErrorIfExists(RELAX_ACTION.VERIFY, this.merchant, customer);
        this.updateCustomerSession(customer);

        const verifyTokenResponse: VerifyTokenResponse = {
            locale: "en_UK",
            username: `relax_username_${customer.cust_id}`,
            customerid: `relax_customerid_${customer.cust_id}`,
            playerid: customer.cust_id,
            countrycode: "GB",
            lastlogin: new Date().toISOString(),
            birthdate: new Date().toISOString(),
            gender: 1,
            jurisdiction: customer.jurisdiction || RELAX_JURISDICTION.MT,
            balance: customer.balance.amount,
            currency: customer.balance.currency_code,
            partnerid: data.partnerid,
            sessionid: customer.cust_session_id,
            currencyrate: 1.0,
            promotions: customer.promotions
        };

        if (customer.operatorbetsettings) {
            verifyTokenResponse.operatorbetsettings = customer.operatorbetsettings;
        }

        return verifyTokenResponse;
    }

    private updateCustomerSession(customer: Customer): void {
        createCustomerSession(this.merchant, customer);
    }

    @throwCustomError({ action: RELAX_ACTION.GET_BALANCE })
    public async getBalance(data: BalanceRequest): Promise<BalanceResponse> {
        return {
            balance: this.customer.balance.amount,
            currency: this.customer.balance.currency_code,
            sessionid: this.customer.cust_session_id
        };
    }

    @throwCustomError({ action: RELAX_ACTION.DEBIT })
    public async debit(data: PaymentRequest): Promise<PaymentResponse> {
        const { amount } = data;
        this.checkInsufficientBalance(amount);

        return this.makePayment(data, WALLET_ACTION.debit);
    }

    @throwCustomError({ action: RELAX_ACTION.CREDIT })
    public async credit(data: PaymentRequest): Promise<PaymentResponse> {
        return this.makePayment(data, WALLET_ACTION.credit);
    }

    private makePayment(data: PaymentRequest, action: WALLET_ACTION): PaymentResponse {
        const { amount, txid, sessionid } = data;
        if (this.customer.currency_code !== data.currency) {
            throw new CurrencySessionMismatch();
        }
        let transaction = Transaction.getById(txid, action);
        if (!transaction) {
            transaction = Transaction.setById(txid, action, Math.abs(amount), this.customer.cust_id);
            if (data.txtype !== "freespinbet") {
                this.customer.balance.amount = this.customer.balance.amount + Math.abs(amount) * action;
            }
        }

        return {
            balance: this.customer.balance.amount,
            txid,
            relaxid: transaction[TRANSACTION_INDEX.EXTERNAL_ID],
            sessionid
        };
    }

    private checkInsufficientBalance(amount) {
        if ((this.customer.balance.amount + Math.abs(amount) * WALLET_ACTION.debit) < 0) {
            throw new RelaxInsufficientBalanceError();
        }
    }

    @throwCustomError({ action: RELAX_ACTION.ROLLBACK })
    public async rollback(data: RollbackRequest): Promise<RollbackResponse> {
        const { originaltxid, txid, sessionid } = data;

        let rollbackTransaction = Transaction.getById(txid, WALLET_ACTION.rollback);
        if (!rollbackTransaction) {
            const originalTransaction = Transaction.getById(originaltxid, WALLET_ACTION.debit);
            if (!originalTransaction) {
                throw new RelaxTransactionNotFound();
            }

            const [_, amount, __, isRollback] = originalTransaction;
            if (!isRollback) {
                Transaction.rollbackById(originaltxid, WALLET_ACTION.debit);
                this.customer.balance.amount = this.customer.balance.amount - amount;
            }

            rollbackTransaction = Transaction.setById(txid, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        }

        return {
            relaxtxid: rollbackTransaction[TRANSACTION_INDEX.EXTERNAL_ID],
            sessionid,
            txid
        };
    }

    public ackPromotion(data: AckPromotionRequest): AckPromotionResponse {
        const response: AckPromotionResponse = {
            promotion_statuses: []
        };

        for (const promo of data.promotions) {
            const relaxCustomer = this.customer as RelaxCustomer;
            if (relaxCustomer.promotions.length) {
                relaxCustomer.promotions = relaxCustomer.promotions.filter(p => p.txid !== promo.txid);
            }

            response.promotion_statuses.push({ status: "success", txid: promo.txid });
        }

        return response;
    }
}

export function getRelaxService(merchant: Merchant, customer?: Customer) {
    return new RelaxService(merchant, customer);
}
