import { NextFunction, Request, Response, Router } from "express";
import { EvereMatrixSimpleEngineService } from "./servicePredefined";
import {
    authorizePlayer,
    authoriseMerchant,
    buildMethodHash,
    RequestAdditionalInfo, validateMethodHash
} from "./middleware";
import { BaseResponse, EMRoundInfoResponse, EVERY_MATRIX_MOCK_TYPE } from "./entities";
import { EveryMatrixService } from "./service";
import { settings } from "../../models/settings";
import { isEmpty } from "../../utils/isEmpty";
import { EmptyFieldError } from "./errors";
import { authenticateResponder, basicResponder } from "./responders";

const router: Router = Router();

function validateFieldsForEmpty(...fields: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        for (const param of fields) {
            const value = req.body[param];
            if (isEmpty(value)) {
                return next(new EmptyFieldError(`[${param}] is required`));
            }
        }
        return next();
    };
}

function notSaveAnyDataMiddleware(responder: (req: Request & RequestAdditionalInfo) => object) {
    return (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req));
            } catch (err) {
                return next(err);
            }
        }
        return next();
    };
}

router.post("/Authenticate",
    notSaveAnyDataMiddleware(authenticateResponder),
    authoriseMerchant,
    validateFieldsForEmpty("LaunchToken", "RequestScope"),
    async (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            const result = new EvereMatrixSimpleEngineService().authenticatePlayer(req.body);
            res.send(result);
        } else if (req.mockType === EVERY_MATRIX_MOCK_TYPE.MockEnginePredefined) {
            const result = new EveryMatrixService(req.merchant, req.customer)
                .authenticatePredefinedPlayer(req.body, req.operatorId, req.jurisdiction);
            res.send(result);
        } else {
            const result = await new EveryMatrixService(req.merchant, req.customer)
                .authenticatePlayer(req.body);
            res.send(result);
        }

        next();
    });

router.post("/Bet",
    notSaveAnyDataMiddleware(basicResponder),
    authoriseMerchant,
    authorizePlayer,
    validateMethodHash,
    validateFieldsForEmpty("Token", "Hash", "Amount", "Currency", "ExternalId", "GameId", "RoundId"),
    async (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        let result: BaseResponse;
        if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            result = new EvereMatrixSimpleEngineService().bet(req.body.UserId, req.body);
        } else {
            result = await new EveryMatrixService(req.merchant, req.customer).bet(req.body);
        }
        res.send(result);
        next();
    });

router.post("/Win",
    notSaveAnyDataMiddleware(basicResponder),
    authoriseMerchant,
    authorizePlayer,
    validateMethodHash,
    validateFieldsForEmpty("Token", "Hash", "Amount", "Currency", "ExternalId",
        "GameId", "RoundId", "BetExternalId"),
    async (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        let result: BaseResponse;
        if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            result = new EvereMatrixSimpleEngineService().win(req.body.UserId, req.body);
        } else {
            result = await new EveryMatrixService(req.merchant, req.customer).win(req.body);
        }
        res.send(result);
        next();
    });

router.post("/RoundInfo",
    notSaveAnyDataMiddleware(basicResponder),
    authoriseMerchant,
    authorizePlayer,
    validateMethodHash,
    validateFieldsForEmpty(
        "Token",
        "Hash",
        "Currency",
        "GameId",
        "UserId",
        "RoundId",
        "SpinId",
        "RoundInfo",
        "GameType",
        "Time"
    ),
    async (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        let result: EMRoundInfoResponse;
        if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            result = new EvereMatrixSimpleEngineService().roundInfo(req.body.UserId, req.body);
        } else {
            result = await new EveryMatrixService(req.merchant, req.customer).roundInfo(req.body);
        }
        res.send(result);
        next();
    });

router.post("/BonusWin",
    notSaveAnyDataMiddleware(basicResponder),
    authoriseMerchant,
    authorizePlayer,
    validateMethodHash,
    validateFieldsForEmpty("Token", "Hash", "Amount", "Currency", "ExternalId", "GameId", "RoundId",
        "BonusId", "BonusType"),
    async (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        let result: BaseResponse;
        if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            result = new EvereMatrixSimpleEngineService().bonusWin(req.body.UserId, req.body);
        } else {
            result = await new EveryMatrixService(req.merchant, req.customer).bonusWin(req.body);
        }
        res.send(result);
        next();
    });

router.post("/Cancel",
    notSaveAnyDataMiddleware(basicResponder),
    authoriseMerchant,
    authorizePlayer,
    validateMethodHash,
    validateFieldsForEmpty("Token", "Hash", "ExternalId", "CanceledExternalId", "Hash"),
    async (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        let result: BaseResponse;
        if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            result = new EvereMatrixSimpleEngineService().cancel(req.body.UserId, req.body);
        } else {
            result = await new EveryMatrixService(req.merchant, req.customer).cancel(req.body);
        }
        res.send(result);
        next();
    });

router.post("/GetBalance",
    notSaveAnyDataMiddleware(basicResponder),
    authoriseMerchant,
    authorizePlayer,
    validateMethodHash,
    validateFieldsForEmpty("Token", "Hash", "Currency"),
    async (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        let result: BaseResponse;
        if (req.mockType === EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            result = new EvereMatrixSimpleEngineService().getBalance(req.body);
        } else {
            result = await new EveryMatrixService(req.merchant, req.customer).getBalance(req.body);
        }
        res.send(result);
        next();
    });

router.get("/Hash/:methodName",
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        const result = buildMethodHash(req.params.methodName);
        res.send(result);
        next();
    });

router.post("/Predefined/History",
    authoriseMerchant,
    authorizePlayer,
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        if (req.mockType !== EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            throw new Error("Wrong usage methods for predefined user. Please use predefined user's tokens");
        }
        const result = new EvereMatrixSimpleEngineService().getPlayerHistory(req.body);
        res.send(result);
        next();
    });

router.post("/Predefined/ClearHistory",
    authoriseMerchant,
    authorizePlayer,
    (req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) => {
        if (req.mockType !== EVERY_MATRIX_MOCK_TYPE.SimpleEngine) {
            throw new Error("Wrong usage methods for predefined user. Please use predefined user's tokens");
        }
        const result = new EvereMatrixSimpleEngineService().clearPlayerStatistic(req.body);
        res.send(result);
        next();
    });

export default router;
