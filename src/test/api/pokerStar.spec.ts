///<reference path="../../skywind/api/routers.ts"/>
import { getApplication } from "../../skywind/serverPokerStar";
import logger from "../../skywind/utils/logger";
import * as MerchantModel from "../../skywind/models/merchant";
import * as CustomErrorModel from "../../skywind/models/customError";
import {
    addMoneyForCustomer,
    createCustomer,
    createMerchant,
    createTicketForCustomer
} from "./helper";
import { clearAll, settings } from "../../skywind/models/settings";
import * as TransactionModel from "../../skywind/models/transaction";
import * as SessionModel from "../../skywind/models/session";
import { ACTION } from "../../skywind/api/pokerStar";
import { XML_ACCEPT_TYPE, XML_TYPE } from "../../skywind/utils/helper";
import config from "../../skywind/config";
import { defaultCustomerCurrency } from "../../skywind/service/customer";
import { suite, test } from "mocha-typescript";

const chai = require("chai");
const uuid = require("uuid");
chai.use(require("chai-shallow-deep-equal"));
chai.use(require("chai-xml"));
const expect = chai.expect;

const log = logger("ipm-mock:tests");

const request = require("supertest");
const url = "/api/xml/";

@suite()
class PokerStarSpec {

    public server;

    public merchantId: string;
    public merchantPassword: string = "abc";
    public customerId: string;
    public balance: number;
    public ticket;
    public trxId = uuid.v4();

    public async before() {
        this.server = await getApplication();
        clearAll();
        MerchantModel.setAll({});

        SessionModel.clearAll();
        TransactionModel.setAll({});

        CustomErrorModel.setAll({});

        this.merchantId = "merchant_" + uuid.v4().substr(0, 8);
        this.customerId = "customer_" + uuid.v4().substr(0, 8);

        // Balance in minor unit
        this.balance = Math.floor(Math.random() * 1000) + 10;

        await createMerchant(this.server, this.merchantId, this.merchantPassword);
        await createCustomer(this.server, this.merchantId, this.customerId);

        // Add balance in major unit
        await addMoneyForCustomer(this.server,
            this.merchantId,
            this.customerId,
            this.balance / config.currencyUnitMultiplier);
        this.ticket = await createTicketForCustomer(this.server, this.merchantId, this.customerId);
    };

    @test
    public async testRunStartGame() {
        log.debug("Run start game: " + this.ticket);
        const RequestTime = new Date().toJSON();

        await request(this.server)
            .post(url + "/startgame")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>5</RequestSn>
    </Header>
    <Body xsi:type="StartGameRequest">
        <Type>FINALIZABLE</Type>
    </Body>
</Message>`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text).xml.to.deep.equal(`
<?xml version="1.0" encoding="UTF-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Header>
    <RequestSn>5</RequestSn>
    <RequestTime>${RequestTime}</RequestTime>
    <RgToken>${this.ticket}</RgToken>
    <UserId>${this.customerId}</UserId>
  </Header>
  <Body xsi:type="StartGameResponse">
    <GameSessionId>1234</GameSessionId>
    <UserScreenName>${this.customerId}</UserScreenName>
    <PlayerMaxBet>1500</PlayerMaxBet>
    <UserCountry>CN</UserCountry>
    <AvailableFunds>${this.balance}</AvailableFunds>
    <TableType>
      <TableName>TableName</TableName>
      <TableTypeId>1234</TableTypeId>
      <GameTypeId>1234</GameTypeId>
      <VariantTypeId>1234</VariantTypeId>
      <IsPlayForFun>false</IsPlayForFun>
      <Currency>USD</Currency>
      <MinBetLimit>0</MinBetLimit>
      <MaxBetLimit>1500</MaxBetLimit>
      <VendorConfig>any extra information</VendorConfig>
    </TableType>
    <InterruptedGame>
      <GameSessionId/>
      <RgToken/>
      <HandId/>
    </InterruptedGame>
  </Body>
</Message>`);
            });
    };

    @test
    public async testMakeBet() {
        let RequestTime = new Date().toJSON();
        let bet = Math.floor(Math.random() * 10) + 1;

        log.debug(`Make bet 1, balance: ${this.balance} , WIN: ${bet} , ticket: ${this.ticket}`);

        await request(this.server)
            .post(url + "/bet")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetRequest}">
        <RgTxId>${this.trxId}</RgTxId>
        <BetAmount>${bet}</BetAmount>
    </Body>
</Message>
`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                this.balance = this.balance - bet;
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text)
                    .to
                    .include(`<AvailableFunds>${this.balance}</AvailableFunds>`);
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetResponse}">
        <TransactionId>${(+new Date() / 10000).toFixed()}</TransactionId>
        <HandId>1</HandId>
        <AvailableFunds>${this.balance}</AvailableFunds>
    </Body>
</Message>
`);
            });

        RequestTime = new Date().toJSON();
        bet = Math.floor(Math.random() * 10) + 1;

        log.debug(`Make bet 2, balance: ${this.balance} , BET: ${bet} , ticket: ${this.ticket}`);
        await request(this.server)
            .post(url + "/bet")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetRequest}">
        <RgTxId>${this.trxId}</RgTxId>
        <BetAmount>${bet}</BetAmount>
    </Body>
</Message>`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .then(response => {
                this.balance = this.balance - bet;
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text).to.include(`<AvailableFunds>${this.balance}</AvailableFunds>`);
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetResponse}">
        <TransactionId>${(+new Date() / 10000).toFixed()}</TransactionId>
        <HandId>1</HandId>
        <AvailableFunds>${this.balance}</AvailableFunds>
    </Body>
</Message>
`);
            });

    };

    @test
    public async testMakeHandresultWin() {
        log.debug("Make handresult: " + this.ticket);
        const RequestTime = new Date().toJSON();
        const win = Math.floor(Math.random() * 10) + 1;

        log.debug(`Make handresult (WIN), balance: ${this.balance} , WIN: ${win} , ticket: ${this.ticket}`);
        await request(this.server)
            .post(url + "/handresult")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultRequest" >
        <Pay>
            <RgTxId>${this.trxId}</RgTxId>
            <PayAmount>${win}</PayAmount>
        </Pay>
        <Handover>
            <RegularBet>250</RegularBet>
            <RegularPay>100</RegularPay>
            <SideBet>0</SideBet>
            <SideBetPay>0</SideBetPay>
            <Refund>0</Refund>
            <HandHistory>
                <CSRVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</CSRVersion>
                <PlayerVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</PlayerVersion>
            </HandHistory>
            <Jackpots>
                <Jackpot>
                    <JackpotId>SW-SUPER-SHOT-EU__super-shot-eu</JackpotId>
                    <JackpotBet>0</JackpotBet>
                    <JackpotContribution>984</JackpotContribution>
                    <JackpotResetContribution>376</JackpotResetContribution>
                    <JackpotPay>0</JackpotPay>
                </Jackpot>
                <Jackpot>
                    <JackpotId>SW-GRAND-POT-SHOT-EU__grand-pot-shot-eu</JackpotId>
                    <JackpotBet>0</JackpotBet>
                    <JackpotContribution>1644</JackpotContribution>
                    <JackpotResetContribution>949</JackpotResetContribution>
                    <JackpotPay>100</JackpotPay>
                </Jackpot>
            </Jackpots>
        </Handover>
    </Body>
</Message>
`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                const initialBalance = this.balance;
                this.balance = initialBalance + win;
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text)
                    .to
                    .include(`<AvailableFunds>${initialBalance}</AvailableFunds>`);
                expect(response.text).xml.to.deep.equal(`
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Message  xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
      <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultResponse">
        <HandId>1</HandId>
        <Pay>
            <TransactionId>${(+new Date() / 10000).toFixed()}</TransactionId>
            <AvailableFunds>${initialBalance}</AvailableFunds>
        </Pay>
    </Body>
</Message>
`);
            });

    };

    @test
    public async testMakeHandresultWinWithoutPayamount() {
        log.debug("Make handresult: " + this.ticket);
        const RequestTime = new Date().toJSON();
        const win = Math.floor(Math.random() * 10) + 1;

        await request(this.server)
            .post(url + "/handresult")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultRequest" >
        <Pay>
             <RgTxId>${this.trxId}</RgTxId>
            <PayAmount>0.1</PayAmount>
        </Pay>
        <Handover>
            <RegularBet>250</RegularBet>
            <RegularPay>100</RegularPay>
            <SideBet>0</SideBet>
            <SideBetPay>0</SideBetPay>
            <Refund>0</Refund>
            <HandHistory>
                <CSRVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</CSRVersion>
                <PlayerVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</PlayerVersion>
            </HandHistory>
        </Handover>
    </Body>
</Message>
`)
            .set("Content-Type", XML_ACCEPT_TYPE)
            .expect(400)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text).xml.to.deep.equal(`
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Message  xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
      <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="SystemError">
    <ErrorMessage>PayAmount is invalid value</ErrorMessage>
    </Body>
</Message>
`);
            });

        await request(this.server)
            .post(url + "/handresult")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultRequest" >
        <Handover>
            <RegularBet>250</RegularBet>
            <RegularPay>100</RegularPay>
            <SideBet>0</SideBet>
            <SideBetPay>0</SideBetPay>
            <Refund>0</Refund>
            <HandHistory>
                <CSRVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</CSRVersion>
                <PlayerVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</PlayerVersion>
            </HandHistory>
        </Handover>
    </Body>
</Message>
`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text).xml.to.deep.equal(`
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Message  xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
      <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultResponse">
        <HandId>1</HandId>

        <Pay>
            <AvailableFunds>${this.balance}</AvailableFunds>
            <TransactionId>0</TransactionId>
        </Pay>
    </Body>
</Message>
`);
            });

        await request(this.server)
            .post(url + "/handresult")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultRequest" >
        <Pay>
             <RgTxId>${this.trxId}</RgTxId>
            <PayAmount>${win}</PayAmount>
        </Pay>
        <Handover>
            <RegularBet>250</RegularBet>
            <RegularPay>100</RegularPay>
            <SideBet>0</SideBet>
            <SideBetPay>0</SideBetPay>
            <Refund>0</Refund>
            <HandHistory>
                <CSRVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</CSRVersion>
                <PlayerVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</PlayerVersion>
            </HandHistory>
        </Handover>
    </Body>
</Message>
`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                const initialBalance = this.balance;
                this.balance = initialBalance + win;
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text)
                    .to
                    .include(`<AvailableFunds>${initialBalance}</AvailableFunds>`);
                expect(response.text).xml.to.deep.equal(`
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Message  xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
      <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultResponse">
        <HandId>1</HandId>
        <Pay>
            <TransactionId>${(+new Date() / 10000).toFixed()}</TransactionId>
            <AvailableFunds>${initialBalance}</AvailableFunds>
        </Pay>
    </Body>
</Message>
`);
            });

    };

    @test
    public async testGetBalance() {
        log.debug("Get balance: " + this.ticket);
        const RequestTime = new Date().toJSON();

        await request(this.server)
            .post(url + "/getbalance")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>0</RequestSn>
    </Header>
    <Body xsi:type="GetBalanceRequest" >
    </Body>
</Message>`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text)
                    .to
                    .include(`<AvailableFunds>${this.balance}</AvailableFunds>`);
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>0</RequestSn>
    </Header>
    <Body xsi:type="GetBalanceResponse">
        <AvailableFunds>${this.balance}</AvailableFunds>
    </Body>
</Message>
`);
            });

    };

    @test
    public async testBetInsufficientBalance() {
        const RequestTime = new Date().toJSON();
        const bet = this.balance;

        log.debug("Make bet 1: " + this.ticket);
        await request(this.server)
            .post(url + "/bet")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetRequest}">
        <RgTxId>${this.trxId}</RgTxId>
        <BetAmount>${bet}</BetAmount>
    </Body>
</Message>`)
            .set("Content-Type", XML_ACCEPT_TYPE)
            .expect(200)
            .then(response => {
                this.balance = this.balance - bet;
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text)
                    .to
                    .include(`<AvailableFunds>${this.balance}</AvailableFunds>`);
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetResponse}">
        <TransactionId>${(+new Date() / 10000).toFixed()}</TransactionId>
        <HandId>1</HandId>
        <AvailableFunds>${this.balance}</AvailableFunds>
    </Body>
</Message>
`);
            });

        log.debug("Make bet 2: " + this.ticket);
        await request(this.server)
            .post(url + "/bet")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetRequest}">
        <RgTxId>${this.trxId}</RgTxId>
        <BetAmount>1</BetAmount>
    </Body>
</Message>`)
            .set("Content-Type", XML_ACCEPT_TYPE)
            .expect(200)
            .then(response => {
                expect(response.text).to.include(`<RgToken>${this.ticket}</RgToken>`);
                expect(response.text).to.include(`<RequestTime>${RequestTime}</RequestTime>`);
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="InsufficientFundsError">
        <ErrorMessage>Insufficient Funds Error</ErrorMessage>
    </Body>
</Message>
`);
            });
    };

    @test
    public async testGetBalanceBadTicket() {
        log.debug("Get balance bad ticket - Negative " + this.ticket);
        const RequestTime = new Date().toJSON();

        await request(this.server)
            .post(url + "/getbalance")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>BAD_TICKET_${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="GetBalanceRequest" >
    </Body>
</Message>`)
            .set("Content-Type", XML_ACCEPT_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).xml.to.deep.equal(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>BAD_TICKET_${this.ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="SessionTerminatedError">
        <ErrorMessage>Ticket not found</ErrorMessage>
    </Body>
</Message>
`);
            });

    };

    @test
    public async testGetBalanceMalformedXml() {
        log.debug("Get balance Malformed xml - Negative " + this.ticket);
        const RequestTime = new Date().toJSON();

        await request(this.server)
            .post(url + "/getbalance")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${this.customerId}</UserId>
        <RgToken>BAD_TICKET_${this.ticket}</RgToken>
        <RequestTime>${RequestTime}
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="GetBalanceRequest" >
    </Body>
</Message>`)
            .set("Content-Type", XML_ACCEPT_TYPE)
            .expect(400)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId></UserId>
        <RgToken></RgToken>
        <RequestTime></RequestTime>
        <RequestSn>0</RequestSn>
    </Header>
    <Body xsi:type="SystemError">
        <ErrorMessage>Invalid input: Malformed xml</ErrorMessage>
    </Body>
</Message>
`);
            });
    };
}

@suite()
class PokerStarLoadSpec {
    public server;

    public async before() {
        this.server = await getApplication();
        clearAll();
        MerchantModel.setAll({});

        SessionModel.clearAll();
        TransactionModel.setAll({});

        CustomErrorModel.setAll({});
        settings.notSaveAnyData = true;

    };

    @test
    public async testRunStartGame() {
        const customerId = "customer_" + uuid.v4().substr(0, 8);
        const ticket = uuid.v4().substr(0, 8);

        log.debug("Run start game: " + ticket);
        const RequestTime = new Date().toJSON();

        await request(this.server)
            .post(url + "/startgame")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${customerId}</UserId>
        <RgToken>${ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>5</RequestSn>
    </Header>
    <Body xsi:type="StartGameRequest">
        <Type>FINALIZABLE</Type>
    </Body>
</Message>`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).xml.to.deep.equal(`<?xml version="1.0" encoding="UTF-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Header>
    <RequestSn>5</RequestSn>
    <RequestTime>${RequestTime}</RequestTime>
    <RgToken>${ticket}</RgToken>
    <UserId>${customerId}</UserId>
  </Header>
  <Body xsi:type="StartGameResponse">
    <GameSessionId>1234</GameSessionId>
    <UserScreenName>${customerId}</UserScreenName>
    <UserCountry>CN</UserCountry>
    <AvailableFunds>${settings.amount}</AvailableFunds>
    <TableType>
      <TableName>TableName</TableName>
      <TableTypeId>1234</TableTypeId>
      <GameTypeId>1234</GameTypeId>
      <VariantTypeId>1234</VariantTypeId>
      <IsPlayForFun>false</IsPlayForFun>
      <Currency>${defaultCustomerCurrency}</Currency>
      <MinBetLimit>0</MinBetLimit>
      <MaxBetLimit>1500</MaxBetLimit>
      <VendorConfig>any extra information</VendorConfig>
    </TableType>
    <InterruptedGame>
      <GameSessionId/>
      <RgToken/>
      <HandId/>
    </InterruptedGame>
    </Body>
</Message>`);
            });
    };

    @test
    public async testMakeBet() {
        const customerId = "customer_" + uuid.v4().substr(0, 8);
        const ticket = uuid.v4().substr(0, 8);
        const RequestTime = new Date().toJSON();
        const bet = Math.floor(Math.random() * 10) + 1;

        log.debug(`Make bet 1, WIN: ${bet} , ticket: ${ticket}`);

        await request(this.server)
            .post(url + "/bet")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${customerId}</UserId>
        <RgToken>${ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetRequest}">
        <RgTxId>1</RgTxId>
        <BetAmount>${bet}</BetAmount>
    </Body>
</Message>
`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${customerId}</UserId>
        <RgToken>${ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>2</RequestSn>
    </Header>
    <Body xsi:type="${ACTION.BetResponse}">
        <TransactionId>${(+new Date() / 10000).toFixed()}</TransactionId>
        <HandId>1</HandId>
        <AvailableFunds>${settings.amount}</AvailableFunds>
    </Body>
</Message>
`);
            });

    };

    @test
    public async testMakeHandresultWin() {
        const customerId = "customer_" + uuid.v4().substr(0, 8);
        const ticket = uuid.v4().substr(0, 8);
        const RequestTime = new Date().toJSON();
        const win = Math.floor(Math.random() * 10) + 1;

        log.debug(`Make handresult (WIN),  WIN: ${win} , ticket: ${ticket}`);
        await request(this.server)
            .post(url + "/handresult")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${customerId}</UserId>
        <RgToken>${ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultRequest" >
        <Pay>
            <RgTxId>2</RgTxId>
            <PayAmount>${win}</PayAmount>
        </Pay>
        <Handover>
            <RegularBet>250</RegularBet>
            <RegularPay>100</RegularPay>
            <SideBet>0</SideBet>
            <SideBetPay>0</SideBetPay>
            <Refund>0</Refund>
            <HandHistory>
                <CSRVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</CSRVersion>
                <PlayerVersion>http://api.cd2.d.skywind-tech.com:3910/history/game</PlayerVersion>
            </HandHistory>
            <Jackpots>
                <Jackpot>
                    <JackpotId>SW-SUPER-SHOT-EU__super-shot-eu</JackpotId>
                    <JackpotBet>0</JackpotBet>
                    <JackpotContribution>984</JackpotContribution>
                    <JackpotResetContribution>376</JackpotResetContribution>
                    <JackpotPay>0</JackpotPay>
                </Jackpot>
                <Jackpot>
                    <JackpotId>SW-GRAND-POT-SHOT-EU__grand-pot-shot-eu</JackpotId>
                    <JackpotBet>0</JackpotBet>
                    <JackpotContribution>1644</JackpotContribution>
                    <JackpotResetContribution>949</JackpotResetContribution>
                    <JackpotPay>100</JackpotPay>
                </Jackpot>
            </Jackpots>
        </Handover>
    </Body>
</Message>
`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).xml.to.deep.equal(`
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Message  xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
      <UserId>${customerId}</UserId>
        <RgToken>${ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>3</RequestSn>
    </Header>
    <Body xsi:type="HandResultResponse">
        <HandId>1</HandId>
        <Pay>
            <TransactionId>${(+new Date() / 10000).toFixed()}</TransactionId>
            <AvailableFunds>${settings.amount}</AvailableFunds>
        </Pay>
    </Body>
</Message>
`);
            });

    };

    @test
    public async testGetBalance() {
        const customerId = "customer_" + uuid.v4().substr(0, 8);
        const ticket = uuid.v4().substr(0, 8);
        const RequestTime = new Date().toJSON();

        await request(this.server)
            .post(url + "/getbalance")
            .type("form")
            .send(`
<?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${customerId}</UserId>
        <RgToken>${ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>0</RequestSn>
    </Header>
    <Body xsi:type="GetBalanceRequest" >
    </Body>
</Message>`)
            .set("Content-Type", XML_TYPE)
            .expect(200)
            .expect("Content-Type", XML_TYPE + "; charset=utf-8")
            .then(response => {
                expect(response.text).xml.to.deep.equal(`
 <?xml version="1.0" encoding="utf-8"?>
<Message xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Header>
        <UserId>${customerId}</UserId>
        <RgToken>${ticket}</RgToken>
        <RequestTime>${RequestTime}</RequestTime>
        <RequestSn>0</RequestSn>
    </Header>
    <Body xsi:type="GetBalanceResponse">
        <AvailableFunds>${settings.amount}</AvailableFunds>
    </Body>
</Message>
`);
            });

    };
}
