import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import config from "../../skywind/config";
import { PariplayBalanceRequest } from "../../skywind/entities/pariplay";
import { PariplayBaseSpec } from "./auth.spec";
import { ACTION } from "../../skywind/service/pariplay/actions";

const request = require("supertest");

@suite
class PariplayGetBalanceSpec extends PariplayBaseSpec {
    @test
    public async testGetBalanceForInvalidMerchant() {
        const balanceRequest: PariplayBalanceRequest = {
            PlayerId: this.playerCode,
            Token: this.sessionId,
            GameCode: "someGame",
            Account: {
                Username: "someInvalidMerchant",
                Password: "somePwd"
            }
        };
        const response = await request(this.server)
            .post("/EVI/GetBalance")
            .send(balanceRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(2);
    }

    @test
    public async testGetBalanceIfPlayerNotFound() {
        const balanceRequest: PariplayBalanceRequest = {
            PlayerId: "someInvalidPlayer",
            Token: this.sessionId,
            GameCode: "someGame",
            Account: {
                Username: this.merchantCode,
                Password: "somePwd"
            }
        };
        const response = await request(this.server)
            .post("/EVI/GetBalance")
            .send(balanceRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async getBalanceSessionNotFound() {
        const playerWithoutSession = this.addPlayerToMerchant("chebureck");

        const balanceRequest: PariplayBalanceRequest = {
            PlayerId: playerWithoutSession.cust_id,
            Token: "invalidToken",
            GameCode: "someGame",
            Account: {
                Username: this.merchantCode,
                Password: "somePwd"
            }
        };
        const response = await request(this.server)
            .post("/EVI/GetBalance")
            .send(balanceRequest)
            .expect(200);

        expect(response.body.ErrorCode).to.be.equal(4);
    }

    @test
    public async getBalanceSuccess() {
        const balanceRequest: PariplayBalanceRequest = {
            PlayerId: this.merchantCode,
            Token: this.sessionId,
            GameCode: "someGame",
            Account: {
                Username: this.merchantCode,
                Password: "somePwd"
            }
        };
        const response = await request(this.server)
            .post("/EVI/GetBalance")
            .send(balanceRequest)
            .expect(200);

        expect(response.body.Balance).to.be.equal(config.defaultSettings.amount);
        expect(response.body.ErrorCode).to.be.equal(0);
    }

    @test
    public async testCustomError() {
        this.createCustomError(900, ACTION.GET_BALANCE);
        const response = await request(this.server)
            .post("/EVI/GetBalance")
            .send({
            PlayerId: this.merchantCode,
            Token: this.sessionId,
            GameCode: "someGame",
            Account: {
                Username: this.merchantCode,
                Password: "somePwd"
            }
        }).expect(200);

        expect(response.body.ErrorCode).to.be.equal(900);
    }

    @test
    public async testCMAMessage() {
        this.createExtraData({
            Message: [{
                Title: "title",
                Text: "message",
                DisplayType: 2,
                Buttons: [
                    {
                        Text: "Quit Game",
                        Action: "quit"
                    },
                    {
                        Text: "Ok",
                        Action: "continue"
                    },
                    {
                        Text: "View History",
                        Action: "link",
                        Link: "https://www.casino.com/myaccount/history"
                    }
                ]
            }]
        }, ACTION.GET_BALANCE);

        const response = await request(this.server)
            .post("/EVI/GetBalance")
            .send({
                PlayerId: this.merchantCode,
                Token: this.sessionId,
                GameCode: "someGame",
                Account: {
                    Username: this.merchantCode,
                    Password: "somePwd"
                }
            }).expect(200);

        expect(response.body.ErrorCode).to.be.equal(0);
        expect(response.body.Message).is.exist;
    }
}
