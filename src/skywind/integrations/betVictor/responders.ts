import { Request } from "express";
import { AccountResponse, AuthenticateResponse, BasePaymentResponse } from "./entities";
import { getDataFromTemplateTicket } from "../../service/ticket";

export const authenticateResponder = (req: Request): AuthenticateResponse => {
    return {
        responseCode: 0,
        sessionId: req.body.authToken
    };
};

export const accountDetailsResponder = (req: Request): AccountResponse => {
    const token = req.body.sessionId;

    if (token.split("__").length === 4 || token.split("__").length === 5) {
        // if it's right format
        const [custId, currency, jurisdiction, country] = getDataFromTemplateTicket(token);

        return {
            accountNumber: custId,
            country: country || "GB",
            currency: currency,
            responseCode: 0,
            firstName: "Peter",
            lastName: "Forsberg",
            balance: 10500.00
        };
    }

    return {
        accountNumber: token,
        country: "GB",
        currency: "GBP",
        responseCode: 0,
        firstName: "Peter",
        lastName: "Forsberg",
        balance: 10500.00
    };
};

export const placeBetResponder = (req: Request): BasePaymentResponse => {
    return {
        responseCode: 0,
        transactionId: "12asdfak12356fsadf_bet",
        duplicate: false,
        balance: 10500.00
    };
};

export const additionalBetResponder = (req: Request): BasePaymentResponse => {
    return {
        responseCode: 0,
        transactionId: "12asdfak12356fsadf_bet",
        duplicate: false,
        balance: 10500.00
    };
};

export const settleBetResponder = (req: Request): BasePaymentResponse => {
    return {
        responseCode: 0,
        transactionId: "12asdfak12356fsadf_win",
        duplicate: false,
        balance: 10500.00
    };
};

export const additionalWinningsResponder = (req: Request): BasePaymentResponse => {
    return {
        responseCode: 0,
        transactionId: "12asdfak12356fsadf_win",
        duplicate: false,
        balance: 10500.00
    };
};

export const promoCreditResponder = (req: Request): BasePaymentResponse => {
    return {
        responseCode: 0,
        transactionId: "12asdfak12356fsadf_win",
        duplicate: false,
        balance: 10500.00
    };
};

export const rollbackBetResponder = (req: Request): BasePaymentResponse => {
    return {
        responseCode: 0,
        transactionId: "12asdfak12356fsadf_cancel",
        duplicate: false,
        balance: 10500.00
    };
};
