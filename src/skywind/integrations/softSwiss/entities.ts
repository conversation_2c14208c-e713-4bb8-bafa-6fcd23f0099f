import { Request } from "express";
import { Customer, Merchant } from "../../models/merchant";

export enum TransactionType {
    BALANCE = "balance",
    BET = "bet",
    WIN = "win",
    ROLLBACK = "rollback",
    FREESPINS_FINISH = "freespins-finish"
}

interface Transaction {
    type: TransactionType;
    amount: number;
    id_provider: string;
    jackpot_contribution?: number;
    jackpot_win?: number;
}

export interface OperatorBalanceRequest {
    account: string;
    currency: string;
    game_id: string;
    session_id?: string;
}

export interface OperatorBetWinRequest {
    account_id: string;
    currency: string;
    game_id: string;
    round_id?: string;
    transactions?: Array<Transaction>;
    finished?: boolean;
    session_id?: string;
    sm_result?: string;
}

interface ResponseTransaction {
    // from request
    id: string;
    // unique transaction identifier in wallet
    id_provider: string;
}

export interface OperatorBalanceResponse {
    balance: number;
}

export interface OperatorBetWinResponse {
    balance: number;
    round_id: string;
    transactions?: Array<ResponseTransaction>;
}

export interface OperatorRollbackResponse {
    balance: number;
    round_id: string;
    transactions: Array<ResponseTransaction>
}

export interface RequestEx extends Request {
    merchant: Merchant;
    customer: Customer;
}

export interface OperatorRollbackRequest {
    account_id: string;
    currency: string;
    game_id: string;
    round_id_provider: string;
    transactions: Array<RollbackTransaction>;
    finished: boolean;
    session_id?: string;
    sm_result?: string;
}

export interface RollbackTransaction {
    type: TransactionType.ROLLBACK;
    id_provider: string;
    original_id_provider: string;
}

export interface OperatorFreeSpinsFinishRequest {
    amount: number;
    issue_id: string;
}
