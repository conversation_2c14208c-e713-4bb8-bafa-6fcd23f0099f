export class GANError extends <PERSON><PERSON>r {
    constructor(public code: number, public errorMessage: string) {
        super(errorMessage);
    }
}

/*
 * MEMO: Validation Errors
 */

export class MissingAuthElement extends GANError {
    constructor() {
        super(100, "Missing auth element");
    }
}

export class <PERSON><PERSON><PERSON><PERSON><PERSON> extends GANError {
    constructor() {
        super(101, "Missing auth user");
    }
}

export class Missing<PERSON>uthSecret extends GANError {
    constructor() {
        super(102, "Missing auth secret");
    }
}

export class MissingChannelElement extends GANError {
    constructor() {
        super(110, "Missing channel element");
    }
}

export class MissingChannelRef extends GANError {
    constructor() {
        super(111, "Missing channel ref");
    }
}

export class InvalidChannelRef extends GANError {
    constructor() {
        super(112, "Invalid channel element");
    }
}

export class MissingPayload extends GANError {
    constructor() {
        super(120, "Missing payload");
    }
}

export class MissingTokenToInvalidate extends GANError {
    constructor() {
        super(130, "Missing token to invalidate");
    }
}

export class MissingPlayerGUIDForIssue extends GANError {
    constructor() {
        super(131, "Missing player GUID for issue");
    }
}

export class MissingGameTypeRefForIssue extends GANError {
    constructor() {
        super(132, "Missing game type ref for issue");
    }
}

export class MissingPlayerGUIDForPlayer extends GANError {
    constructor() {
        super(140, "Missing game type ref for issue");
    }
}

export class InvalidPlayerGUID extends GANError {
    constructor() {
        super(141, "Invalid player GUID");
    }
}

export class MissingContestRef extends GANError {
    constructor() {
        super(150, "Missing contest ref for contest to create/find/close");
    }
}

export class InvalidContestRef extends GANError {
    constructor() {
        super(151, "Invalid contest ref");
    }
}

export class MissingGameTypeRef extends GANError {
    constructor() {
        super(152, "Missing game type ref for contest to create/find");
    }
}

export class MissingPlayerGUID extends GANError {
    constructor() {
        super(153, "Missing player GUID for contest to create/find");
    }
}

export class MissingBuyInRef extends GANError {
    constructor() {
        super(154, "Missing buy-in ref");
    }
}

export class MissingBuyInCurrency extends GANError {
    constructor() {
        super(155, "Missing buy-in currency");
    }
}

export class MissingBuyInAmount extends GANError {
    constructor() {
        super(156, "Missing buy-in amount");
    }
}

export class MissingExternalRef extends GANError {
    constructor() {
        super(157, "Missing external ref");
    }
}

export class InvalidExternalRef extends GANError {
    constructor() {
        super(158, "Invalid external ref");
    }
}

export class InvalidBuyInAmount extends GANError {
    constructor() {
        super(159, "Invalid buy-in ref");
    }
}

export class MissingPlayRef extends GANError {
    constructor() {
        super(160, "Missing play ref");
    }
}

export class InvalidPlayRef extends GANError {
    constructor() {
        super(161, "Invalid play ref");
    }
}

export class MissingContestRefForPlay extends GANError {
    constructor() {
        super(162, "Missing contest ref for play to process");
    }
}

export class MissingPlayerGUIDForPlay extends GANError {
    constructor() {
        super(163, "Missing player GUID for play to process");
    }
}

export class NoActionSpecified extends GANError {
    constructor() {
        super(164, "No actions specified");
    }
}

export class MissingActionRef extends GANError {
    constructor() {
        super(165, "Missing action ref");
    }
}

export class InvalidActionRef extends GANError {
    constructor() {
        super(166, "Invalid action ref");
    }
}

export class MissingActionType extends GANError {
    constructor() {
        super(167, "Missing action type");
    }
}

export class InvalidActionType extends GANError {
    constructor() {
        super(168, "Invalid action type");
    }
}

export class MissingActionAmount extends GANError {
    constructor() {
        super(169, "Missing action amount");
    }
}

export class InvalidStakeAmount extends GANError {
    constructor() {
        super(170, "Invalid stake amount");
    }
}

export class InvalidWinAmount extends GANError {
    constructor() {
        super(171, "Invalid win amount");
    }
}

export class FinishFlagCantBeSet extends GANError {
    constructor() {
        super(172, "Finish flag can't be set unless a win action is specified");
    }
}

export class MissingCurrency extends GANError {
    constructor() {
        super(173, "Missing currency");
    }
}

export class InvalidCurrency extends GANError {
    constructor() {
        super(174, "Invalid currency");
    }
}

export class NoTransactionSpecified extends GANError {
    constructor() {
        super(180, "No transaction(s) specified");
    }
}

export class MissingTransactionRef extends GANError {
    constructor() {
        super(181, "Missing transaction ref");
    }
}

export class InvalidTransactionRef extends GANError {
    constructor() {
        super(182, "Invalid transaction ref");
    }
}

export class MissingTransactionType extends GANError {
    constructor() {
        super(183, "Missing transaction type");
    }
}

export class InvalidTransactionType extends GANError {
    constructor() {
        super(184, "Invalid transaction type");
    }
}

export class MissingPlayerGUIDForTransaction extends GANError {
    constructor() {
        super(185, "Missing player GUID for transaction");
    }
}

export class MissingCurrencyForTransaction extends GANError {
    constructor() {
        super(186, "Missing currency for transaction");
    }
}

export class InvalidTransactionCurrency extends GANError {
    constructor() {
        super(187, "Invalid transaction currency");
    }
}

export class MissingAmountForTransaction extends GANError {
    constructor() {
        super(188, "Missing amount for transaction");
    }
}

export class InvalidTransactionAmount extends GANError {
    constructor() {
        super(189, "Invalid transaction amount");
    }
}

export class MissingTransactionGroupReference extends GANError {
    constructor() {
        super(190, "Missing transaction group reference");
    }
}

export class InvalidTransactionGroupReference extends GANError {
    constructor() {
        super(191, "Invalid transaction group reference");
    }
}

export class InconsistentTransactionGroupReference extends GANError {
    constructor() {
        super(192, "Inconsistent transaction group reference");
    }
}

export class InvalidGameTypeRefForTransaction extends GANError {
    constructor() {
        super(193, "Invalid game type ref for transaction");
    }
}

export class MissingPlayerGUIDForLimit extends GANError {
    constructor() {
        super(800, "Missing player GUID for limit");
    }
}

export class MissingContestRefForLimit extends GANError {
    constructor() {
        super(801, "Missing contest ref for limit");
    }
}

/*
 * MEMO: Access Control Errors
 */

export class InvalidCredentials extends GANError {
    constructor() {
        super(200, "Invalid credentials");
    }
}

export class InsufficientPrivileges extends GANError {
    constructor() {
        super(201, "Insufficient privileges");
    }
}

export class InvalidToken extends GANError {
    constructor() {
        super(202, "Insufficient token");
    }
}

export class AccessViolation extends GANError {
    constructor() {
        super(203, "Access violation");
    }
}

/*
 * MEMO: State / Logic Errors
 */

export class NoSuchChannel extends GANError {
    constructor() {
        super(300, "No such channel");
    }
}

export class NoSuchPlayer extends GANError {
    constructor() {
        super(301, "No such player");
    }
}

export class NoSuchGameType extends GANError {
    constructor() {
        super(302, "No such game type");
    }
}

export class NoSuchContest extends GANError {
    constructor() {
        super(303, "No such contest");
    }
}

export class NoSuchPlay extends GANError {
    constructor() {
        super(304, "No such play");
    }
}

export class ContestAlreadyExists extends GANError {
    constructor() {
        super(305, "Contest already exists for different game and/or player");
    }
}

export class ContestIsPending extends GANError {
    constructor() {
        super(306, "Contest is pending");
    }
}

export class ContestIsClosed extends GANError {
    constructor() {
        super(307, "Contest is closed");
    }
}

export class PlayIsClosed extends GANError {
    constructor() {
        super(308, "Play is closed");
    }
}

export class PlayIsVoided extends GANError {
    constructor() {
        super(309, "Play is voided");
    }
}

export class PlayHasNotBeenStaked extends GANError {
    constructor() {
        super(310, "Play has not been staked");
    }
}

export class PlayCantSpan extends GANError {
    constructor() {
        super(311, "Play can't span more than one contest");
    }
}

export class PlayActionHasBeenSubmitted extends GANError {
    constructor() {
        super(312, "Play action has already been submitted but with different amount/type");
    }
}

export class ExistingBuyInAttemptIsInProgress extends GANError {
    constructor() {
        super(313, "An existing buy-in attempt is already in progress");
    }
}

export class ActionReferenceIsNonUnique extends GANError {
    constructor() {
        super(314, "Action reference is non-unique");
    }
}

export class NoSuchBuyIn extends GANError {
    constructor() {
        super(315, "No such buy-in");
    }
}

export class ContestReferenceExists extends GANError {
    constructor() {
        super(316, "Contest reference already exists");
    }
}

export class BuyInRequired extends GANError {
    constructor() {
        super(317, "Buy-in required");
    }
}

/*
 * MEMO: Account Errors
 */

export class AccountIsClosed extends GANError {
    constructor() {
        super(400, "Account is closed");
    }
}

export class AccountIsSuspended extends GANError {
    constructor() {
        super(401, "Account is suspended");
    }
}

export class InsufficientFunds extends GANError {
    constructor() {
        super(402, "Insufficient funds");
    }
}

export class InsufficientCash extends GANError {
    constructor() {
        super(403, "Insufficient cash");
    }
}

/*
 * MEMO: Regulatory / Limit Errors
 */

export class SpendLimitReached extends GANError {
    constructor() {
        super(500, "Spend limit reached");
    }
}

export class TimeLimitReached extends GANError {
    constructor() {
        super(501, "Time limit reached");
    }
}

export class BuyInLimitReached extends GANError {
    constructor() {
        super(502, "Buy in limit reached");
    }
}

export class ContestCreationDisallowed extends GANError {
    constructor() {
        super(503, "Contest creation disallowed by third party");
    }
}

export class BuyInDisallowed extends GANError {
    constructor() {
        super(504, "Buy-in disallowed by third party");
    }
}

/*
 * MEMO: Other Errors
 */

export class InternalError extends GANError {
    constructor() {
        super(600, "Internal error");
    }
}
