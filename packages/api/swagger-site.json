{"swagger": "2.0", "info": {"description": "Skywind - API for Site", "version": "5.56", "title": "Skywind - API for Site"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-site-token", "in": "header"}}, "paths": {"/games": {"get": {"tags": ["Game"], "security": [{"apiKey": []}], "summary": "Gets list of available games", "description": "Gets list of available games for a site and filters it by parameters. Case sortBy == categoryList returns games from included list of category before others.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/gameProviderCodeEquality"}, {"$ref": "#/parameters/gameProviderCode__in"}, {"$ref": "#/parameters/gameProviderTitleEquality"}, {"$ref": "#/parameters/gameProviderTitleContains"}, {"$ref": "#/parameters/gameProviderTitleNotContains"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/shortInfo"}, {"$ref": "#/parameters/limitsCurrencyInQuery"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 306: Game is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}}}}, "/fun/games/{gameCode}": {"get": {"tags": ["Game"], "security": [{"apiKey": []}], "summary": "Gets player game URL for playing in fun mode", "description": "The method enables the player to play a particular game in fun mode", "parameters": [{"$ref": "#/parameters/gameCode"}], "responses": {"200": {"description": "Game URL for player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 306: Game is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}}}}, "/languages": {"get": {"security": [{"apiKey": []}], "tags": ["Lists"], "summary": "Gets languages list", "description": "Gets list of all avaliable languages for a site", "responses": {"200": {"description": "list of languages", "schema": {"$ref": "#/definitions/Languages"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/countries": {"get": {"security": [{"apiKey": []}], "tags": ["Lists"], "summary": "Gets countries list", "description": "Gets list of all avaliable countries for a site", "responses": {"200": {"description": "List of countries", "schema": {"$ref": "#/definitions/Countries"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/currencies": {"get": {"security": [{"apiKey": []}], "tags": ["Lists"], "summary": "Gets currencies list", "description": "Gets list of all avaliable currencies for a site", "responses": {"200": {"description": "List of currencies", "schema": {"type": "array", "items": {"$ref": "#/definitions/Currency"}}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/gameproviders": {"get": {"security": [{"apiKey": []}], "tags": ["Game"], "summary": "Gets list of game providers", "parameters": [{"$ref": "#/parameters/isTest"}], "description": "Gets list of all game providers for site", "responses": {"200": {"description": "List of game providers", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameProviderShortInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/gamecategories": {"get": {"security": [{"apiKey": []}], "tags": ["Game"], "summary": "Gets list of game categories", "description": "Gets list of all avaliable game categories for a site", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/includeGames"}, {"$ref": "#/parameters/includeGamesAmount"}, {"$ref": "#/parameters/gameCategoryType"}], "responses": {"200": {"description": "List of game categories", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCategoryShortInfo"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}}}}, "/gamecategories/{gameCategoryId}": {"parameters": [{"$ref": "#/parameters/gameCategoryInPath"}], "get": {"security": [{"apiKey": []}], "tags": ["Game"], "summary": "Gets gamecategory by public Id", "description": "Gets detailed gamecategory information with gameproviders, tags and games", "responses": {"200": {"description": "Game Category information", "schema": {"$ref": "#/definitions/GameCategoryInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "404": {"description": "- 304: Game category not found\n"}}}}, "/customer/register": {"post": {"security": [{"apiKey": []}], "tags": ["Customer"], "summary": "Registers new customer", "description": "Creates new customer with code and password. Other properties are not required.", "parameters": [{"$ref": "#/parameters/registerPlayer"}], "responses": {"201": {"description": "Created customer", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 41: The Email Address is in an invalid format\n- 42: Customer code should contain from 6 to 30 characters\n- 43: Password should contain at least 8 letters and contains at least one letter, one uppercase letter and one digit\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 199: Email already used\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 707: <PERSON> has exceeded of test players. It's max number.\n"}, "404": {"description": "- 211: Game group is not found\n"}, "409": {"description": "- 100: Player already exist\n"}}}}, "/customer/login": {"post": {"security": [{"apiKey": []}], "tags": ["Customer"], "summary": "Logs customer in", "description": "Logs customer in by code and password. Method returns a player's token.", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["code", "password"], "properties": {"code": {"type": "string", "description": "customer's code", "example": "PL0034SrvCN02"}, "password": {"type": "string", "format": "password", "description": "password is longer than or equal 8 letters and contains at least one letter, one uppercase letter and one digit", "example": "19Letters&4Numbers&3Signs!"}}}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginPlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 43: Provided password is not valid\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 223: Customer created without password\n- 230: Player authentication is blocked\n- 712: Player is suspended\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded."}, "404": {"description": "- 102: Player not found\n"}}}}, "/customer/password/reset": {"post": {"security": [{"apiKey": []}], "tags": ["Customer"], "summary": "Reset password via email", "description": "Sends confirmation email for reset player's password", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["email", "redirectTo"], "properties": {"email": {"type": "string", "description": "player's email", "example": "<EMAIL>"}, "redirectTo": {"type": "string", "description": "url of site with \"new password form\" guid will added to the end of url", "example": "https://game-portal.fart88.com/confirm-password/"}}}}], "responses": {"204": {"description": "Confirmation email has been sent"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "404": {"description": "- 102: Player not found\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/customer/password/confirm": {"post": {"security": [{"apiKey": []}], "tags": ["Customer"], "summary": "Set new password", "description": "Set new password after email by token", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["password", "guid"], "properties": {"password": {"type": "string", "format": "password", "description": "new password is longer than or equal 8 letters and contains at least one letter, one uppercase letter and one digit", "example": "19Letters&4Numbers&3Signs!"}, "guid": {"type": "string", "description": "guid from confirmation email", "example": "263480bd-ca9b-4f5a-a713-f7a024c3c7db"}}}}], "responses": {"204": {"description": "Password has been changed"}, "400": {"description": "- 43: Provided password is not valid\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n- 222: Reset password already complete or something go wrong\n"}, "404": {"description": "- 102: Player not found\n- 221: Reset password link is expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/token/status": {"get": {"security": [{"apiKey": []}], "tags": ["Health"], "summary": "Verifies token", "description": "Method verifies x-site-token from request header", "responses": {"200": {"description": "Empty response with status 200 means that token is OK"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "Returned in case we have error on the server side\n- 227: Site token does not exist\n"}}}}, "/customer/code-is-used/{playerCode}": {"get": {"tags": ["Customer"], "security": [{"apiKey": []}], "summary": "Checks if player code has been already taken", "description": "Checks if player code has been already taken", "parameters": [{"$ref": "#/parameters/playerCode"}], "responses": {"204": {"description": "Player code is not in use"}, "401": {"description": "- 10: Access Token is missing\n - 204: Access token error\n- 205: Access Token has expired\n"}, "409": {"description": "- 750: Player code is in use\n"}}}}, "/customer/mail-is-used/{email}": {"get": {"tags": ["Customer"], "security": [{"apiKey": []}], "summary": "Checks if player email has been already taken", "description": "Checks if player email has been already taken", "parameters": [{"$ref": "#/parameters/email"}], "responses": {"204": {"description": "Player email is not in use"}, "400": {"description": "- 41: The Email Address is in an invalid format\n"}, "401": {"description": "- 10: Access Token is missing\n - 204: Access token error\n- 205: Access Token has expired\n"}, "409": {"description": "- 199: Email is already used\n"}}}}, "/jurisdictions": {"get": {"security": [{"apiKey": []}], "tags": ["Juris<PERSON>"], "summary": "Gets list of jurisdictions", "responses": {"200": {"description": "List of jurisdictions", "schema": {"type": "array", "items": {"$ref": "#/definitions/JurisdictionInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/jurisdictions/{jurisdictionId}": {"parameters": [{"$ref": "#/parameters/jurisdictionId"}], "get": {"security": [{"apiKey": []}], "tags": ["Juris<PERSON>"], "summary": "Gets a jurisdiction by id", "responses": {"200": {"description": "Jurisdiction found and returned", "schema": {"$ref": "#/definitions/JurisdictionInfo"}}, "400": {"description": "- 40: Validation error"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Checks server health", "responses": {"200": {"description": "Health check OK"}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns verion, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}}, "definitions": {"Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 719}, "message": {"type": "string", "description": "error message", "example": "'15 March 44 BC' is not valid date of birth"}}}, "Jurisdiction": {"type": "object", "properties": {"title": {"type": "string", "description": "Jurisdiction title", "example": "Juris<PERSON>"}, "code": {"type": "string", "description": "Jurisdiction code", "example": "GIB"}, "description": {"type": "string", "description": "GIB Jurisdiction description", "example": "Some description"}, "settings": {"type": "object", "description": "Settings of jurisdiction", "example": {"showRTP": true, "rulesDateStamped": true, "autoPlayUntilFeatureEnabled": true, "autoPlayLimitEnabled": true, "autoPlayLossLimitEnabled": true, "autoPlayLossLimitDefaultValue": 500, "autoPlaySingleWinLimitEnabled": true, "autoPlaySingleWinLimitDefaultValue": 1000, "stopAutoPlayOnJP": true, "showClockOnMobile": true, "fastPlay": true, "turboPlus": true, "turbo": true, "jpWinPushNotificationEnabled": false, "saveTurboModeState": false}}}}, "JurisdictionInfo": {"allOf": [{"$ref": "#/definitions/Jurisdiction"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Jurisdiction public id", "example": "pQ3513OE"}, "createdUser": {"type": "string", "description": "Public id of a user who created this jurisdiction", "example": "aZQ900OE"}, "updatedUser": {"type": "string", "description": "Public id of a user who updated this jurisdiction", "example": "aZQ900OE"}, "createdDate": {"type": "string", "description": "Jurisdiction creation date", "example": "2017-12-23T16:00:00.000Z"}, "updatedDate": {"type": "string", "description": "Jurisdiction update date", "example": "2017-12-23T16:00:00.000Z"}}}]}, "GameInfo": {"type": "object", "required": ["code", "defaultInfo", "info"], "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"type": "object", "description": "game info by locale", "additionalProperties": {"$ref": "#/definitions/GameDescription"}}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "labels": {"type": "array", "items": {"$ref": "#/definitions/LabelInfo"}}, "providerCode": {"type": "string", "description": "Provider code", "example": "PR"}, "providerTitle": {"type": "string", "description": "Provider title", "example": "Provider 1"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "normal", "enum": ["normal", "suspended"]}, "settings": {"type": "object", "description": "game settings - any key-value pairs"}, "royalties": {"type": "number", "description": "Royalties for an entity game", "example": 0.15}, "releaseDate": {"type": "string", "description": "date when game was added into system", "example": "2018-08-22T12:28:51.382Z"}, "jackpots": {"$ref": "#/definitions/GameJackpots"}, "live": {"$ref": "#/definitions/GameLive"}}}, "GameDescription": {"type": "object", "description": "game info", "properties": {"name": {"type": "string", "description": "game name", "example": "Slot Name"}, "description": {"type": "string", "description": "game description", "example": "Slot description"}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}}}, "LimitsByCurrencyCode": {"type": "object", "example": {"USD": {"maxTotalStake": 2000, "stakeAll": [1, 2, 3, 5], "stakeDef": 1, "stakeMax": 100, "stakeMin": 1, "winMax": 200}, "CNY": {"maxTotalStake": 3000, "stakeAll": [2, 3, 5, 10], "stakeDef": 2, "stakeMax": 200, "stakeMin": 2, "winMax": 400}}}, "LabelInfo": {"type": "object", "required": ["name", "group"], "properties": {"id": {"type": "string", "description": "public id of label", "example": "Hjh7As0M"}, "name": {"type": "string", "description": "Label name", "example": "html5"}, "group": {"type": "string", "description": "Label group: category, type or feature", "example": "type"}}}, "Languages": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Language name", "example": "English"}, "nativeName": {"type": "string", "description": "Native name.", "example": "English"}, "direction": {"type": "string", "description": "Language direction. Possible values: ltr (Left-to-right text), rtl (Right-to-left text).", "example": "ltr", "enum": ["ltr", "rtl"]}, "code": {"type": "string", "description": "[ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1) language code.", "example": "en"}}}}, "Countries": {"type": "array", "items": {"type": "object", "properties": {"displayName": {"type": "string", "example": "United States"}, "code": {"type": "string", "description": "[ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) country code.", "example": "US"}}}}, "Currency": {"type": "object", "properties": {"displayName": {"type": "string", "example": "US dollar"}, "code": {"type": "string", "description": "[ISO 4217](http://en.wikipedia.org/wiki/ISO_4217) currency code.", "example": "USD"}}}, "GameCategoryInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "Game category public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Game category name", "example": "Some category"}, "description": {"type": "string", "description": "Game category description", "example": "Some category description"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "items": {"type": "array", "description": "Game Category filter of games - gameCodes, labels, providers, intersections", "items": {"$ref": "#/definitions/GameCategoryItem"}}}}, "GameCategoryShortInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "Game category public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Game category name", "example": "Some category"}, "type": {"type": "string", "description": "Type of game category", "enum": ["general", "gamestore"]}, "description": {"type": "string", "description": "Game category description", "example": "Some category description"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "items": {"type": "array", "description": "Game Category filter of games - gameCodes, labels, providers, intersections", "items": {"$ref": "#/definitions/GameCategoryItem"}}}}, "GameCategoryItem": {"type": "object", "required": ["type"], "properties": {"id": {"type": "string", "description": "Object publicId or code", "example": "sw_fufish"}, "type": {"type": "string", "description": "Type of object - game, provider, label, intersection", "example": "game"}}}, "GameProviderShortInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "game provider public id", "example": "jJr70Mq1"}, "code": {"type": "string", "description": "game provider code", "example": "provider1"}, "title": {"type": "string", "description": "game provider title", "example": "Provider 1"}, "status": {"type": "string", "description": "game provider status (normal/suspended)", "enum": ["normal", "suspended"]}, "isTest": {"type": "boolean", "description": "is game provider test", "enum": [true, false]}}}, "RegisterPlayerData": {"type": "object", "required": ["code", "password"], "properties": {"code": {"type": "string", "description": "customer code", "example": "C09PL00545B"}, "firstName": {"type": "string", "description": "customer first name", "example": "<PERSON><PERSON><PERSON>"}, "lastName": {"type": "string", "description": "customer last name", "example": "Lund<PERSON>g"}, "email": {"type": "string", "description": "customer email", "example": "<EMAIL>"}, "country": {"type": "string", "description": "customer country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "customer currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "language": {"type": "string", "description": "customer language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "password": {"type": "string", "format": "password", "description": "password is longer than or equal 8 letters and contains at least one letter, one uppercase letter and one digit", "minLength": 8, "example": "Big!Secret01"}, "customData": {"type": "object", "description": "custom customer's data", "example": [{"key": "alias", "keyName": "Inner name", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}, "gameGroup": {"type": "string", "description": "game group name", "example": "VIP-1"}, "agentDomain": {"type": "string", "description": "Domain address for agent's domen", "example": "example.com"}}}, "GameJackpots": {"type": "object", "description": "jackports of game", "additionalProperties": {"type": "object", "description": "jackpot info for game", "properties": {"currency": {"type": "string", "example": "USD"}, "id": {"type": "string", "example": "sw-jpgame"}, "pools": {"type": "object", "additionalProperties": {"type": "object", "properties": {"amount": {"type": "number", "example": 120}}}}}}, "example": {"sw-jpgame": {"currency": "USD", "id": "sw-jpgame", "pools": {"pool0": {"amount": 122}}}}}, "GameLive": {"type": "object", "description": "live info for game", "properties": {"id": {"type": "string", "description": "table id", "example": "mock-0-1"}, "provider": {"type": "string", "description": "table provider", "example": "mock"}, "dealer": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON>"}, "picture": {"type": "string", "example": "http://picture.com/mock.jpeg"}}}, "status": {"type": "string", "description": "table status", "example": "online"}, "type": {"type": "number", "description": "table game type", "example": 0}}, "example": {"id": "mock-0-1", "provider": "mock", "dealer": {"name": "<PERSON><PERSON>", "picture": "http://picture.com/mock.jpeg"}, "status": "online", "type": 0}}, "PlayerInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "customer code", "example": "Monija009AD01"}, "id": {"type": "string", "description": "public id of customer", "example": "481eAS0d"}, "status": {"type": "string", "description": "customer status (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "firstName": {"type": "string", "description": "customer first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "customer last name", "example": "Dow"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "country": {"type": "string", "description": "country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "language": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "customData": {"type": "object", "description": "custom customer's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "isTest": {"type": "boolean", "description": "is customer created only for testing", "example": false}, "lastLogin": {"type": "string", "description": "The last time a user logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "agentTitle": {"type": "string", "description": "agent's title", "example": "brother<PERSON>ite"}, "agentDomain": {"type": "string", "description": "agent's domain", "example": "example.com"}, "comments": {"type": "string", "description": "Customers are be able to same any text information there", "example": "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."}}}, "LoginPlayerInfo": {"type": "object", "required": ["code", "token"], "properties": {"code": {"type": "string", "description": "code of customer", "example": "Pl0039SrvInd04VIP02"}, "token": {"type": "string", "format": "byte", "description": "access token", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "isPasswordTemp": {"type": "boolean", "description": "indicate whether password is temporary. If no could be omitted", "example": true}}}, "PlayerGameURLInfo": {"type": "object", "required": ["url", "token"], "properties": {"url": {"type": "string", "description": "game URL for specific player", "example": "http://super_game.com/"}, "token": {"type": "string", "format": "byte", "description": "player token to access game", "example": "oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6uqB56WDPhPGdS3aGnQ"}}}}, "parameters": {"includeGamesAmount": {"name": "includeGamesAmount", "in": "query", "description": "Include games count available for entity in game category", "required": false, "type": "boolean"}, "includeGames": {"name": "includeGames", "in": "query", "description": "Include games available for entity in game category", "required": false, "type": "boolean"}, "gameCategoryType": {"name": "type", "in": "query", "description": "Game category type, by default general is used", "required": false, "type": "string", "enum": ["general", "gamestore"]}, "isTest": {"in": "query", "name": "isTest", "type": "boolean", "required": false, "description": "is test entry", "enum": [true, false]}, "jurisdictionId": {"name": "jurisdictionId", "in": "path", "description": "Jurisdiction public id", "required": true, "type": "string"}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "jackpots": {"in": "query", "name": "jackpots", "description": "Append jackpots info to game info", "required": false, "type": "boolean"}, "includeLive": {"in": "query", "name": "includeLive", "description": "Append live info to game info", "required": false, "type": "boolean"}, "isFreebetSupported": {"in": "query", "name": "isFreebetSupported", "description": "true if searched games should support freebets", "required": false, "type": "boolean"}, "isBonusCoinsSupported": {"in": "query", "name": "isBonusCoinsSupported", "description": "true if searched games should support bonus coins", "required": false, "type": "boolean"}, "transferEnabled": {"in": "query", "name": "transferEnabled", "description": "true if searched games should support transfer", "required": false, "type": "boolean"}, "isGRCGame": {"in": "query", "name": "isGRCGame", "description": "true if searched games should be GRC", "required": false, "type": "boolean"}, "jackpotTypes": {"in": "query", "name": "jackpotTypes", "description": "'true' or comma-separated list of jackpot types if searched games should have jackpots", "required": false, "type": "string"}, "live": {"in": "query", "name": "live", "description": "'true' or json string of live parameters if searched games should have live streaming", "required": false, "type": "string"}, "codeStrictEquality": {"in": "query", "name": "code", "description": "Code equals to value", "required": false, "type": "string"}, "titleStrictEquality": {"in": "query", "name": "title", "description": "Title equals to value", "required": false, "type": "string"}, "titleContains": {"in": "query", "name": "title__contains", "description": "Title contains string", "required": false, "type": "string"}, "titleNotContains": {"in": "query", "name": "title__contains!", "description": "Title not contains string", "required": false, "type": "string"}, "gameCategoryId": {"in": "query", "name": "gamecategoryId", "description": "Game category public id", "required": false, "type": "string"}, "gameProviderId": {"in": "query", "name": "providerId", "description": "Game provider id", "required": false, "type": "string"}, "gameProviderCodeEquality": {"in": "query", "name": "providerCode", "description": "Game provider code equals to value", "required": false, "type": "string"}, "gameProviderCode__in": {"in": "query", "name": "providerCode__in", "description": "Game provider codes separated by commas", "required": false, "type": "string"}, "gameProviderTitleEquality": {"in": "query", "name": "providerTitle", "description": "Game provider title equals to value", "required": false, "type": "string"}, "gameProviderTitleContains": {"in": "query", "name": "providerTitle__contains", "description": "Game provider title contains string", "required": false, "type": "string"}, "gameProviderTitleNotContains": {"in": "query", "name": "providerTitle__contains!", "description": "Game provider title not contains string", "required": false, "type": "string"}, "gameCategoryInPath": {"name": "gameCategoryId", "in": "path", "description": "Game category public id", "required": true, "type": "string"}, "registerPlayer": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RegisterPlayerData"}}, "gameCode": {"name": "gameCode", "in": "path", "description": "Game code", "required": true, "type": "string"}, "playerCode": {"name": "playerCode", "in": "path", "description": "Player code", "required": true, "type": "string"}, "email": {"name": "email", "in": "path", "type": "string", "description": "customer email. must be escaped before usage", "required": true}, "features": {"in": "query", "name": "features", "description": "json string of features object of searched games. Example: {\"isGRCGame\":true, \"customNewFeature\": 1, \"live\":{}, \"jackpotTypes\":[], \"transferEnabled\": false}", "required": false, "type": "string"}, "shortInfo": {"in": "query", "name": "shortInfo", "description": "true if need to return game short info (without limits)", "required": false, "type": "boolean"}, "limitsCurrencyInQuery": {"name": "currency", "in": "query", "type": "string", "description": "currency code for fetching only this currency limits", "required": false}}}