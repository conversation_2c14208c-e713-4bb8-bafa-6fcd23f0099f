import { Customer, Merchant } from "../../models/merchant";
import {
    TransactionType,
    OperatorBetWinRequest,
    OperatorBetWinResponse,
    OperatorRollbackRequest,
    OperatorBalanceRequest,
    OperatorBalanceResponse,
    OperatorRollbackResponse,
    OperatorFreeSpinsFinishRequest
} from "./entities";
import * as SoftSwissErrors from "./errors";
import * as Transaction from "../../models/transaction";
import { WALLET_ACTION } from "../../models/transaction";
import { throwCustomError } from "../../service/customError";

export class SoftSwissService {

    constructor(private merchant: Merchant, private customer: Customer) {
    }

    private getTransaction(type: string) {
        return Transaction.getById(type, WALLET_ACTION.debit) ||
            Transaction.getById(type, WALLET_ACTION.credit) ||
            Transaction.getById(type, WALLET_ACTION.rollback);
    }

    private checkTransactionsAndSetNewBalance(
        walletAction: WALLET_ACTION,
        type: string,
        amount: number,
        customer: Customer): { trx_id: string, ext_trx_id: string, balance: number } {
        const transaction = this.getTransaction(type);
        if (transaction) {
            throw new SoftSwissErrors.BadRequestError("duplicate id_provider");
        }
        const [trx_id, , ext_trx_id] = Transaction.setById(
            type,
            walletAction,
            Math.abs(amount),
            customer.cust_id
        );
        const balance = customer.balance.amount + Math.abs(amount) * walletAction;
        customer.balance.amount = balance;

        return {
            trx_id,
            ext_trx_id,
            balance
        };
    }

    @throwCustomError({ action: TransactionType.ROLLBACK })
    public async rollback(requestData: OperatorRollbackRequest): Promise<OperatorRollbackResponse> {

        const { type, original_id_provider, id_provider } = requestData.transactions[0];

        const transaction = this.getTransaction(type);
        if (transaction) {
            return this.getRollbackResponseWithEmptyTransaction(this.customer, requestData.round_id_provider);
        }

        const oldTransaction = Transaction.getById(original_id_provider, WALLET_ACTION.debit) ||
            Transaction.getById(type, WALLET_ACTION.credit);
        if (!oldTransaction) {
            return this.getRollbackResponseWithEmptyTransaction(this.customer, requestData.round_id_provider);
        }

        const [ , , ext_trx_id] = Transaction.setById(type, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        const amount = oldTransaction[1];
        const balance = this.customer.balance.amount + amount * -1;
        this.customer.balance.amount = balance;

        return {
            balance,
            round_id: requestData.round_id_provider,
            transactions: [
                {
                    id: ext_trx_id,
                    id_provider: id_provider
                }
            ]
        };
    }

    protected getRollbackResponseWithEmptyTransaction(customer: Customer, round_id: string) {
        return {
            round_id,
            balance: customer.balance.amount,
            transactions: [
                {
                    id: "1",
                    id_provider: "", // if rollback bet is not processed, operator can send empty tx_id
                }
            ]
        };
    }

    @throwCustomError({ action: TransactionType.BALANCE })
    public async balance(requestData: OperatorBalanceRequest): Promise<OperatorBalanceResponse> {
        return { balance: this.customer.balance.amount };
    }

    public async betWin(requestData: OperatorBetWinRequest): Promise<OperatorBetWinResponse> {
        const balance = this.customer.balance.amount;
        const isDebit = requestData.transactions[0].type === TransactionType.BET;
        if (isDebit) {
            return this.bet(requestData, this.customer, balance);
        }
        return this.win(requestData, this.customer, balance);
    }

    @throwCustomError({ action: TransactionType.BET })
    private async bet(requestData: OperatorBetWinRequest, customer: Customer, balance: number) {
        return requestData.transactions.reduce((responseData, { type, amount, id_provider }) => {
            const isDebit = type === TransactionType.BET;
            if (isDebit && customer.balance.amount < amount) {
                throw new SoftSwissErrors.NotEnoughFundsError("Player has not enough funds to process an action");
            }
            // tslint:disable-next-line:no-shadowed-variable
            const { trx_id, ext_trx_id, balance } = this.checkTransactionsAndSetNewBalance(
                isDebit ? WALLET_ACTION.debit : WALLET_ACTION.credit,
                id_provider,
                amount,
                customer);

            return {
                ...responseData,
                balance,
                transactions: [
                    ...responseData.transactions,
                    {
                       id: ext_trx_id,
                       id_provider: trx_id
                    }
                ]
            };
        }, {
            balance,
            round_id: requestData.round_id,
            transactions: []
        });
    }

    @throwCustomError({ action: TransactionType.WIN })
    private async win(requestData: OperatorBetWinRequest, customer: Customer, balance: number) {
        return requestData.transactions.reduce((responseData, { type, amount, id_provider }) => {
            const isDebit = type === TransactionType.BET;
            if (isDebit && customer.balance.amount < amount) {
                throw new SoftSwissErrors.NotEnoughFundsError("Player has not enough funds to process an action");
            }
            // tslint:disable-next-line:no-shadowed-variable
            const { trx_id, ext_trx_id, balance } = this.checkTransactionsAndSetNewBalance(
                isDebit ? WALLET_ACTION.debit : WALLET_ACTION.credit,
                id_provider,
                amount,
                customer);

            return {
                ...responseData,
                balance,
                transactions: [
                    ...responseData.transactions,
                    {
                        id: ext_trx_id,
                        id_provider: trx_id,
                    }
                ]
            };
        }, {
            balance,
            round_id: requestData.round_id,
            transactions: []
        });
    }

    @throwCustomError({ action: TransactionType.FREESPINS_FINISH })
    public async freeSpinsFinish(request: OperatorFreeSpinsFinishRequest): Promise<{ balance: number }> {
        this.customer.balance.amount = this.customer.balance.amount + request.amount;

        return {
            balance: this.customer.balance.amount,
        };
    }
}
