import { BaseErrorToLog, ErrorInfoToLog } from "../../errors";
import { EVERY_MATRIX_JURISDICTION } from "./entities";

export enum EVERY_MATRIX_STATUS {
    OK =  "Ok",
    Failed = "Failed"
}

export enum EveryMatrixErrorCode {
    UNKNOWN_ERROR = 101,
    TOKEN_NOT_FOUND = 102,
    USER_IS_BLOCKED = 103,
    INSUFFICIENT_FUNDS = 105,
    VENDOR_ACCOUNT_NOT_ACTIVE = 106,
    IP_IS_NOT_ALLOWED = 107,
    CURRENCY_DOES_NOT_MATCH = 108,
    TRANSACTION_NOT_FOUND = 109,
    DOUBLE_TRANSACTION = 110,
    INVALID_HASH = 111,
    CASINO_LOSS_LIMIT = 112,
    // mock custom errors (not EveryMatrix)
    OPERATOR_NOT_SET = 777,
    OPERATOR_NOT_FOUND = 778,
    SESSION_EXPIRED = 779,
    JURISDICTION_NOT_SET = 780,
    JURISDICTION_INVALID = 781,
}

export enum EVERY_MATRIX_ERROR_DESCRIPTION {
    UNKNOWN_ERROR = "UnknownError",
    TOKEN_NOT_FOUND = "TokenNotFound",
    USER_IS_BLOCKED = "UserIsBlocked",
    INSUFFICIENT_FUNDS = "InsufficientFunds",
    VENDOR_ACCOUNT_NOT_ACTIVE = "VendorAccountNotActive",
    IP_IS_NOT_ALLOWED = "IPIsNotAllowed",
    CURRENCY_DOES_NOT_MATCH = "CurrencyDoesNotMatch",
    TRANSACTION_NOT_FOUND = "TransactionNotFound",
    DOUBLE_TRANSACTION = "DoubleTransaction",
    INVALID_HASH = "InvalidHash",
    CASINO_LOSS_LIMIT = "CasinoLossLimit",
    OPERATOR_NOT_FOUND
        = "OperatorNotFound. Merchant not found by OperatorId (merchantCode). Create merchant before use.",
    OPERATOR_NOT_SET = "OperatorNotSet. OperatorId not set (merchantCode)",
    SESSION_EXPIRED = "SESSION_EXPIRED",
}

export enum EVERY_MATRIX_ERROR_LOG_ID {
    UNKNOWN_ERROR = "34t495t749_UnknownError",
    TOKEN_NOT_FOUND = "34t495t749_TokenNotFound",
    USER_IS_BLOCKED = "34t495t749_UserIsBlocked",
    INSUFFICIENT_FUNDS = "34t495t749_InsufficientFunds",
    VENDOR_ACCOUNT_NOT_ACTIVE = "34t495t749_VendorAccountNotActive",
    IP_IS_NOT_ALLOWED = "34t495t749_IPIsNotAllowed",
    CURRENCY_DOES_NOT_MATCH = "34t495t749_CurrencyDoesNotMatch",
    TRANSACTION_NOT_FOUND = "34t495t749_TransactionNotFound",
    DOUBLE_TRANSACTION = "34t495t749_DoubleTransaction",
    INVALID_HASH = "34t495t749_InvalidHash",
    CASINO_LOSS_LIMIT = "34t495t749_CasinoLossLimit",
    OPERATOR_NOT_FOUND = "34t495t749_OperatorNotFound",
    OPERATOR_NOT_SET = "34t495t749_OperatorNotSet",
    JURISDICTION_NOT_SET = "34t495t749_JurisdictionNotSet",
    JURISDICTION_INVALID = "34t495t749_JurisdictionInvalid",
}

export interface IEveryMatrixError extends BaseErrorToLog {
    responseStatus: number;
    Status: string;
    ErrorCode: number;
    ErrorDescription: string;
    LogId: string;
}

export class EveryMatrixError extends Error implements IEveryMatrixError {
    constructor(status: string,
                errorCode: number,
                errorDescription: string,
                longId: string,
                responseStatus: number = 200,
                customField: string = null) {
        super(errorDescription);

        this.Status = status;
        this.ErrorCode = errorCode;
        this.ErrorDescription = errorDescription;
        this.LogId = longId;
        this.responseStatus = responseStatus;
        this.CustomField = customField;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.ErrorCode,
                message: this.ErrorDescription,
                stack: this.stack
            }
        };
    }

    public responseStatus: number;
    public Status: string;
    public ErrorCode: number;
    public ErrorDescription: string;
    public LogId: string;
    public CustomField: string;
}

/**
 * MOCK Custom
 */

export class OperatorNotFoundError extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.OPERATOR_NOT_FOUND,
            EVERY_MATRIX_ERROR_DESCRIPTION.OPERATOR_NOT_FOUND,
            EVERY_MATRIX_ERROR_LOG_ID.OPERATOR_NOT_FOUND);
    }
}

export class OperatorNotSetError extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.OPERATOR_NOT_SET,
            EVERY_MATRIX_ERROR_DESCRIPTION.OPERATOR_NOT_SET,
            EVERY_MATRIX_ERROR_LOG_ID.OPERATOR_NOT_SET);
    }
}

export class JurisdictionNotSetError extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.JURISDICTION_NOT_SET,
            "Jurisdiction not set. please setup jurisdiction either in token" +
            "emLaunchToken{anyString}_{merchantCode}_{jurisdiction}' or in customer settings (user settings)",
            EVERY_MATRIX_ERROR_LOG_ID.JURISDICTION_NOT_SET);
    }
}

export class InvalidJurisdictionError extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.JURISDICTION_INVALID,
            "Jurisdiction is invalid. Can have values: " +
            `${EVERY_MATRIX_JURISDICTION.UK}, ${EVERY_MATRIX_JURISDICTION.MT}`,
            EVERY_MATRIX_ERROR_LOG_ID.JURISDICTION_NOT_SET);
    }
}

export class WrongTokenFormatError extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.TOKEN_NOT_FOUND,
            `Wrong token format, token must contains two '_' literals.
            "Please use right format like 'emLaunchToken{anyString}_{merchantCode}_{jurisdiction}'`,
            EVERY_MATRIX_ERROR_LOG_ID.TOKEN_NOT_FOUND);
    }
}

export class EmptyFieldError extends EveryMatrixError {
    constructor(message: string) {
        super(EVERY_MATRIX_STATUS.Failed, 400, "MockErrorEmptyField", message);
    }
}

/**
 * End of MOCK Custom
 */

/**
 * Return Codes
 */

export class UnknownError extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.UNKNOWN_ERROR,
            EVERY_MATRIX_ERROR_DESCRIPTION.UNKNOWN_ERROR,
            EVERY_MATRIX_ERROR_LOG_ID.UNKNOWN_ERROR);
    }
}

export class TokenNotFound extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.TOKEN_NOT_FOUND,
            EVERY_MATRIX_ERROR_DESCRIPTION.TOKEN_NOT_FOUND,
            EVERY_MATRIX_ERROR_LOG_ID.TOKEN_NOT_FOUND);
    }
}

export class UserIsBlocked extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.USER_IS_BLOCKED,
            EVERY_MATRIX_ERROR_DESCRIPTION.USER_IS_BLOCKED,
            EVERY_MATRIX_ERROR_LOG_ID.USER_IS_BLOCKED);
    }
}

export class InsufficientFunds extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.INSUFFICIENT_FUNDS,
            EVERY_MATRIX_ERROR_DESCRIPTION.INSUFFICIENT_FUNDS,
            EVERY_MATRIX_ERROR_LOG_ID.INSUFFICIENT_FUNDS);
    }
}

export class VendorAccountNotActive extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.VENDOR_ACCOUNT_NOT_ACTIVE,
            EVERY_MATRIX_ERROR_DESCRIPTION.VENDOR_ACCOUNT_NOT_ACTIVE,
            EVERY_MATRIX_ERROR_LOG_ID.VENDOR_ACCOUNT_NOT_ACTIVE);
    }
}

export class IPIsNotAllowed extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.IP_IS_NOT_ALLOWED,
            EVERY_MATRIX_ERROR_DESCRIPTION.IP_IS_NOT_ALLOWED,
            EVERY_MATRIX_ERROR_LOG_ID.IP_IS_NOT_ALLOWED);
    }
}

export class CurrencyDoesNotMatch extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.CURRENCY_DOES_NOT_MATCH,
            EVERY_MATRIX_ERROR_DESCRIPTION.CURRENCY_DOES_NOT_MATCH,
            EVERY_MATRIX_ERROR_LOG_ID.CURRENCY_DOES_NOT_MATCH);
    }
}

export class TransactionNotFound extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.TRANSACTION_NOT_FOUND,
            EVERY_MATRIX_ERROR_DESCRIPTION.TRANSACTION_NOT_FOUND,
            EVERY_MATRIX_ERROR_LOG_ID.TRANSACTION_NOT_FOUND);
    }
}

export class DoubleTransaction extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.DOUBLE_TRANSACTION,
            EVERY_MATRIX_ERROR_DESCRIPTION.DOUBLE_TRANSACTION,
            EVERY_MATRIX_ERROR_LOG_ID.DOUBLE_TRANSACTION);
    }
}

export class InvalidHash extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.INVALID_HASH,
            EVERY_MATRIX_ERROR_DESCRIPTION.INVALID_HASH,
            EVERY_MATRIX_ERROR_LOG_ID.INVALID_HASH);
    }
}

export class CasinoLossLimit extends EveryMatrixError {
    constructor() {
        super(EVERY_MATRIX_STATUS.Failed,
            EveryMatrixErrorCode.CASINO_LOSS_LIMIT,
            EVERY_MATRIX_ERROR_DESCRIPTION.CASINO_LOSS_LIMIT,
            EVERY_MATRIX_ERROR_LOG_ID.CASINO_LOSS_LIMIT);
    }
}

/**
 * End of Return Codes
 */

export function getEveryMatrixErrorInstance(code: number) {
    switch (code) {
        case EveryMatrixErrorCode.UNKNOWN_ERROR:
            return new UnknownError();
        case EveryMatrixErrorCode.TOKEN_NOT_FOUND:
            return new TokenNotFound();
        case EveryMatrixErrorCode.USER_IS_BLOCKED:
            return new UserIsBlocked();
        case EveryMatrixErrorCode.INSUFFICIENT_FUNDS:
            return new InsufficientFunds();
        case EveryMatrixErrorCode.VENDOR_ACCOUNT_NOT_ACTIVE:
            return new VendorAccountNotActive();
        case EveryMatrixErrorCode.IP_IS_NOT_ALLOWED:
            return new IPIsNotAllowed();
        case EveryMatrixErrorCode.CURRENCY_DOES_NOT_MATCH:
            return new CurrencyDoesNotMatch();
        case EveryMatrixErrorCode.TRANSACTION_NOT_FOUND:
            return new TransactionNotFound();
        case EveryMatrixErrorCode.DOUBLE_TRANSACTION:
            return new DoubleTransaction();
        case EveryMatrixErrorCode.INVALID_HASH:
            return new InvalidHash();
        case EveryMatrixErrorCode.CASINO_LOSS_LIMIT:
            return new CasinoLossLimit();

        default:
            throw new Error("Unknown EveryMatrix error code");
    }
}
