import { getApplication } from "../../skywind/serverGVC";
import {
    GameRoundState,
    GVCAuthenticateUserRequest,
    GVCFundsTransferRequest,
    GVCPlayerBalanceRequest,
    GVCVoidTransactionRequest,
    SKYWIND_PARTNER_ID,
    GVC_TRANSACTION_TYPE
} from "../../skywind/entities/gvc";
import { getDefaultCustomer } from "../../skywind/service/customer";
import { createCustomerSession } from "../../skywind/service/session";

import { Customer, Merchant, setAll } from "../../skywind/models/merchant";
import * as Session from "../../skywind/models/session";
import * as TransactionModel from "../../skywind/models/transaction";
import { createActionError, RaiseType } from "../../skywind/models/customError";
import { createActionData } from "../../skywind/models/extraData";

export class GVCBaseSpec {
    public urlPathToGetBalance: string = "/api/casino/player/balance";
    public urlPathToMakePayment: string = "/api/casino/player/funds";
    public urlPathToRollback: string = "/api/casino/player/transaction";

    public server;
    public merchantCode = "gvcMerchant";
    public playerCode = "gvcPlayer";

    public sessionId: string;
    public merchant: Merchant;
    public customer: Customer;

    public authRequest: GVCAuthenticateUserRequest;
    public balanceRequest: GVCPlayerBalanceRequest;
    public debitRequest: GVCFundsTransferRequest;
    public creditRequest: GVCFundsTransferRequest;
    public rollbackRequest: GVCVoidTransactionRequest;

    public trxId: string;
    public creditTrxId: string;
    public rollbackTrxId: string;

    public ticketExpirationTimeDefault: number = 5 * 60 * 1000; // 5 min

    public async before() {
        this.server = await getApplication();
        Session.clearAll();
        TransactionModel.setAll({});

        const merchant: Merchant = {
            merch_id: this.merchantCode,
            merch_pwd: "test",
            multiple_session: true,
            customers: {}
        };
        setAll({ [this.merchantCode]: merchant });

        this.merchant = merchant;
        this.customer = this.addPlayerToMerchant(this.playerCode);

        this.sessionId = createCustomerSession(merchant, this.customer);
        this.trxId = "PQ+OAAAChlsAAALXPQ+OAJH5+v_bet";
        this.creditTrxId = "PQ+OAAAChlsAAALXPQ+OAJH5+v_win";
        this.rollbackTrxId = "PQ+OAAAChlsAAALXPQ+OAJH5+v";

        this.authRequest = {
            partnerId: SKYWIND_PARTNER_ID,
            accountId: `${this.merchantCode}__${this.playerCode}`, // mock determines merchants and players
            gameContext: "GAMECONTEXT001",
            currencyCode: "USD",
            secureToken: "default",
            gameCode: "GAMECODE001",
            gameSessionId: "GAMESESSION001",
            clientChannel: "CLIENTCH001",
        };
        this.balanceRequest = {
            ...this.authRequest,
        };
        this.debitRequest = {
            ...this.authRequest,
            gameRoundState: GameRoundState.INPROGRESS,
            gameRoundId: "123",
            transactionId: "PQ+OKOAAChlsAKALXPQ+OA=_bet",
            transactionDetails: [ // minor units
                { amount: 100, transactionType: GVC_TRANSACTION_TYPE.BET },
                { amount: 200, transactionType: GVC_TRANSACTION_TYPE.BET },
                { amount: 300 } // wrong transaction should be skipped
            ],
        };
        this.creditRequest = {
            ...this.authRequest,
            gameRoundState: GameRoundState.INPROGRESS,
            gameRoundId: "123",
            transactionId: "PQ+OKOAAChlsAKALXPQ+OA=_win",
            transactionDetails: [ // minor units
                { amount: 500, transactionType: GVC_TRANSACTION_TYPE.WIN },
                { amount: 300 } // wrong transaction should be skipped
            ],
        };
        this.rollbackRequest = {
            partnerId: SKYWIND_PARTNER_ID,
            accountId: `${this.merchantCode}__${this.playerCode}`,
            transactionId: "PQ+OKOAAChlsAKALXPQ+OA=",
            cancelledTransactionId: "PQ+OKOAAChlsAKALXPQ+OA=_bet", // bet transaction id that should be cancelled
            gameSessionId: "GAMESESSION001",
            reason: "Internal error",
            clientChannel: "CLIENTCH001",
        };
    }

    public addPlayerToMerchant(name: string): Customer {
        const customer = getDefaultCustomer(name, "USD", true, 100);
        this.merchant.customers[customer.cust_id] = customer;

        return customer;
    }

    public createCustomError(httpStatus: number, errorCode: number, action: string) {
        createActionError(this.merchantCode, this.playerCode, action, RaiseType.AFTER, {
            http_response_status: httpStatus,
            errorCode: errorCode
        });
    }

    public createExtraData(data: object, action: string) {
        createActionData(this.merchantCode, this.playerCode, action, data);
    }
}
