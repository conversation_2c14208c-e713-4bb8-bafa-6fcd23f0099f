import { Sequelize, Transaction, Options, PoolOptions } from "sequelize";
import config from "../config";
import * as pg from "pg";
import { lazy } from "@skywind-group/sw-utils";
import logger from "../utils/logger";

const log = logger("sequelize");

pg.types.setTypeParser(1114, stringValue => {
    return new Date(stringValue + "+0000");
});

const DB_DIALECT = "postgres";

function constructOptions(dbConfig): Options {
    const dbPool: PoolOptions = {

        /**
         * Maximum connections of the pool
         */
        max: dbConfig.maxConnections,

        /**
         * The maximum time, in milliseconds, that a connection can be idle before being released.
         */
        idle: dbConfig.maxIdleTime,

    };
    const dbOptions: Options = {

        /**
         * The dialect of the database you are connecting to. One of mysql, postgres, sqlite, mariadb and mssql.
         */
        dialect: DB_DIALECT,

        /**
         * Default isolation level
         */
        isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,

        /**
         * The dialect specific options
         */
        dialectOptions: {},

        /**
         * The host of the relational database.
         */
        host: dbConfig.host,

        /**
         * The port of the relational database.
         */
        port: dbConfig.port,

        /**
         * Connection pool options
         */
        pool: dbPool,

        logging: config.queryLogging ? (args) => log.info(args) : () => {}, //tslint:disable-line

        define: {
            schema: dbConfig.schema,
        }

    };

    if (config.db.ssl.isEnabled) {
        dbOptions.dialectOptions["ssl"] = config.db.ssl;
    }

    return dbOptions;
}

export const sequelize = lazy(() => new Sequelize(
    config.db.database, config.db.user, config.db.password, constructOptions(config.db)
));
