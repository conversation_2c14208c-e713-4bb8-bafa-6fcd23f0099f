import { NextFunction, Request, Response, Router } from "express";
import {
    GANExtraRequestData,
    GANRequest,
    GANErrorResponse,
    GAN_RESPONSE_STATUS
} from "../../entities/gan";
import * as Merchants from "../../models/merchant";
import { Merchant, Customer } from "../../models/merchant";
import { Ticket } from "../../models/ticket";
import { getGANTicket } from "../../service/ticket";
import config from "../../config";
import * as GANErrors from "../../errorsGAN";
import * as parser from "../../utils/xmlParser";

type CustomFunction = (req: Request, res: Response, next: NextFunction) => void;
export type ControllersToBeWrapped = CustomFunction[] | CustomFunction;

export function wrapApiControllers(controllers: ControllersToBeWrapped): Array<any> {
    if (!controllers || !controllers.length) {
        return;
    }
    controllers = Array.isArray(controllers) ? controllers : [controllers];

    return controllers.map(controller =>
        async (req: Request, res: Response, next: NextFunction) => {
            try {
                await controller(req, res, next);
            } catch (error) {
                next(error);
            }
        });
}

export const authenticate = (req: Request & GANExtraRequestData, res: Response, next: NextFunction) => {
    const requestBody = req.parsedRequest as GANRequest;
    let merchantId: string;
    let playerId: string;

    if (requestBody.Auth._token) {
        req.tokenData = getGANTicket(requestBody.Auth._token, config.expirationTime.ticket) as Ticket;
        merchantId = req.tokenData.merchantId;
        playerId = req.tokenData.custId;

    } else {
        ([ merchantId, playerId ] = getMerchantAndCustomerIdsFromChannelRef(requestBody.Channel._ref));
    }

    const merchant: Merchant = Merchants.getById(merchantId, false);
    if (!merchant) {
        throw new GANErrors.InvalidCredentials();
    }

    const customer: Customer = merchant.customers[playerId];
    if (!customer) {
        throw new GANErrors.InvalidCredentials();
    }

    req.merchant = merchant;
    req.customer = customer;

    next();
};

export function getMerchantAndCustomerIdsFromChannelRef(contestRef: string): string[] {
    return contestRef.split("-");
}

export function validate(withExtra?: (requestBody: any) => any) {
    return (req: Request & GANExtraRequestData, res: Response, next: NextFunction) => {
        const requestBody = req.parsedRequest as GANRequest;

        if (!requestBody.Auth) {
            throw new GANErrors.MissingAuthElement();
        }
        if (!requestBody.Auth._user) {
            throw new GANErrors.MissingAuthUser();
        }
        if (!requestBody.Auth._secret) {
            throw new GANErrors.MissingAuthSecret();
        }
        if (!requestBody.Channel) {
            throw new GANErrors.MissingChannelElement();
        }
        if (!requestBody.Channel._ref) {
            throw new GANErrors.MissingChannelRef();
        }

        // tslint:disable-next-line:no-unused-expression
        withExtra && withExtra(requestBody);

        next();
    };
}

export function parseXml(req: Request & GANExtraRequestData, res: Response, next: NextFunction): any {
    req.parsedRequest = parser.parseXML(req.body).Request;
    next();
}

export function convertToXml(data: any): string {
    return parser.convertToXML({
        Response: data
    });
}

export function prepareGANError(error: GANErrors.GANError): string {
    const errorResponse = {
        _status: GAN_RESPONSE_STATUS.FAILURE,
        Error: {
            _code: error.code,
            _message: error.message,
        }
    } as GANErrorResponse;

    return convertToXml(errorResponse);
}
