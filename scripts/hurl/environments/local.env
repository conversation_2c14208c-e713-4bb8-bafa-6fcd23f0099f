# Local Development Environment Configuration for Hurl Tests
# This file contains environment variables for running Hurl tests against local services

# Management API Configuration
MAPI_URL=http://localhost:3000
MAPI_PASSWORD=admin123

# IMP-Mock Service Configuration
MOCK_URL=http://localhost:8000

# Terminal Service Configuration
TERMINAL_URL=http://localhost:3004

# Wallet Service Configuration
WALLET_URL=http://localhost:3005

# Game Provider Service Configuration
GAMEPROVIDER_URL=http://localhost:3006

# Game Auth Service Configuration
GAMEAUTH_URL=http://localhost:3007

# Default Game Configuration
GAME_CODE=sw_mrmnky
GAME_PROVIDER=skywind
ALTERNATIVE_GAME_CODE=sw_ss

# Currency and Locale Settings
DEFAULT_CURRENCY=USD
DEFAULT_LANGUAGE=en
DEFAULT_COUNTRY=US
DEFAULT_JURISDICTION=COM

# Test Player Configuration
TEST_PLAYER_ID=test_player_001
TEST_PLAYER_LOGIN=TESTPLAYER001
TEST_PLAYER_EMAIL=<EMAIL>
TEST_PLAYER_BALANCE=100000

# Merchant Configuration
TEST_MERCHANT_ID=seamless_test_merchant
TEST_MERCHANT_PASSWORD=SecurePassword123!
PROMO_MERCHANT_ID=promo_merchant
PROMO_MERCHANT_PASSWORD=PromoPassword123!

# Bonus and Promotional Settings
BONUS_BALANCE=25000
FREE_BET_BALANCE=10000
BET_LIMIT=1000

# Transaction Settings
DEFAULT_BET_AMOUNT=10.00
DEFAULT_WIN_AMOUNT=25.00
HIGH_BET_AMOUNT=100.00
EXCESSIVE_BET_AMOUNT=1000.00

# Session and Token Settings
SESSION_TIMEOUT=300
TOKEN_EXPIRY=3600

# Error Testing Configuration
INVALID_MERCHANT_ID=invalid_merchant
INVALID_GAME_CODE=invalid_game_code
INVALID_CURRENCY=INVALID_CURRENCY
BLOCKED_PLAYER_STATUS=blocked
INSUFFICIENT_BALANCE=0

# Network and Timeout Settings
REQUEST_TIMEOUT=30
CONNECTION_TIMEOUT=10
RETRY_COUNT=3

# Logging and Debug Settings
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true
ENABLE_RESPONSE_LOGGING=true

# Database Configuration (for reference)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=sw_management
DB_USER=postgres

# Redis Configuration (for reference)
REDIS_HOST=localhost
REDIS_PORT=6379

# Live Manager Configuration
LIVE_MANAGER_URL=http://localhost:3010
LIVE_MANAGER_PORT=3010

# Socket Configuration
SOCKET_V2_PATH=/socket-v2
SOCKET_V4_PATH=/socket-v4

# Test Data Cleanup Settings
CLEANUP_AFTER_TESTS=true
PRESERVE_TEST_DATA=false

# Performance Testing Settings
CONCURRENT_USERS=10
TEST_DURATION=60
RAMP_UP_TIME=10

# Security Settings
ENABLE_SSL=false
SSL_CERT_PATH=
SSL_KEY_PATH=
API_KEY_HEADER=x-api-key

# Monitoring and Metrics
ENABLE_METRICS=true
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health

# Feature Flags
ENABLE_BONUS_FEATURES=true
ENABLE_FREE_BET_FEATURES=true
ENABLE_MULTI_CURRENCY=true
ENABLE_JACKPOT_FEATURES=true
ENABLE_TOURNAMENT_FEATURES=false

# Regulation Settings
DEFAULT_REGULATION=default
ITALIAN_REGULATION=italian
UK_REGULATION=uk
MALTA_REGULATION=malta

# Game Provider Settings
SKYWIND_PROVIDER_CODE=skywind
ITG_PROVIDER_CODE=itg
QUICKSPIN_PROVIDER_CODE=quickspin

# Jackpot Configuration
JACKPOT_POOL_ID=SW-SUPER-LION
JACKPOT_CURRENCY=EUR
JACKPOT_MIN_BET=1.00

# Tournament Configuration
TOURNAMENT_ID=daily_tournament_001
TOURNAMENT_ENTRY_FEE=5.00
TOURNAMENT_PRIZE_POOL=1000.00

# Limits Configuration
MIN_BET_AMOUNT=0.01
MAX_BET_AMOUNT=500.00
MAX_WIN_AMOUNT=500000.00
DAILY_LOSS_LIMIT=1000.00

# Country Restrictions
RESTRICTED_COUNTRIES=US,FR,IT
ALLOWED_COUNTRIES=GB,DE,SE,NO

# IP Restrictions
ALLOWED_IP_RANGES=127.0.0.1,***********/24
BLOCKED_IP_RANGES=10.0.0.0/8

# Game Features
ENABLE_AUTOPLAY=true
ENABLE_TURBO_MODE=true
ENABLE_SOUND=true
ENABLE_FULLSCREEN=true

# Mobile Configuration
MOBILE_GAME_URL=http://localhost:4000/mobile
MOBILE_LOBBY_URL=http://localhost:4000/mobile/lobby

# Analytics Configuration
ANALYTICS_ENDPOINT=http://localhost:9000/analytics
ENABLE_PLAYER_TRACKING=true
ENABLE_GAME_ANALYTICS=true

# Cache Configuration
CACHE_TTL=3600
ENABLE_REDIS_CACHE=true
CACHE_PREFIX=sw_test_

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_ENABLED=true

# Webhook Configuration
WEBHOOK_URL=http://localhost:8080/webhooks
WEBHOOK_SECRET=webhook_secret_key
ENABLE_WEBHOOKS=false

# External Service URLs
PAYMENT_GATEWAY_URL=http://localhost:9001
FRAUD_DETECTION_URL=http://localhost:9002
KYC_SERVICE_URL=http://localhost:9003

# Test Environment Specific
TEST_MODE=true
MOCK_EXTERNAL_SERVICES=true
ENABLE_TEST_ENDPOINTS=true
BYPASS_AUTHENTICATION=false
