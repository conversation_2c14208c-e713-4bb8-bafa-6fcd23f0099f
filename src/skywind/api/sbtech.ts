import { NextFunction, Request, Response, Router } from "express";
import logger from "../utils/logger";
import { getSBTechService } from "../service/sbtech/service";
import * as responder from "../service/sbtech/responder";
import {
    SBTechInitSessionRequest,
    SBTechInitSessionResponse,
    SBTechDebitRequest,
    SBTechDebitResponse,
    SBTechCreditRequest,
    SBTechCreditResponse,
    SBTechGetBalanceRequest,
    SBTechGetBalanceResponse,
    SBTechEndRoundRequest,
    SBTechEndRoundResponse,
    SBTechRollbackTransactionRequest,
    SBTechRollbackTransactionResponse,
    SBTechExtraRequestData
} from "../entities/sbtech";
import { authenticate, wrapApiControllers } from "./config/sbtechMiddleware";
import { settings } from "../models/settings";
import { isEmpty } from "../utils/isEmpty";
import * as SBTechErrors from "../errorsSBTech";
import { INTEGRATION_TYPE } from "../../config/integrationType";
import * as Merchants from "../models/merchant";

const log = logger(`${INTEGRATION_TYPE.SBTECH}:api`);
const router: Router = Router();

router.post("/initSession",
    validateFieldsForEmpty([
        "providerKey",
        "providerPass",
        "clientToken",
        "gameId",
        "playerIp"
    ]),
    authenticate,
    notSaveAnyDataMiddleware(responder.initSession),
    wrapApiControllers(initSession));

router.post("/debit",
    validateFieldsForEmpty([
        "providerKey",
        "providerPass",
        "sessionId",
        "gameId",
        "playerId",
        "brandId",
        "roundId",
        "trxId",
        "amount",
    ]),
    authenticate,
    notSaveAnyDataMiddleware(responder.debit),
    wrapApiControllers(debit));

router.post("/credit",
    validateFieldsForEmpty([
        "providerKey",
        "providerPass",
        "sessionId",
        "gameId",
        "playerId",
        "brandId",
        "roundId",
        "trxId",
        "debitTrxId",
        "amount",
    ]),
    authenticate,
    notSaveAnyDataMiddleware(responder.credit),
    wrapApiControllers(credit));

router.post("/endRound",
    validateFieldsForEmpty([
        "providerKey",
        "providerPass",
        "sessionId",
        "gameId",
        "playerId",
        "brandId",
        "roundId",
        "trxId",
    ]),
    authenticate,
    notSaveAnyDataMiddleware(responder.endRound),
    wrapApiControllers(endRound));

router.post("/rollbackTransaction",
    validateFieldsForEmpty([
        "providerKey",
        "providerPass",
        "sessionId",
        "gameId",
        "playerId",
        "brandId",
        "roundId",
        "trxId",
        "debitTrxId"
    ]),
    authenticate,
    notSaveAnyDataMiddleware(responder.rollbackTransaction),
    wrapApiControllers(rollbackTransaction));

router.post("/getBalance",
    validateFieldsForEmpty([
        "providerKey",
        "providerPass",
        "sessionId",
        "gameId",
        "playerId",
        "brandId",
    ]),
    authenticate,
    notSaveAnyDataMiddleware(responder.getBalance),
    wrapApiControllers(getBalance));

/**
 * MEMO: API controllers
 */

async function initSession(req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SBTechInitSessionRequest;
    const response: SBTechInitSessionResponse = await getSBTechService(req.merchant, req.customer).initSession(data);

    res.send(response);
}

async function debit(req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SBTechDebitRequest;
    const response: SBTechDebitResponse = await getSBTechService(req.merchant, req.customer).debit(data);

    res.send(response);
}

async function credit(req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SBTechCreditRequest;
    const response: SBTechCreditResponse = await getSBTechService(req.merchant, req.customer).credit(data);

    res.send(response);
}

async function getBalance(req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SBTechGetBalanceRequest;
    const response: SBTechGetBalanceResponse = await getSBTechService(req.merchant, req.customer).getBalance(data);

    res.send(response);
}

async function rollbackTransaction(req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SBTechRollbackTransactionRequest;
    const response: SBTechRollbackTransactionResponse =
        await getSBTechService(req.merchant, req.customer).rollbackTransaction(data);

    res.send(response);
}

async function endRound(req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) {
    const data = req.body as SBTechEndRoundRequest;
    const response: SBTechEndRoundResponse = await getSBTechService(req.merchant, req.customer).endRound(data);

    res.send(response);
}

/**
 * MEMO: Auxiliary methods
 */

function notSaveAnyDataMiddleware(responder: (req: Request & SBTechExtraRequestData) => object) {
    return (req: Request & SBTechExtraRequestData, res: Response, next: NextFunction) => {
        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req));
            } catch (err) {
                return next(err);
            }
        }
        return next();
    };
}

function validateFieldsForEmpty(fields: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        for (const param of fields) {
            const value = req.body[param];
            if (isEmpty(value)) {
                return next(new SBTechErrors.InternalError(`[${param}] is required`));
            }
        }
        return next();
    };
}

/*function decorateResponse<T extends GVCResponseStatusData>(response: T) {
    response.status = {
        statusCode: "0",
        statusMessage: "success"
    };
}*/

export default router;
