import { Request } from "express";
import {
    FAKE_GAME_ROUND_ID,
    GVCExtraRequestData,
    GVCFundsTransferResponse,
    GVCPlayerBalanceResponse,
    GVCVerifyTokenResponse,
    GVCVoidTransactionResponse,
    SKYWIND_PARTNER_ID
} from "../entities/gvc";

export function verifyTokenResponder(req: Request & GVCExtraRequestData): GVCVerifyTokenResponse {
    return {
        partnerId: SKYWIND_PARTNER_ID,
        currencyCode: "USD",
        balance: 100,
        status: {
            statusCode: "0",
            statusMessage: "success"
        },
        renewedSecureToken: "renewed_secure_token"
    } as GVCVerifyTokenResponse;
}

export function balanceResponder(req: Request & GVCExtraRequestData): GVCPlayerBalanceResponse {
    return {
        accountId: "1",
        partnerId: SKYWIND_PARTNER_ID,
        currencyCode: "USD",
        gameCode: "GAME001",
        balance: 100,
        status: {
            statusCode: "0",
            statusMessage: "success"
        }
    } as GVCPlayerBalanceResponse;
}

export function paymentResponder(req: Request & GVCExtraRequestData): GVCFundsTransferResponse {
    return {
        accountId: "1",
        partnerId: SKYWIND_PARTNER_ID,
        currencyCode: "USD",
        gameCode: "GAME001",
        balance: 100,
        gameContext: "GAMECONTEXT001",
        bpGameRoundId: FAKE_GAME_ROUND_ID,
        bpTransactionId: "c79a65f0-3762-4e5e-8db1-4ea61a770b7a",
        transactionId: "e916b795-ccc0-4af8-ac55-247acce16ca5",
        status: {
            statusCode: "0",
            statusMessage: "success"
        }
    } as GVCFundsTransferResponse;
}

export function cancelBetResponder(req: Request & GVCExtraRequestData): GVCVoidTransactionResponse {
    return {
        partnerId: "1",
        accountId: SKYWIND_PARTNER_ID,
        currencyCode: "USD",
        balance: 100,
        bpGameRoundId: FAKE_GAME_ROUND_ID,
        bpTransactionId: "c79a65f0-3762-4e5e-8db1-4ea61a770b7a",
        transactionId: "e916b795-ccc0-4af8-ac55-247acce16ca5",
        status: {
            statusCode: "0",
            statusMessage: "success"
        }
    } as GVCVoidTransactionResponse;
}
