import * as uuid from "uuid";

export enum WALLET_ACTION {
    debit = -1, // BET
    zero_win,
    credit = 1, // WIN
    rollback = 2,
    bonus = 3, // BONUS
    closeRoundWithRoundStats = 4
}

export enum TRANSACTION_INDEX {
    INTERNAL_ID,
    AMOUNT,
    EXTERNAL_ID,
    IS_ROLLBACK,
    CUSTOMER_ID,
    IPM_TRANSACTION_DATA
}

export type Transaction = [
    string, number, string, boolean, string, {
        currencyCode: string,
        gameCode: string,
        gameId: number,
        eventId: number,
        gameType: string,
        platform: string,
        roundId: number
    }?
];

let trxs = {};
export const getAll = () => trxs;

export const setAll = (newTrx) => trxs = newTrx;

export const getById = (trxId: string, action: WALLET_ACTION | string): Transaction => {
    return trxs[`${WALLET_ACTION[action] || action}_${trxId}`];
};

export const setById =
    (trxId: string,
     action: WALLET_ACTION | string,
     amount: number,
     customerId: string,
     ipmTransactionData?: {
         currencyCode: string,
         gameCode: string,
         gameId: number,
         eventId: number,
         gameType: string,
         platform: string,
         roundId: number
     }): Transaction => {

        // debit amount must be negative in transaction data, also, have to explicitly cast to number with '+'
        const balanceChange = action === WALLET_ACTION.debit ? -(+amount) : (+amount);

        const transaction = [];
        transaction[TRANSACTION_INDEX.INTERNAL_ID] = trxId;
        transaction[TRANSACTION_INDEX.AMOUNT] = balanceChange;
        transaction[TRANSACTION_INDEX.EXTERNAL_ID] = uuid.v4();
        transaction[TRANSACTION_INDEX.IS_ROLLBACK] = false;
        transaction[TRANSACTION_INDEX.CUSTOMER_ID] = customerId;
        if (ipmTransactionData) {
            transaction[TRANSACTION_INDEX.IPM_TRANSACTION_DATA] = ipmTransactionData;
        }
        const key = getTransactionKey(trxId, action);
        trxs[key] = transaction;

        return trxs[key];
    };

export const rollbackById = (trxId: string, action: WALLET_ACTION | string): Transaction => {
    const key = `${WALLET_ACTION[action] || action}_${trxId}`;
    trxs[key][TRANSACTION_INDEX.IS_ROLLBACK] = true;

    return trxs[key];
};

export const getTransactionKey = (trxId: string, action: WALLET_ACTION | string): string => {
    return `${WALLET_ACTION[action] || action}_${trxId}`;
};
