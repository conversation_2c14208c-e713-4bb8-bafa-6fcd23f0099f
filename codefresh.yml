version: '1.0'

steps:
 compile:
   image: node:14.17-alpine
   working-directory: ${{initial-clone}}
   commands:
     - echo "NPM Install"
     - npm install --silent
     - echo "Compile Server"
     - npm run compile

 build-step:
   type: build
   dockerfile: Dockerfile.prod
   image-name: skywindgroup/ipm-mock

 push to registry:
   type: push
   candidate: ${{build-step}}
   tag: ${{CF_BRANCH_TAG_NORMALIZED}}
