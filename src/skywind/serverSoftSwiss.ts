"use strict";

import createApplication from "../config/express";
import { defineRoutes } from "./api/routers";
import { Application } from "express";
import { Server } from "http";
import * as fs from "fs";
import logger from "./utils/logger";
import config from "./config";
import { INTEGRATION_TYPE } from "../config/integrationType";
import { setById } from "./models/merchant";

const log = logger(INTEGRATION_TYPE.SOFT_SWISS);
const version = fs.readFileSync(__dirname + "/version", "utf8");

export default async (port = 8009): Promise<Server> => {
    const app = await getApplication();
    return new Promise<Server>((resolve) => {
        const server: Server = require("http")
            .createServer(app)
            .listen(port, null, () => {
                log.info("SoftSwiss mock server listening on " + port);
                log.info("AppVersion: " + version);
                setById("soft_swiss__mock", {
                    merch_id: "soft_swiss__mock",
                    merch_pwd: "qwerty123",
                    customers: {
                        ["Customer123"]: {
                            cust_id: "Customer123",
                            cust_login: "PLAYER1",
                            currency_code: "USD",
                            language: "en",
                            country: "US",
                            test_cust: false,
                            status: "normal",
                            bet_limit: null,
                            first_name: "",
                            last_name: "",
                            email: "",
                            jurisdiction: null,
                            balance: {
                                amount: 500000,
                                currency_code: "USD"
                            }
                        },
                    },
                    isPromoInternal: false,
                    multiple_session: false,
                });
                log.info("Merchant with id soft_swiss__mock was created. Customer with id Customer123 was created");
                resolve(server);
            });
    });
};

let app: Application;

export async function getApplication(): Promise<Application> {
    config.serverName = INTEGRATION_TYPE.SOFT_SWISS;
    if (!app) {
        app = createApplication();
        await defineRoutes(app, INTEGRATION_TYPE.SOFT_SWISS, log);
    }
    return app;
}
