import { Customer, Merchant } from "../models/merchant";
import { Request } from "express";
import { getParamValues } from "../utils/helper";
import { EntityNotFound, InvalidRequest } from "../errors";
import * as Merchants from "../models/merchant";
import { settings } from "../models/settings";

export const defaultCustomerId: string = "test_customer";
export const defaultCustomerCurrency: string = "USD";

export const getDefaultCustomer = (custId: string = defaultCustomerId,
                                   currencyCode: string = defaultCustomerCurrency,
                                   isTest = true,
                                   amount: number = settings.amount): Customer => ({
    cust_id: custId,
    cust_login: "",
    currency_code: currencyCode,
    status: "normal",
    bet_limit: null,
    test_cust: isTest,
    country: "CN",
    language: "en",
    balance: {
        amount,
        currency_code: currencyCode,
    }
});

export function getMerchantCustomer(req: Request): Customer {
    const [merchantId, customerId] = getParamValues(req, ["merch_id", "cust_id"]);

    const merchant = Merchants.getById(merchantId, true);

    const customer = merchant.customers[customerId];
    if (!customer) {
        throw new EntityNotFound("customer", customerId);
    }
    return customer;
}

export function updateCustomer(merchant: Merchant, customer: Customer, dataToUpdate: Customer): Customer {
    const newCustomer: Customer = { ...customer, ...dataToUpdate };

    if (newCustomer.balance) {
        newCustomer.balance = {
            amount: newCustomer.balance.amount,
            currency_code: newCustomer.currency_code
        };

    }
    newCustomer.status = newCustomer.status || "normal";
    newCustomer.bet_limit = +newCustomer.bet_limit || null;

    merchant.customers[customer.cust_id] = newCustomer;
    return newCustomer;
}

export function createCustomer(merchant: Merchant, customerData: Customer): Customer {
    const savedCustomer = merchant.customers[customerData.cust_id];
    if (savedCustomer) {
        return savedCustomer;
    }
    const customer: Customer = {
        ...customerData,
        status: customerData.status || "normal",
        bet_limit: customerData.bet_limit || null
    };

    customer.bonusBalance = customerData.bonusBalance || 0;

    customer.balance = {
        amount: 0,
        currency_code: customer.currency_code
    };

    merchant.customers[customerData.cust_id] = customer;
    return customer;
}

export function setCustomerFreebets(customer: Customer, coin: number, count: number): Customer {
    if (coin <= 0) {
        throw new InvalidRequest("free bet coin should be positive");
    }

    if (count < 0) {
        throw new InvalidRequest("free bet count should be positive");
    }

    if (!Number.isInteger(count)) {
        throw new InvalidRequest("free bet count should be integer");
    }

    customer.freeBets = { count, coin };
    return customer;
}
