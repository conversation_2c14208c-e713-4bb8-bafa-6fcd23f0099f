import { NextFunction, Request, Response, Router } from "express";
import { authorise<PERSON><PERSON><PERSON><PERSON>, authorisePlayer, RequestAdditionalInfo } from "./middleware";
import { settings } from "../../models/settings";
import { isEmpty } from "../../utils/isEmpty";
import {
    cancelWagerResponder,
    getAccountResponder,
    getBalanceResponder,
    resultResponder,
    wagerResponder
} from "./responders";
import { MrGreenService } from "./service";
import { EmptyFieldError, MrGreenError } from "./errors";
import {
    CancelWagerRequest,
    CancelWagerResponse,
    GameClientType,
    GameStatusType,
    GetBalanceRequest,
    ResultRequest,
    ResultType,
    WagerRequest
} from "./entities";
import { XML_TYPE } from "../../utils/helper";
import { routerCallbackWrapper } from "../../api/config/middleware";
import logger from "../../utils/logger";
import { INTEGRATION_TYPE } from "../../../config/integrationType";
import { createXML } from "../../utils/createXml";
const router: Router = Router();
const log = logger(`${INTEGRATION_TYPE.MR_GREEN}:api`);

function validateFieldsForEmpty(req: Request, res: Response, next: NextFunction) {
        let fields: string[];
        switch (req.query.request) {
            case "getaccount":
                fields = ["request", "callerauth", "callerpassword", "session"];
                break;
            case "getbalance":
            case "wager":
            case "result":
            case "cancelwager":
                fields = ["request", "callerauth", "callerpassword", "sessionId"];
                break;
            default:
                fields = [];
        }

        for (const param of fields) {
            const value = req.query[param];
            if (isEmpty(value)) {
                return next(new EmptyFieldError(`[${param}] is required`));
            }
        }
        return next();
}

function notSaveAnyDataMiddleware(req: Request & RequestAdditionalInfo, res: Response, next: NextFunction) {
    if (!settings.notSaveAnyData) {
        return next();
    }

    let result;

    switch (req.query.request) {
        case "getaccount":
            result = getAccountResponder(req);
            break;
        case "getbalance":
            result = getBalanceResponder(req);
            break;
        case "wager":
            result = wagerResponder(req);
            break;
        case "result":
            result = resultResponder(req);
            break;
        case "cancelwager":
            result = cancelWagerResponder(req);
            break;
        default:
            throw new Error("Wrong request");
    }

    return send(req, res, createXML(getSuccessResponseTemplate(req, result), log));
}

/**
 * examples:
 *
 */
router.get("/externalGameAdapter.dll",
    notSaveAnyDataMiddleware,
    validateFieldsForEmpty,
    authoriseMerchant,
    authorisePlayer,
    routerCallbackWrapper((req: Request & RequestAdditionalInfo, res: Response) => {
        const mrGreenService = new MrGreenService(req.merchant, req.customer, req);
        let result;
        switch (req.query.request) {
            case "getaccount": {
                result = mrGreenService.getAccount({
                    sessionid: req.query.session.toString()
                });
                break;
            }
            case "getbalance":
                result = mrGreenService.getBalance({
                    sessionid: req.query.sessionId,
                    gameId: req.query.gameid
                } as GetBalanceRequest);
                break;
            case "wager":
                result = mrGreenService.wager({
                    sessionid: req.query.sessionid,
                    accountid: req.query.accountid,
                    betamount: +req.query.betamount,
                    gameid: req.query.gameid as string,
                    roundid: +req.query.roundid,
                    description: req.query.description,
                    transactionid: +req.query.transactionid,
                    gameclienttype: req.query.gameclienttype === GameClientType.FLASH ?
                                    GameClientType.FLASH : GameClientType.HTML,
                    jackpotcontribution: req.query.jackpotcontribution,
                    discountdescription: req.query.discountdescription,
                    discountamount: req.query.discountamount,
                    freeroundbalance: req.query.freeroundbalance
                } as WagerRequest);
                break;
            case "result":
                result = mrGreenService.result({
                    sessionid: req.query.sessionid,
                    accountid: req.query.accountid,
                    gameid: req.query.gameid,
                    result: +req.query.result,
                    roundid: +req.query.roundid,
                    description: req.query.description,
                    jackpotwin: req.query.jackpotwin,
                    transactionid: +req.query.transactionid,
                    gameclienttype: req.query.gameclienttype === GameClientType.FLASH ?
                                    GameClientType.FLASH : GameClientType.HTML,
                    gamestatus: req.query.gamestatus === GameStatusType.PENDING ?
                                GameStatusType.PENDING : GameStatusType.COMPLETE,
                    type: req.query.type === ResultType.CASH ? ResultType.CASH : ResultType.BONUS,
                    promotioncode: req.query.promotioncode,
                    discountdescription: req.query.discountdescription,
                    discountamount: req.query.discountamont,
                    goodsdescription: req.query.goodsdescription,
                    goodsamount: req.query.googsamount,
                    gamedetails: req.query.gamedetails,
                    freeroundbalance: req.query.freeroundbalance
                } as ResultRequest);
                break;
            case "cancelwager":
                result = mrGreenService.cancelWager({
                    accountid: req.query.accountid,
                    gameid: req.query.gameid,
                    cancelwageramount: req.query.cancelwageramount,
                    roundid: +req.query.roundid,
                    transactionid: +req.query.transactionid,
                    sessionid: req.query.sessionid,
                    description: req.query.description,
                    freeroundbalance: req.query.freeroundbalance
                } as CancelWagerRequest);
                break;
            default:
                throw new Error("Wrong request");
        }

        return send(req, res, createXML(getSuccessResponseTemplate(req, result), log));
}));

export function toUpperCaseResult(result) {
    return Object.keys(result).reduce(function (newObj, key) {
        const subObj = result[key];
        newObj[key.toUpperCase()] = (typeof subObj === "object" && subObj != null) ? toUpperCaseResult(subObj) : subObj;
        return newObj;
    }, {});
}

export function getSuccessResponseTemplate(req, result) {
    return {
        RSP: {
            "@request": req.query.request,
            "@rc": "0",
            ...toUpperCaseResult(result)
        }
    };
}

export function getErrorResponseTemplate(req, err: MrGreenError) {
    const result = {
        RSP: {
            "@request": req.query.request,
            "@rc": err.rc,
            "@msg": err.msg,
        }
    };

    if (err.customField) {
        result.RSP["@customField"] = err.customField;
    }

    return result;
}

export function send(req: Request, res: Response, body: object) {
    res.type(XML_TYPE);
    res.send(body);
    return null;
}

export default router;
