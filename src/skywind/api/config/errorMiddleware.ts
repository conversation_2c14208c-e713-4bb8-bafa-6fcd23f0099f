import { measures } from "@skywind-group/sw-utils";
import { NextFunction, Request, Response } from "express";
import { v4 } from "uuid";
import { getTextResponse, JSON_ACCEPT_TYPE, XML_ACCEPT_TYPE, XML_TYPE } from "../../utils/helper";
import { POPBaseError } from "../../popErrors";
import { BaseErrorToLog, ExtendedErrorInfo, SeamlessAPIError } from "../../errors";
import { getResponseTemplate, MerchantCustomerHolder } from "../pokerStar";
import { PokerStarBaseError } from "../../errorsPokerStar";
import { RelaxBaseError } from "../../errorRelax";
import { PariplayBaseError } from "../../errorsPariplay";
import { INTEGRATION_TYPE } from "../../../config/integrationType";
import { MrGreenError } from "../../integrations/mrGreen/errors";
import { prepareGANError } from "./ganMiddleware";
import { getErrorResponseTemplate } from "../../integrations/mrGreen/api";
import { EVERY_MATRIX_STATUS, EveryMatrixError } from "../../integrations/everyMatrix/errors";
import { BetVictorError } from "../../integrations/betVictor/errors";
import { SisalError } from "../../sisal/sisal.errors";
import measureProvider = measures.measureProvider;
import { createXML } from "../../utils/createXml";

const DEFAULT_RESPONSE_STATUS = 400;

function getDefaultResponseStatusForIntegration(integrationType: INTEGRATION_TYPE) {
    switch (integrationType) {
        case INTEGRATION_TYPE.EVERY_MATRIX:
        case INTEGRATION_TYPE.MR_GREEN:
        case INTEGRATION_TYPE.PARIPLAY:
            return 200;
        case INTEGRATION_TYPE.BET_VICTOR:
            return 400;
        case INTEGRATION_TYPE.RELAX:
        case INTEGRATION_TYPE.SISAL:
            return 500;
        default:
            return DEFAULT_RESPONSE_STATUS;
    }
}

const buildIntegrationError = (integrationType: INTEGRATION_TYPE, err: any, httpCode: any) => {
    if (integrationType === INTEGRATION_TYPE.POP && !err.errorMsg) {
        return new POPBaseError(
            httpCode,
            err.error_msg,
            err.error_code,
            { ...err, error_msg: undefined, error_code: undefined });
    } else if (integrationType === INTEGRATION_TYPE.IPM && !err.error_msg) {
        return new SeamlessAPIError(
            httpCode,
            err.errorMsg,
            err.errorCode,
            { ...err, errorMsg: undefined, errorCode: undefined });
    } else if (integrationType === INTEGRATION_TYPE.POKER_STAR && !err.errorMessage) {
        return new PokerStarBaseError(
            httpCode,
            err.errorMsg || err.error_msg || err.message,
            err.errorCode || err.errorType || err.error_code,
            { ...err, errorMsg: undefined, errorCode: undefined });
    } else if (integrationType === INTEGRATION_TYPE.RELAX && !err.errorcode) {
        return new RelaxBaseError(
            httpCode,
            err.errorCode || err.errorType || err.error_code || "UNHANDLED",
            err.errorMsg || err.error_msg || err.message || "Unhandled error",
        );
    } else if (integrationType === INTEGRATION_TYPE.PARIPLAY && !(err instanceof PariplayBaseError)) {
        return new PariplayBaseError(
            httpCode,
            err.ErrorCode || err.errorCode || err.errorType || err.error_code || 900,
            err.ErrorMessage || err.errorMsg || err.error_msg || err.message
        );
    } else if (integrationType === INTEGRATION_TYPE.SISAL && !(err instanceof SisalError)) {
        return new SisalError(
            err.ErrorCode || err.errorCode || err.errorType || err.error_code || 9999,
            err.ErrorMessage || err.errorMsg || err.error_msg || err.message || "Generic Error"
        );
    } else if (integrationType === INTEGRATION_TYPE.BET_VICTOR && !(err instanceof BetVictorError)) {
        return new BetVictorError(
            err.ErrorCode || err.errorCode || err.errorType || err.error_code || 9999,
            err.ErrorMessage || err.errorMsg || err.error_msg || err.message || "Generic Error"
        );
    }

    return err;
};

export function createErrorToLog(error: BaseErrorToLog & ExtendedErrorInfo): any {
    return error.toLog ? error.toLog() : error;
}

export function createErrorHandler(
    logger,
    integrationType: INTEGRATION_TYPE = INTEGRATION_TYPE.IPM): (err: any,
                                                                req: Request & MerchantCustomerHolder,
                                                                res: Response,
                                                                next: NextFunction) => any {

    return (err: any, req: Request, res: Response, next: NextFunction): any => {
        if (!err) {
            return;
        }

        measureProvider.saveError(err);

        const httpCode = err.responseStatus || err.http_response_status ||
            getDefaultResponseStatusForIntegration(integrationType);
        const errorCode = err.error_code || err.errorCode || err.code;
        const errorMessage = err.error_msg || err.errorMsg || err.message;

        const error = buildIntegrationError(integrationType, err, httpCode);
        const errorInfo = {
            ...error,
            extraData: undefined,
            ...error.extraData,
            responseStatus: undefined
        };

        logger.error(createErrorToLog(err));
        if (integrationType === INTEGRATION_TYPE.SOFT_SWISS) {
            return res.status(httpCode).send({
                code: err.code + "",
                msg: err.message,
                meta: {
                    api_code: err.code,
                    api_message: err.message,
                }
            });
        } else if (integrationType === INTEGRATION_TYPE.GVC) {
            return res.status(httpCode).json({
                ...errorInfo,
                status: {
                    statusCode: errorCode,
                    statusMessage: errorMessage
                }
            });
        } else if (integrationType === INTEGRATION_TYPE.SBTECH) {
            return res.status(httpCode).json(errorInfo);
        } else if (integrationType === INTEGRATION_TYPE.GAN) {
            return res.status(httpCode).send(prepareGANError(err));
        } else if (integrationType === INTEGRATION_TYPE.SISAL) {
            return res.status(httpCode).send({ ...errorInfo, message: errorMessage });
        } else if (integrationType === INTEGRATION_TYPE.EVERY_MATRIX) {
            const errToRaise = err instanceof EveryMatrixError ? err :
                               new EveryMatrixError(EVERY_MATRIX_STATUS.Failed,
                                   errorCode,
                                   errorMessage,
                                   v4(),
                                   httpCode,
                                   err.custom_field);
            if (!err.custom_field) {
                delete errToRaise.CustomField;
            }
            return res.status(httpCode).json(errToRaise);
        } else if (integrationType === INTEGRATION_TYPE.MR_GREEN) {
            const errToRaise = err instanceof MrGreenError ? err :
                               new MrGreenError(errorCode,
                                   errorMessage,
                                   httpCode,
                                   err.custom_field);
            const response = getErrorResponseTemplate(req, errToRaise);
            return res.status(httpCode).type(XML_TYPE).send(createXML(response, logger));
        } else if (integrationType === INTEGRATION_TYPE.BET_VICTOR) {
            if (err instanceof BetVictorError) {
                return res.status(err.responseStatus).json({
                    responseCode: err.responseCode,
                    message: err.message,
                });
            }
            const errToRaise = {
                responseCode: errorCode,
                message: errorMessage,
            };
            if (err.custom_field) {
                errorCode.customField = err.custom_field;
            }
            return res.status(httpCode).json(errToRaise);
        } else if ([
            INTEGRATION_TYPE.POP,
            INTEGRATION_TYPE.RELAX,
            INTEGRATION_TYPE.PARIPLAY
        ].includes(integrationType) || req.header("Accept") === JSON_ACCEPT_TYPE) {
            return res.status(httpCode).json(errorInfo);
        } else if (integrationType === INTEGRATION_TYPE.POKER_STAR &&
            [XML_ACCEPT_TYPE, XML_TYPE].includes(req.header("Content-Type"))) {

            // POKER STAR
            const response = getResponseTemplate(req);
            response.Message.Body = {
                "@xsi:type": errorInfo.errorType,
                ErrorMessage: errorInfo.errorMessage,
            };
            res.type(XML_TYPE);
            return res.status(httpCode).send(createXML(response, logger));
        } else {
            return res.status(httpCode).send(getTextResponse(errorInfo));
        }
    };
}

export function createErrorHandlerForManagement(logger): (err: any,
                                                          req: Request & MerchantCustomerHolder,
                                                          res: Response,
                                                          next: NextFunction) => any {

    return (err: any, req: Request, res: Response, next: NextFunction): any => {
        if (err) {
            measureProvider.saveError(err);

            const errorInfo = {
                ...err,
                extraData: undefined,
                ...err.extraData,
                responseStatus: undefined
            };

            logger.error(errorInfo);

            return res.status(DEFAULT_RESPONSE_STATUS).send(getTextResponse(errorInfo));
        }
    };
}
