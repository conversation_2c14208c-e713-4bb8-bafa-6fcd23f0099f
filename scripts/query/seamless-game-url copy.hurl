# Comprehensive Seamless Wallet + IMP-Mock Game Flow Test
# This script demonstrates a complete game flow from user registration to gameplay
# using seamless wallet integration and mock services

# Environment Variables Setup
# MAPI_URL: Management API URL (default: http://localhost:3000)
# MOCK_URL: IMP Mock Service URL (default: http://localhost:8000)
# TERMINAL_URL: Terminal Service URL (default: http://localhost:3004)
# WALLET_URL: Wallet Service URL (default: http://localhost:3005)
# MAPI_PASSWORD: Management API password
# GAME_CODE: Game to test (default: sw_mrmnky)

## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 3: Setup IMP-Mock Merchant
POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "seamless_test_merchant",
  "merch_pwd": "SecurePassword123!",
  "isPromoInternal": false,
  "multiple_session": true,
  "serverUrl": "{{MOCK_URL}}",
  "type": "seamless"
}
HTTP 201
[Asserts]
jsonpath "$.merch_id" == "seamless_test_merchant"

## Step 4: Create Test Customer in IMP-Mock
POST {{MOCK_URL}}/v1/merchant/seamless_test_merchant/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "test_player_001",
  "cust_login": "TESTPLAYER001",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "bet_limit": 1000,
  "first_name": "Test",
  "last_name": "Player",
  "email": "<EMAIL>",
  "jurisdiction": "COM",
  "balance": {
    "amount": 100000,
    "currency_code": "USD"
  }
}
HTTP 201
[Asserts]
jsonpath "$.cust_id" == "test_player_001"
jsonpath "$.cust_login" == "TESTPLAYER001"
jsonpath "$.balance.amount" == 100000

## Step 2: Generate Terminal Token
POST {{MAPI_URL}}/v1/entities/MERCHANT_ENTITY/terminals/token/generate
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{}
HTTP 200
[Captures]
terminalToken: jsonpath "$['terminalToken']"
[Asserts]
jsonpath "$.terminalToken" exists
jsonpath "$.terminalToken" matches "^[A-Za-z0-9+/=]+$"

## Step 5: Generate Customer Ticket
GET {{MOCK_URL}}/v1/merchant/seamless_test_merchant/customer/test_player_001/ticket
accept: text/plain
HTTP 200
[Captures]
playerTicket: body
[Asserts]
body matches "^[A-Za-z0-9+/=]+$"

## Step 6: Player External Login via Terminal
POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "{{playerTicket}}"
}
HTTP 200
[Captures]
playerToken: jsonpath "$['token']"
playerId: jsonpath "$['playerId']"
[Asserts]
jsonpath "$.token" exists
jsonpath "$.playerId" exists
jsonpath "$.currency" == "USD"
jsonpath "$.balance" exists

## Step 7: Initialize Seamless Wallet Service
POST {{WALLET_URL}}/v1/balances/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "seamless_test_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "SecurePassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{playerId}}",
    "currency": "USD",
    "brandId": 1
  },
  "regulation": "default"
}
HTTP 200
[Captures]
initialBalance: jsonpath "$.main"
[Asserts]
jsonpath "$.main" >= 0
jsonpath "$.currency" == "USD"

## Step 8: Get Game URL for Seamless Integration
POST {{MAPI_URL}}/v1/game/url
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "merchantType": "seamless",
  "merchantCode": "seamless_test_merchant",
  "gameCode": "{{GAME_CODE}}",
  "playmode": "real",
  "language": "en",
  "ip": "127.0.0.1",
  "lobby": "http://localhost:3000/lobby",
  "cashier": "http://localhost:3000/cashier",
  "ticket": "{{playerTicket}}",
  "currency": "USD"
}
HTTP 200
[Captures]
gameUrl: jsonpath "$.url"
gameToken: jsonpath "$.token"
[Asserts]
jsonpath "$.url" exists
jsonpath "$.token" exists
jsonpath "$.currency" == "USD"

## Step 9: Verify Game Token via Wallet Service
POST {{WALLET_URL}}/v1/games/login-terminal
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "seamless_test_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "SecurePassword123!"
    }
  },
  "initRequest": {
    "ticket": "{{playerTicket}}",
    "playmode": "real"
  }
}
HTTP 200
[Captures]
sessionId: jsonpath "$.sessionId"
tokenData: jsonpath "$.tokenData"
[Asserts]
jsonpath "$.sessionId" exists
jsonpath "$.tokenData.playerId" exists
jsonpath "$.tokenData.currency" == "USD"

## Step 10: Register Game Round
POST {{WALLET_URL}}/v1/register_round
accept: application/json
Content-Type: application/json
{
  "request": {
    "roundId": "test_round_001",
    "gameCode": "{{GAME_CODE}}",
    "playerId": "{{playerId}}",
    "currency": "USD"
  },
  "merchantInfo": {
    "code": "seamless_test_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "SecurePassword123!"
    }
  },
  "regulation": "default"
}
HTTP 200
[Captures]
roundId: jsonpath "$.roundId"
[Asserts]
jsonpath "$.roundId" == "test_round_001"
jsonpath "$.status" == "success"

## Step 11: Place Bet via Seamless Wallet
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "seamless_test_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "SecurePassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{playerId}}",
    "currency": "USD",
    "brandId": 1,
    "sessionId": "{{sessionId}}"
  },
  "request": {
    "transactionId": "bet_txn_001",
    "roundId": "test_round_001",
    "gameCode": "{{GAME_CODE}}",
    "bet": 10.00,
    "win": 0,
    "currency": "USD",
    "operationType": "bet"
  },
  "regulation": "default"
}
HTTP 200
[Captures]
betBalance: jsonpath "$.balance"
betTransactionId: jsonpath "$.transactionId"
[Asserts]
jsonpath "$.balance" < {{initialBalance}}
jsonpath "$.transactionId" == "bet_txn_001"
jsonpath "$.currency" == "USD"

## Step 12: Process Win via Seamless Wallet
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "seamless_test_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "SecurePassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{playerId}}",
    "currency": "USD",
    "brandId": 1,
    "sessionId": "{{sessionId}}"
  },
  "request": {
    "transactionId": "win_txn_001",
    "roundId": "test_round_001",
    "gameCode": "{{GAME_CODE}}",
    "bet": 0,
    "win": 25.00,
    "currency": "USD",
    "operationType": "win"
  },
  "regulation": "default"
}
HTTP 200
[Captures]
finalBalance: jsonpath "$.balance"
winTransactionId: jsonpath "$.transactionId"
[Asserts]
jsonpath "$.balance" > {{betBalance}}
jsonpath "$.transactionId" == "win_txn_001"
jsonpath "$.currency" == "USD"

## Step 13: Keep Game Session Alive
POST {{WALLET_URL}}/v1/games/keep-alive
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "seamless_test_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "SecurePassword123!"
    }
  },
  "gameToken": {
    "playerId": "{{playerId}}",
    "currency": "USD",
    "brandId": 1,
    "sessionId": "{{sessionId}}"
  }
}
HTTP 200
[Asserts]
jsonpath "$.status" == "success"

## Step 14: Verify Final Balance
POST {{WALLET_URL}}/v1/balances/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "seamless_test_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "SecurePassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{playerId}}",
    "currency": "USD",
    "brandId": 1
  },
  "regulation": "default"
}
HTTP 200
[Asserts]
jsonpath "$.main" == {{finalBalance}}
jsonpath "$.currency" == "USD"

## Step 15: Cleanup - Logout Player
POST {{TERMINAL_URL}}/v1/terminals/players/logout
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "playerId": "{{playerId}}"
}
HTTP 200
[Asserts]
jsonpath "$.status" == "success"
