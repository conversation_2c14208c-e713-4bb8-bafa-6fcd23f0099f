import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { SinonStub, spy, stub } from "sinon";

const request = require("supertest");

import config from "../../skywind/config";
import { SBTechBaseSpec } from "./base.spec";
import { createTicket } from "../../skywind/service/ticket";
import { SBTECH_ERROR_NAME } from "../../skywind/errorsSBTech";

@suite("SBTech.auth")
class SBTechAuthSpec extends SBTechBaseSpec {

    @test("should get session info")
    public async initSession() {
        const ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const initSessionRequest = Object.assign(this.fixtures.initSessionRequest,
            { clientToken: ticket.id });
        const expectedResponseBody = {
            "balance": 10000,
            "brandId": "sbtech",
            "countryCode": "CN",
            "currencyCode": "USD",
            "isTestPlayer": true,
            "jurisdiction": "",
            "playerId": "sbtech_player",
        };

        const actualResponse = await request(this.server)
            .post(this.urlPathToInitSession)
            .send(initSessionRequest)
            .expect(200);

        delete actualResponse.body.sessionId;
        delete actualResponse.body.timestamp;
        expect(actualResponse.body).deep.equal(expectedResponseBody);
    }

    @test("throw error when client token is invalid")
    public async throwErrorWhenInvalidClientToken() {
        createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const initSessionRequest = Object.assign(this.fixtures.initSessionRequest,
            { clientToken: "wrong_ticket_id" });

        const actualResponse = await request(this.server)
            .post(this.urlPathToInitSession)
            .send(initSessionRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.INVALID_CLIENT_TOKEN);
    }

    @test("throw error when client token expired")
    public async throwErrorWhenClientTokenExpired() {
        config.expirationTime.ticket = 0; // to check expired ticket

        const ticket = createTicket(this.fixtures.merchantCode, this.fixtures.playerCode);
        const initSessionRequest = Object.assign(this.fixtures.initSessionRequest,
            { clientToken: ticket.id });

        const actualResponse = await request(this.server)
            .post(this.urlPathToInitSession)
            .send(initSessionRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.INVALID_CLIENT_TOKEN);

        config.expirationTime.ticket = this.ticketExpirationTimeDefault; // revert default
    }

    @test("throw error when merchant not found")
    public async throwErrorWhenMerchantNotFound() {
        const initSessionRequest = Object.assign(this.fixtures.getBalanceRequest, {
            brandId: "wrong_merchant_code"
        });

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(initSessionRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.WRONG_CREDENTIALS);
    }

    @test("throw error when player not found")
    public async throwErrorWhenPlayerNotFound() {
        const initSessionRequest = Object.assign(this.fixtures.getBalanceRequest, {
            playerId: "wrong_player_code"
        });

        const actualResponse = await request(this.server)
            .post(this.urlPathToGetBalance)
            .send(initSessionRequest)
            .expect(400);

        expect(actualResponse.body.error).deep.equal(SBTECH_ERROR_NAME.WRONG_CREDENTIALS);
    }
}
