export enum GameClientType {
    FLASH = "Flash",
    HTML = "HTML"
}

export enum GameStatusType {
    PENDING = "pending",
    COMPLETE = "complete"
}

export enum ResultType {
    BONUS = "Bonus",
    CASH = "Cash"
}

export enum MR_GREEN_MOCK_TYPE {
    /**
     * Mock engine with fully manual token setup
     */
    MockEngine,
    /**
     * Mock engine with semi-manual token setup
     */
    MockEnginePredefined,
}

export enum MR_GREEN_JURISDICTION {
    UK = "UKGC",
    MGA = "MGA",
    Sweden = "SGA"
}

// normal player token format is "emLaunchToken_{operatorId}_{jurisdiction}"
export enum MR_GREEN_TOKEN {
    NORMAL = "mrGreenGameToken"
}

export enum MR_GREEN_ACTION {
    GET_ACCOUNT = "getaccount",
    BET = "wager",
    WIN = "result",
    CANCEL = "cancelwager",
    GET_BALANCE = "getbalance"
}

export interface MrGreenAccountDetails {
    sex: string;
    city: string;
    firstName: string;
    lastName: string;
}

/**
 * Base
 */

export interface BaseRequest {
    sessionid: string;
}

export interface BaseResponse {
    sessionId: string;
}

export interface BasePaymentTransactionResponse extends BaseResponse {
    balance: number;
    walletTransactionId: number;
    alreadyProcessed: boolean;
}

export interface BasePaymentResponseDetails {
    realBalance: number;
    bonusBalance: number;
    baseCurrencyRate: number;
    baseCurrency: string;
}

/**
 * End of Base responses
 */

/**
 * Get Account
 */

export interface GetAccountResponse extends BaseResponse {
    accountId: string;
    currency: string;
    country: string;
    details: MrGreenAccountDetails;
}

/**
 * End of Get Account
 */

/**
 * Get Balance
 */

export interface GetBalanceRequest extends BaseRequest {
    gameId: string;
}

export interface GetBalanceResponseDetails {
    realBalance: number;
    bonusBalance: number;
}

export interface GetBalanceResponse extends BaseResponse {
    balance: number;
    details: GetBalanceResponseDetails;
}

/**
 * End of Get Balance
 */

/**
 * Wager
 */

export interface WagerRequest extends BaseRequest {
    accountid: string;
    betamount: number;
    gameid: string;
    roundid: number;
    description: string;
    transactionid: number;
    gameclienttype: GameClientType;
    jackpotcontribution?: string;
    discountdescription: string;
    discountamount: string;
    freeroundbalance: string;
}

export interface WagerResponse extends BasePaymentTransactionResponse {
    details: WagerResponseDetails;
}

export interface WagerResponseDetails extends BasePaymentResponseDetails {
    bonusMoneyBet: number;
    realMoneyBet: number;
}

/**
 * End of Wager
 */

/**
 * Result
 */

export interface ResultRequest extends BaseRequest {
    accountid: string;
    gameid: string;
    result: number;
    roundid: number;
    description: string;
    jackpotwin?: string;
    transactionid: number;
    gameclienttype: GameClientType;
    gamestatus: GameStatusType;
    type: ResultType;
    promotioncode: string;
    discountdescription: string;
    discountamount: string;
    goodsdescription: string;
    goodsamount: string;
    gamedetails: string;
    freeroundbalance: string;
}

export interface ResultResponse extends BasePaymentTransactionResponse {
    details: BasePaymentResponseDetails;
}

/**
 * End of Result
 */

/**
 * Cancel Wager
 */

export interface CancelWagerRequest extends BaseRequest {
    accountid: string;
    gameid: string;
    cancelwageramount: string;
    roundid: number;
    transactionid: number;
    sessionid: string;
    description: string;
    freeroundbalance: string;
}

export interface CancelWagerResponse extends BasePaymentTransactionResponse {
    transactionId: number;
}

/**
 * End of Cancel Wager
 */
