# Seamless Wallet Bonus and Promotional Features Test
# This script tests bonus handling, free bets, and promotional features

## Setup: Authentication and Basic Configuration
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"

POST {{MAPI_URL}}/v1/entities/MERCHANT_ENTITY/terminals/token/generate
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{}
HTTP 200
[Captures]
terminalToken: jsonpath "$['terminalToken']"

## Setup: Create Promotional Merchant
POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "promo_merchant",
  "merch_pwd": "PromoPassword123!",
  "isPromoInternal": true,
  "multiple_session": true,
  "serverUrl": "{{MOCK_URL}}",
  "type": "seamless"
}
HTTP 200
[Asserts]
jsonpath "$.merch_id" == "promo_merchant"
jsonpath "$.isPromoInternal" == true

## Test 1: Create Customer with Bonus Balance
POST {{MOCK_URL}}/v1/merchant/promo_merchant/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "bonus_player_001",
  "cust_login": "BONUSPLAYER001",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "bet_limit": 500,
  "first_name": "Bonus",
  "last_name": "Player",
  "email": "<EMAIL>",
  "jurisdiction": "COM",
  "balance": {
    "amount": 50000,
    "currency_code": "USD"
  },
  "bonus_balance": {
    "amount": 25000,
    "currency_code": "USD"
  },
  "free_bet_balance": {
    "amount": 10000,
    "currency_code": "USD"
  }
}
HTTP 200
[Captures]
bonusPlayerId: jsonpath "$.cust_id"
[Asserts]
jsonpath "$.cust_id" == "bonus_player_001"
jsonpath "$.bonus_balance.amount" == 25000
jsonpath "$.free_bet_balance.amount" == 10000

## Test 2: Generate Ticket and Login
GET {{MOCK_URL}}/v1/merchant/promo_merchant/customer/bonus_player_001/ticket
accept: text/plain
HTTP 200
[Captures]
bonusPlayerTicket: body

POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "{{bonusPlayerTicket}}"
}
HTTP 200
[Captures]
bonusPlayerToken: jsonpath "$['token']"
bonusPlayerSessionId: jsonpath "$['playerId']"
[Asserts]
jsonpath "$.token" exists
jsonpath "$.balance" >= 50000
jsonpath "$.bonusBalance" >= 25000

## Test 3: Check Multi-Balance Structure
POST {{WALLET_URL}}/v1/balances/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "regulation": "default"
}
HTTP 200
[Captures]
mainBalance: jsonpath "$.main"
bonusBalance: jsonpath "$.extraBalances.bonus"
freeBetBalance: jsonpath "$.extraBalances.freeBet"
[Asserts]
jsonpath "$.main" >= 500
jsonpath "$.extraBalances.bonus" >= 250
jsonpath "$.extraBalances.freeBet" >= 100
jsonpath "$.currency" == "USD"

## Test 4: Place Bet Using Main Balance
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "main_bet_001",
    "roundId": "bonus_round_001",
    "gameCode": "sw_mrmnky",
    "bet": 20.00,
    "win": 0,
    "currency": "USD",
    "operationType": "bet",
    "balanceType": "main"
  },
  "regulation": "default"
}
HTTP 200
[Captures]
afterMainBetBalance: jsonpath "$.balance"
[Asserts]
jsonpath "$.balance" < {{mainBalance}}
jsonpath "$.transactionId" == "main_bet_001"
jsonpath "$.currency" == "USD"

## Test 5: Place Bonus Bet
POST {{WALLET_URL}}/v1/payments/bonus
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "bonus_bet_001",
    "roundId": "bonus_round_002",
    "gameCode": "sw_mrmnky",
    "bet": 15.00,
    "win": 0,
    "currency": "USD",
    "operationType": "bet",
    "balanceType": "bonus"
  },
  "regulation": "default"
}
HTTP 200
[Captures]
afterBonusBetBalance: jsonpath "$.balance"
[Asserts]
jsonpath "$.balance" < {{bonusBalance}}
jsonpath "$.transactionId" == "bonus_bet_001"
jsonpath "$.currency" == "USD"

## Test 6: Free Bet Information Request
POST {{WALLET_URL}}/v1/info/free-bet
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "gameCode": "sw_mrmnky",
    "currency": "USD"
  }
}
HTTP 200
[Captures]
freeBetInfo: jsonpath "$.freeBetInfo"
[Asserts]
jsonpath "$.freeBetInfo" exists
jsonpath "$.freeBetInfo.available" == true
jsonpath "$.freeBetInfo.amount" > 0

## Test 7: Place Free Bet
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "free_bet_001",
    "roundId": "bonus_round_003",
    "gameCode": "sw_mrmnky",
    "bet": 10.00,
    "win": 0,
    "currency": "USD",
    "operationType": "bet",
    "freeBetCoin": true,
    "balanceType": "freeBet"
  },
  "regulation": "default"
}
HTTP 200
[Captures]
afterFreeBetBalance: jsonpath "$.balance"
[Asserts]
jsonpath "$.balance" <= {{freeBetBalance}}
jsonpath "$.transactionId" == "free_bet_001"
jsonpath "$.currency" == "USD"

## Test 8: Win from Free Bet (should go to main balance)
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "free_bet_win_001",
    "roundId": "bonus_round_003",
    "gameCode": "sw_mrmnky",
    "bet": 0,
    "win": 30.00,
    "currency": "USD",
    "operationType": "win",
    "freeBetCoin": true
  },
  "regulation": "default"
}
HTTP 200
[Captures]
freeBetWinBalance: jsonpath "$.balance"
[Asserts]
jsonpath "$.balance" > {{afterMainBetBalance}}
jsonpath "$.transactionId" == "free_bet_win_001"
jsonpath "$.currency" == "USD"

## Test 9: Bonus Win (should stay in bonus balance)
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "bonus_win_001",
    "roundId": "bonus_round_002",
    "gameCode": "sw_mrmnky",
    "bet": 0,
    "win": 45.00,
    "currency": "USD",
    "operationType": "win",
    "balanceType": "bonus"
  },
  "regulation": "default"
}
HTTP 200
[Captures]
bonusWinBalance: jsonpath "$.balance"
[Asserts]
jsonpath "$.balance" > {{afterBonusBetBalance}}
jsonpath "$.transactionId" == "bonus_win_001"
jsonpath "$.currency" == "USD"

## Test 10: Verify Final Multi-Balance State
POST {{WALLET_URL}}/v1/balances/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "regulation": "default"
}
HTTP 200
[Asserts]
jsonpath "$.main" == {{freeBetWinBalance}}
jsonpath "$.extraBalances.bonus" == {{bonusWinBalance}}
jsonpath "$.extraBalances.freeBet" == {{afterFreeBetBalance}}
jsonpath "$.currency" == "USD"

## Test 11: Rollback Transaction Test
POST {{WALLET_URL}}/v1/payments/rollback
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "rollback_test_001",
    "originalTransactionId": "bonus_win_001",
    "roundId": "bonus_round_002",
    "gameCode": "sw_mrmnky",
    "currency": "USD",
    "operationType": "rollback"
  },
  "regulation": "default"
}
HTTP 200
[Captures]
rollbackBalance: jsonpath "$.balance"
[Asserts]
jsonpath "$.balance" < {{bonusWinBalance}}
jsonpath "$.transactionId" == "rollback_test_001"
jsonpath "$.currency" == "USD"

## Test 12: Transfer Between Balances
POST {{WALLET_URL}}/v1/transfers/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "promo_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "PromoPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{bonusPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "transfer_001",
    "fromBalanceType": "bonus",
    "toBalanceType": "main",
    "amount": 50.00,
    "currency": "USD",
    "transferType": "bonus_conversion"
  },
  "regulation": "default"
}
HTTP 200
[Asserts]
jsonpath "$.transactionId" == "transfer_001"
jsonpath "$.status" == "success"
jsonpath "$.currency" == "USD"
