import * as Errors from "../../popErrors";
import * as TransactionModel from "../../models/transaction";
import { IncorrectPlayerIdOrToken } from "../../popErrors";
import { MoneyTransaction, MoneyTransactionRequest } from "../../scheme/pop/protocol";
import { isBrokenGameExists } from "../../service/brokenGames";
import * as RoundModel from "../../models/round";
import { InvalidGameType } from "../../popErrors";
import { isEmpty } from "../../utils/isEmpty";
import { Transaction } from "../../models/transaction";

const MONEY_TRANS_REQUIRED_FIELDS = ["transSeq", "transId", "transAmt", "transType", "transDateTime"];
const VALID_GAME_TYPES_ON_ROUND_STARTS = ["normal"];

export function validateRequestFields(req: any, ...fields: string[]) {
    for (const param of fields) {
        const value = req[param];
        if (isEmpty(value)) {
            throw new IncorrectPlayerIdOrToken();
        }
    }
}

export function validateMoneyTransactions(moneyTrans: MoneyTransaction[]) {
    // Empty money transaction valid for open cycle request

    if (moneyTrans === undefined) {
        throw new Errors.TransactionFailed();
    }

    if (moneyTrans.length === 0) {
        return;
    }

    for (const moneyTrx of moneyTrans) {
        validateMoneyTransObject(moneyTrx);
    }
}

export function validateMoneyTransObject(moneyTrans: MoneyTransaction) {

    const absentFields = MONEY_TRANS_REQUIRED_FIELDS.filter((it) =>
        moneyTrans[it] === undefined || moneyTrans[it] === null);

    if (absentFields && absentFields.length) {
        throw new Errors.IncorrectMoneyTransaction(absentFields);
    }

    if (moneyTrans["transAmt"] === 0) {
        throw new Errors.TransactionFailed();
    }
}

export function validateJackpot(request: MoneyTransactionRequest) {
    const jackpot = request.jackpot;

    // Check JP only at the last operation in cycle
    if (!request.gameCycleFinished) {
        return;
    }

    if (!jackpot) {
        return;
    }

    if (jackpot.contribAmt === 0 || jackpot.contribAmt < 0) {
        throw new Errors.IncompleteRequest();
    }

    if (jackpot.awardAmt < 0) {
        throw new Errors.IncompleteRequest();
    }
}

export function validateTransaction(type: string, trxId: string,
                                    customerBalance: number, amount: number = 0): Transaction {
    const transaction = TransactionModel.getById(trxId, type);

    if (transaction) {
        throw new Errors.TransactionFailed();
    }

    if (customerBalance + amount < 0) {
        throw new Errors.InsufficientBalance();
    }

    return transaction;
}

export function validateGameCycleId(gameCycleId, isFinalize = false) {
    if (!isFinalize && isBrokenGameExists(gameCycleId)) {
        throw new Errors.InvalidRequest("Game cycle reported as broken!");
    }
}

export function validateGameType(merchantId: string,
                                 customerId: string,
                                 gameCycleId: string,
                                 transDesc: string) {
    const round = RoundModel.findOne(merchantId, customerId, gameCycleId);

    // crutch to get gameType, transDesc format: "${gameType} win/bet"
    const [ gameType ] = transDesc.split(" ");

    if (!round && !VALID_GAME_TYPES_ON_ROUND_STARTS.includes(gameType)) {
        throw new InvalidGameType();
    }
}
