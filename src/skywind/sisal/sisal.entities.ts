import { Customer, Merchant } from "../models/merchant";
import { Ticket } from "../models/ticket";

export interface SisalExtraRequestData {
    merchant?: Merchant;
    customer?: Customer;
    tokenData?: Ticket;
}

export interface SisalWagerRequest {
    betAmount: number;
    betAmountFromPlayBonus?: number;
    gamePhaseId: string; // roundPID
    jackpotShareAmount?: number;
    timestamp: number;
    txId: string;
    jackpots?: SisalJackpot[];
}

export interface SisalEndWagerRequest {
    additionalJackpotWinAmount?: number;
    finalPhaseWager: boolean;
    finalWager: boolean;
    gamePhaseId: string; // roundPID
    jackpotWinAmount?: number;
    playBonusWinAmount?: number;
    timestamp: number;
    totalWinAmount: number;
    txId: string;
    jackpots?: SisalJackpot[];
}

export interface SisalCancelWagerRequest {
    gamePhaseId: string; // roundPID
    timestamp: number;
    txId: string;
}

export interface SisalBalanceResponse {
    balance: number;
    bonusBalance: number;
    playerId: number | string; // Sisal saves it as number
}

export interface SisalWagerResponse {
    gamePhaseId: number | string; // sisal round id
    inPhaseSeqNumber: number;
    playerGamePhaseId: number | string;
    playerGamePhaseWagerId: number | string;
    sessionBalance: SisalBalanceResponse;
    messageBody?: string;
    messageTitle?: string;
    messageBodyHtml?: string;
}

export interface SisalError {
    [field: string]: any;
}

export interface SisalHeaders {
    authorization: string; // sisalToken
    gameCode: string;
    xAuthorization?: string; // toHEX( SHA256( payloadRequest + sharedkey ))
    xClientId?: string;
}

export interface SisalJackpot {
    jackpotId: number;
    amount: number;
}
