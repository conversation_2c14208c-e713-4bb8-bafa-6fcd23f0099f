import { Application } from "express";
import { getApplication } from "../../skywind/sisal.server";
import { getDefaultCustomer } from "../../skywind/service/customer";
import { createCustomerSession } from "../../skywind/service/session";
import { SisalTestFixtures } from "./fixtures";

import { Customer, Merchant, setAll } from "../../skywind/models/merchant";
import * as Session from "../../skywind/models/session";
import * as TransactionModel from "../../skywind/models/transaction";
import { createActionError, RaiseType } from "../../skywind/models/customError";
import { createActionData } from "../../skywind/models/extraData";
import { createXAuthorization } from "../../skywind/sisal/sisal.middleware";

export class SisalBaseSpec {

    public urlPathToSessionBalance = gameSessionId => `/atena/${gameSessionId}/sessionBalance`;
    public urlPathToWager = gameSessionId => `/atena/${gameSessionId}/wager`;
    public urlPathToEndWager = gameSessionId => `/atena/${gameSessionId}/endwager`;
    public urlPathToCancelWager = gameSessionId => `/atena/${gameSessionId}/cancelwager`;
    public server: Application;
    public fixtures = new SisalTestFixtures();

    public ticketExpirationTimeDefault: number = 5 * 60 * 1000; // 5 min

    public async before() {
        this.server = await getApplication();
        Session.clearAll();
        TransactionModel.setAll({});

        setAll({ [this.fixtures.merchantCode]: this.fixtures.merchant });
        this.addPlayerToMerchant(this.fixtures.playerCode);

        // this.sessionId = createCustomerSession(merchant, this.customer);
    }

    public addPlayerToMerchant(name: string): Customer {
        const customer = getDefaultCustomer(name, "USD", true, 100);
        customer.bonusBalance = 0;
        this.fixtures.merchant.customers[customer.cust_id] = customer;

        return customer;
    }

    public createCustomError(httpStatus: number, errorCode: number, action: string) {
        createActionError(this.fixtures.merchantCode, this.fixtures.playerCode, action, RaiseType.AFTER, {
            http_response_status: httpStatus,
            errorCode: errorCode
        });
    }

    public createExtraData(data: object, action: string) {
        createActionData(this.fixtures.merchantCode, this.fixtures.playerCode, action, data);
    }

    public getXAuthorization(data: object) {
        return createXAuthorization(data, this.fixtures.merchant.merch_pwd);
    }
}
