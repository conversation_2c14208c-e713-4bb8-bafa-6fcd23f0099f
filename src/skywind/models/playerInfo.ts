import { lazy } from "@skywind-group/sw-utils";
import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes, fn, col } from "sequelize";
import { sequelize as db } from "../storage/db";

export interface PlayerInfo {
    id?: number;
    email?: string;
    playerCode: string;
    firstName?: string;
    lastName?: string;
    language?: string;
    company?: string;
    phone?: string;
}

export interface PlayerInfoDBInstance extends Model<
    InferAttributes<PlayerInfoDBInstance>,
    InferCreationAttributes<PlayerInfoDBInstance>
>, PlayerInfo {
}

const schema = {
    id: { field: "id", type: DataTypes.BIGINT, autoIncrement: true, primaryKey: true },
    email: { field: "email", type: DataTypes.STRING, unique: true, allowNull: false },
    playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
    firstName: { field: "first_name", type: DataTypes.STRING, allowNull: true },
    lastName: { field: "last_name", type: DataTypes.STRING, allowNull: true },
    company: { field: "company", type: DataTypes.STRING, allowNull: true },
    phone: { field: "phone", type: DataTypes.STRING, allowNull: true },
    language: { field: "language", type: DataTypes.STRING, allowNull: true },
    createdAt: { field: "created_at", type: DataTypes.DATE, allowNull: true, defaultValue: fn("NOW") },
    updatedAt: { field: "updated_at", type: DataTypes.DATE, allowNull: true, defaultValue: fn("NOW") },
};
export type IPlayerInfoModel = ModelStatic<PlayerInfoDBInstance>;
const model: IPlayerInfoModel = db.get().define<PlayerInfoDBInstance, PlayerInfo>(
    "player_infos",
    schema,
    {
        timestamps: false,
        freezeTableName: true,
        indexes: [
            {
                name: "idx_player_infos_email_unique",
                unique: true,
                fields: [fn("lower", col("email"))]
            }
        ]
    }
);
export const playerInfoModel = lazy<IPlayerInfoModel>(
    () => model);
