/**
 * ************ Requests ************
 */
export interface BaseRequest {
    apiKey: string;
    sessionId: string;
}

export interface BasePaymentRequest extends BaseRequest {
    clientTransactionId: string;
    clientGamePlayId: string;
}

/**
 * Authenticate Request
 */
export interface AuthenticateRequest {
    apiKey: string;
    authToken: string;
}

/**
 * Get Balance Request
 */
export interface AccountResponse extends BaseBalanceResponse {
    accountNumber: string;
    country: string;
    currency: string;
    responseCode: number;
    firstName: string;
    lastName: string;
}

export interface JackpotInfo {
    jackpotId: string;
    jackpotContribution: string;
}

/**
 * Bet request
 */
export interface UnsettledBetRequest extends BasePaymentRequest {
    betAmount: number;
    gameId: string;
    description: string;
    jackpotInfo: JackpotInfo;
    reSpin?: boolean;
}

export interface SettlePlaceBetRequest extends UnsettledBetRequest {
    settle: {
        winnings: number,
        jackpot: boolean
    };
}

export interface PlaceFreeBetRequest extends BasePaymentRequest {
    gameId: string;
    description: string;
    freeSpinInfo: {
        campaignId: string;
    };
}

export interface PlaceSettleFreeBetRequest extends PlaceFreeBetRequest {
    settle: {
        winnings: number,
        jackpot: boolean
    };
}

/**
 * Win request
 */
export interface SettleBet extends BasePaymentRequest {
    winnings: number;
    jackpot: boolean;
    jackpotInfo: JackpotInfo;
}

/**
 * ************ Responses ************
 */

export interface BaseResponse {
    responseCode: number;
}

export interface BaseBalanceResponse {
    balance: number;
}

export interface AuthenticateResponse extends BaseResponse {
    sessionId: string;
    maxStake?: number;
}

export interface AccountResponse extends BaseBalanceResponse {
    accountNumber: string;
    country: string;
    currency: string;
    responseCode: number;
    firstName: string;
    lastName: string;
}

export interface BasePaymentResponse extends BaseResponse, BaseBalanceResponse {
    transactionId: string;
    duplicate: boolean;
}

export interface ErrorResponse extends BaseResponse {
    message: string;
}

/**
 * ************ Other ************
 */

export enum BET_VICTOR_MOCK_TYPE {
    /**
     * Mock engine with fully manual token setup
     */
    MockEngine,
    /**
     * Mock engine with semi-manual token setup
     */
    MockEnginePredefined,
}

export enum BET_VICTOR_JURISDICTION {
    UK = "UK",
    GIB = "GIB",
    IOM = "IOM"
}

// normal player token format is "betVictor_{operatorId}_{jurisdiction}_{currency}_{country}"
export enum BET_VICTOR_TOKEN {
    NORMAL = "betVictor"
}

export enum BET_VICTOR_ACTION {
    AUTHENTICATE = "authenticate",
    BET = "place-bet",
    WIN = "settle-bet",
    CANCEL = "rollback-bet",
    GET_BALANCE = "account-details",
    ADDITIONAL_WINNINGS = "additional-winnings",
    ADDITIONAL_BET = "additional-bet",
    PROMO_CREDIT = "promo-credit"
}
