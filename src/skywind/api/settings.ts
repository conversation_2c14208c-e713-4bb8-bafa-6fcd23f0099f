import { Router, Request, Response, NextFunction } from "express";
import { settings } from "../models/settings";

const router: Router = Router();

router.get("/settings",
    (req: Request, res: Response, next: NextFunction) => {
        res.send(settings);
    });

router.patch("/settings",
    (req: Request, res: Response, next: NextFunction) => {
        const aroundAmount = req.body.aroundAmount;
        if (aroundAmount !== undefined) {
            if (aroundAmount !== true && aroundAmount !== false) {
                res.status(400).send("aroundAmount is missing");
                return;
            }
            settings.aroundAmount = aroundAmount;
        }

        const decreaseLoad = req.body.decreaseLoad;
        if (decreaseLoad !== undefined) {
            if (decreaseLoad !== true && decreaseLoad !== false) {
                res.status(400).send("decreaseLoad is missing");
                return;
            }
            settings.decreaseLoad = decreaseLoad;
        }

        const notSaveAnyData = req.body.notSaveAnyData;
        if (notSaveAnyData !== undefined) {
            if (notSaveAnyData !== true && notSaveAnyData !== false) {
                res.status(400).send("notSaveAnyData is missing");
                return;
            }
            settings.notSaveAnyData = notSaveAnyData;
        }

        const amount = req.body.amount;
        if (amount !== undefined) {
            if (typeof amount !== "number" || +amount < 0) {
                res.status(400).send("amount is missing");
                return;
            }
            settings.amount = amount;
        }

        const addNickname = req.body.addNickname;
        if (addNickname !== undefined) {
            if (addNickname !== true && addNickname !== false) {
                res.status(400).send("addNickname is missing");
                return;
            }
            settings.addNickname = addNickname;
        }

        const useTestPlayers = req.body.useTestPlayers;
        if (useTestPlayers !== undefined) {
            if (useTestPlayers !== true && useTestPlayers !== false) {
                res.status(400).send("useTestPlayers is missing");
                return;
            }
            settings.useTestPlayers = useTestPlayers;
        }

        res.send(settings);
    });

export default router;
