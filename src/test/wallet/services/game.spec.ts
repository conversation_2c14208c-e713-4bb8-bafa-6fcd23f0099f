import { BaseSpec, MOCK_ARG } from "../../base.spec";
import { GameService, getGameService } from "../../../skywind/wallet/services/game";
import { testing } from "@skywind-group/sw-utils";
import { SEAMLESS_ERROR_CODE } from "../../../skywind/errors";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import status200 = testing.status200;

@suite
class GameSpec extends BaseSpec {
    public service: GameService;

    public before() {
        this.service = getGameService(this.apiService);
    }

    @test
    public async checkIfDisable_offersIsPassedToStartGameToken() {
        const createGameUrlParams = [
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                lobby: "http://lobby.com/v1/version?hello=true"
            }] as const;
        const validateTicketBody = {
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
        };

        BaseSpec.mocker.post(/validate_ticket/, status200({ ...validateTicketBody, disable_offers: true }));
        let result = await this.service.createGameUrl(...createGameUrlParams);
        expect(result.tokenData.disablePlayerPhantomFeatures).to.equal(true);

        BaseSpec.mocker.post(/validate_ticket/, status200({ ...validateTicketBody, disable_offers: false }));
        result = await this.service.createGameUrl(...createGameUrlParams);
        expect(result.tokenData.disablePlayerPhantomFeatures).to.equal(false);
    }

    @test
    public async testCreateFunGameUrl() {
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false
        }));

        const result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                lobby: "http://lobby.com/v1/version?hello=true"
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/validate_ticket");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            ticket: this.fixtures.initRequest.ticket,
            ip: this.fixtures.initRequest.ip
        });

        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                country: undefined,
                currency: "USD",
                gameCode: "sw_mrmnky",
                gameGroup: undefined,
                ipmSessionId: "test",
                language: "en",
                merchantCode: "seamless",
                merchantType: "seamless",
                playerCode: "chebureck",
                playmode: PlayMode.FUN,
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                test: false,
                hasLobby: true,
                siteUrl: "http://lobby.com",
                oldGameCode: "sw_mrmnky"
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com/v1/version?hello=true",
                merch_login_url: "http://merch_login_url.com",
                playmode: PlayMode.FUN
            }
        });
    }

    @test
    public async testCreateFunAnonymousGameUrl() {
        const result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            this.fixtures.initRequestFunMode
        );

        expect(this.seamlessAPISpy.post.called).to.be.false;

        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                gameCode: "sw_mrmnky",
                language: "en",
                merchantCode: "seamless",
                merchantType: "seamless",
                playerCode: result.tokenData.playerCode,
                playmode: PlayMode.FUN,
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                gameGroup: undefined,
                currency: undefined
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: PlayMode.FUN
            }
        });
    }

    @test
    public async testCreateRealGameUrl() {
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false
        }));

        const result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                country: undefined,
                currency: "USD",
                gameCode: "sw_mrmnky",
                gameGroup: undefined,
                ipmSessionId: "test",
                language: "en",
                merchantCode: "seamless",
                merchantType: "seamless",
                playerCode: "chebureck",
                playmode: "real",
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                test: false,
                hasLobby: true,
                siteUrl: "http://lobby.com",
                oldGameCode: "sw_mrmnky"
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: "real"
            }
        });
    }

    @test
    public async testCreateUrlFromPreviousToken() {
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false
        }));

        const result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...{
                    ...this.fixtures.initRequest,
                    language: undefined
                },
                playmode: PlayMode.BNS,
                previousStartTokenData: {
                    country: undefined,
                    currency: "USD",
                    playerCode: "chebureck",
                    language: "en",
                    brandId: 1,
                    playmode: PlayMode.REAL,
                    merchantType: this.fixtures.merchantInfo.type,
                    merchantCode: this.fixtures.merchantInfo.code,
                    providerGameCode: this.fixtures.provideGameCode,
                    providerCode: this.fixtures.providerCode,
                    gameCode: this.fixtures.initRequest.gameCode,
                    ipmSessionId: "test",
                    test: false,
                    siteUrl: "http://lobby.com",
                    disablePlayerPhantomFeatures: true
                }
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.false;
        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                country: undefined,
                currency: "USD",
                gameCode: "sw_mrmnky",
                gameGroup: undefined,
                ipmSessionId: "test",
                language: "en",
                merchantCode: "seamless",
                merchantType: "seamless",
                playerCode: "chebureck",
                playmode: "bns",
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                test: false,
                siteUrl: "http://lobby.com",
                oldGameCode: "sw_mrmnky",
                disablePlayerPhantomFeatures: true
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: "bns"
            }
        });
    }

    @test
    public async testRefreshSessionForNewGame() {
        BaseSpec.mocker.post(/refresh_session/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            new_cust_session_id: "newSession"
        }));

        const result = await this.service.createGameUrl(
            {
                ...this.fixtures.merchantInfo,
                params: {
                    ...this.fixtures.merchantInfo.params,
                    refreshSessionForNewGame: true
                }
            },
            this.fixtures.newGameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...{ ...this.fixtures.initRequest, language: undefined, oldGameCode: "sw_mrmnky" },
                playmode: PlayMode.BNS,
                previousStartTokenData: {
                    country: undefined,
                    currency: "USD",
                    playerCode: "chebureck",
                    language: "en",
                    brandId: 1,
                    playmode: PlayMode.REAL,
                    merchantType: this.fixtures.merchantInfo.type,
                    merchantCode: this.fixtures.merchantInfo.code,
                    providerGameCode: this.fixtures.provideGameCode,
                    providerCode: this.fixtures.providerCode,
                    gameCode: this.fixtures.provideGameCode,
                    ipmSessionId: "test",
                    test: false,
                    siteUrl: "http://lobby.com"
                }
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/refresh_session");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            cust_id: "chebureck",
            old_game_code: this.fixtures.provideGameCode,
            new_game_code: this.fixtures.newGameCode,
            cust_session_id: "test"
        });

        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                country: undefined,
                currency: "USD",
                gameCode: "sw_mrmnky_new",
                gameGroup: undefined,
                ipmSessionId: "newSession",
                language: "en",
                merchantCode: "seamless",
                merchantType: "seamless",
                playerCode: "chebureck",
                playmode: "bns",
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                test: false,
                siteUrl: "http://lobby.com",
                oldGameCode: "sw_mrmnky_new"
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: "bns"
            }
        });
    }

    @test
    public async testCreateUrlForLobby() {
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false
        }));

        const result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                customerSessionId: "qwertyu",
                playmode: PlayMode.REAL,
                playerCode: "chebureck"
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.false;
        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                gameCode: "sw_mrmnky",
                merchantCode: "seamless",
                merchantType: "seamless",
                playmode: "real",
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                ipmSessionId: "qwertyu",
                siteUrl: "http://lobby.com",
                playerCode: "chebureck",
                oldGameCode: "sw_mrmnky"
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: PlayMode.REAL
            }
        });
    }

    @test
    public async testRefreshSessionForLobby() {
        BaseSpec.mocker.post(/refresh_session/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            new_cust_session_id: "newSession"
        }));

        const result = await this.service.createGameUrl(
            {
                ...this.fixtures.merchantInfo,
                params: {
                    ...this.fixtures.merchantInfo.params,
                    refreshSessionForNewGame: true
                }
            },
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                gameCode: undefined,
                playerCode: "chebureck",
                customerSessionId: "qwertyu",
                playmode: PlayMode.REAL
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                gameCode: "sw_mrmnky",
                merchantCode: "seamless",
                merchantType: "seamless",
                playmode: "real",
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                ipmSessionId: "newSession",
                siteUrl: "http://lobby.com",
                playerCode: "chebureck",
                oldGameCode: "sw_mrmnky"
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: PlayMode.REAL
            }
        });
    }

    @test
    public async testCreateUrlForLobbyWithoutCustomerSessionId() {
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false
        }));

        const result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                ipmSessionId: "qwertyu",
                playmode: PlayMode.REAL,
                playerCode: "chebureck"
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.false;
        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                gameCode: "sw_mrmnky",
                merchantCode: "seamless",
                merchantType: "seamless",
                playmode: "real",
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                ipmSessionId: "qwertyu",
                siteUrl: "http://lobby.com",
                playerCode: "chebureck",
                oldGameCode: "sw_mrmnky"
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: PlayMode.REAL
            }
        });
    }

    @test
    public async testTerminalLogin() {
        BaseSpec.mocker.post(/validate_terminal_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false
        }));

        const ticket = "qwertyu1234567ui";
        const result = await this.service.loginTerminalPlayer(
            this.fixtures.merchantInfo,
            { ticket }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.URL]).to.be.equal("api/validate_terminal_ticket");
        expect(this.seamlessAPISpy.post.lastCall.args[MOCK_ARG.BODY]).to.be.deep.equal({
            merch_id: this.fixtures.merchantInfo.code,
            merch_pwd: this.fixtures.merchantInfo.params.password,
            ticket
        });

        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                country: undefined,
                currency: "USD",
                gameCode: undefined,
                providerCode: undefined,
                providerGameCode: undefined,
                gameGroup: undefined,
                ipmSessionId: "test",
                language: undefined,
                merchantCode: "seamless",
                merchantType: "seamless",
                playerCode: "chebureck",
                test: false,
                hasLobby: false,
                siteUrl: undefined,
                oldGameCode: undefined
            },
            sessionId: "test"
        });
    }

    @test
    public async testCreateRealGameUrlFor360() {
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            nickname: "chebureckNickname"
        }));

        const result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(this.seamlessAPISpy.post.calledOnce).to.be.true;
        expect(result).to.be.deep.equal({
            tokenData: {
                brandId: 1,
                country: undefined,
                currency: "USD",
                gameCode: "sw_mrmnky",
                gameGroup: undefined,
                ipmSessionId: "test",
                language: "en",
                merchantCode: "seamless",
                merchantType: "seamless",
                playerCode: "chebureck",
                playmode: "real",
                providerCode: "skywind",
                providerGameCode: "sw_mrmnky",
                test: false,
                hasLobby: true,
                siteUrl: "http://lobby.com",
                oldGameCode: "sw_mrmnky",
                nickname: "chebureckNickname"
            },
            urlParams: {
                cashier: "http://cashier.com",
                language: "en",
                lobby: "http://lobby.com",
                merch_login_url: "http://merch_login_url.com",
                playmode: "real"
            }
        });
    }

    @test
    public async testRciAndRceValues() {
        // rci received as number, rce not received
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: 60
        }));

        let result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.equal(60);
        expect(result.tokenData.rce).to.equal(0);

        // rci received as string number, rce not received
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: "60"
        }));

        result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.equal(60);
        expect(result.tokenData.rce).to.equal(0);

        // rci and rce received as number
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: 60,
            rce: 10
        }));

        result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.equal(60);
        expect(result.tokenData.rce).to.equal(10);

        // rci and rce received as string numbers
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: "60",
            rce: "10"
        }));

        result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.equal(60);
        expect(result.tokenData.rce).to.equal(10);

        // rci received as number, rce received as invalid string
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: 60,
            rce: "invalid"
        }));

        result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.equal(60);
        expect(result.tokenData.rce).to.equal(0);

        // rci received as invalid string, rce received as number
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: "invalid",
            rce: 30
        }));

        result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.be.undefined;
        expect(result.tokenData.rce).to.be.undefined;

        // rci received as number, rce received as empty string
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: 60,
            rce: ""
        }));

        result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.equal(60);
        expect(result.tokenData.rce).to.equal(0);

        // rci received as empty string, rce received as number
        BaseSpec.mocker.post(/validate_ticket/, status200({
            error_code: SEAMLESS_ERROR_CODE.SUCCESS,
            cust_session_id: "test",
            cust_id: "chebureck",
            currency_code: "USD",
            test_cust: false,
            rci: "",
            rce: 10
        }));

        result = await this.service.createGameUrl(
            this.fixtures.merchantInfo,
            this.fixtures.initRequest.gameCode,
            this.fixtures.providerCode,
            this.fixtures.provideGameCode,
            {
                ...this.fixtures.initRequest,
                playmode: PlayMode.REAL
            }
        );

        expect(result.tokenData.rci).to.be.undefined;
        expect(result.tokenData.rce).to.be.undefined;
    }
}
