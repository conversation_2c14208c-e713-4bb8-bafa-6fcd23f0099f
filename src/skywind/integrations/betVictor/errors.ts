import { BaseErrorToLog, ErrorInfoToLog } from "../../errors";
import { BET_VICTOR_JURISDICTION } from "./entities";

export enum BET_VICTOR_RETURN_CODE {
    SUCCESS = 0,
    INTERNAL_ERROR = 1,
    VALIDATION_ERROR = 100,
    API_KEY_NOT_FOUND = 101,
    SESSION_EXPIRED = 102,
    SESSION_NOT_FOUND = 103,
    INVALID_BET_AMOUNT = 104,
    INVALID_GAME_ID = 107,
    INVALID_GAME_PLAY_ID = 108,
    INVALID_LOSE_AMOUNT = 109,
    INSUFFICIENT_FUNDS = 110,
    GAME_ALREADY_SETTLED = 111,
    ACCOUNT_CLOSED = 112,
    ACCOUNT_NOT_FOUND = 113,
    INVALID_ROLLBACK_REQUEST = 114,
    GAME_BLOCKED = 115,
    GAME_MAPPING_NOT_AVAILABLE = 116,
    API_VERSION_NOT_ALLOWED = 117,
    INVALID_PROMO_AMOUNT = 118,
    PROMO_CREDIT_NOT_ALLOWED = 119
}

export enum BET_VICTOR_RETURN_CODE_DESCRIPTION {
    SUCCESS = 0,
    INTERNAL_ERROR = "Generic Internal Errors",
    VALIDATION_ERROR = "Missing fields from the request, violations of null constraints or value length constraints",
    API_KEY_NOT_FOUND = "",
    SESSION_EXPIRED = "",
    SESSION_NOT_FOUND = "",
    INVALID_BET_AMOUNT = "Bet amount for placeBet and placeAndSettleBet is negative",
    INVALID_GAME_ID = "",
    INVALID_GAME_PLAY_ID = "",
    INVALID_LOSE_AMOUNT = "Lose amount higher than total bet amount",
    INSUFFICIENT_FUNDS = "",
    GAME_ALREADY_SETTLED = "",
    ACCOUNT_CLOSED = "",
    ACCOUNT_NOT_FOUND = "",
    INVALID_ROLLBACK_REQUEST = "",
    GAME_BLOCKED = "",
    GAME_MAPPING_NOT_AVAILABLE = "",
    API_VERSION_NOT_ALLOWED = "",
    INVALID_PROMO_AMOUNT = "Amount for promo credit must be greater than zero",
    PROMO_CREDIT_NOT_ALLOWED = "Promo crediting not allowed for provider"
}

/**
 * Base
 */

export class BetVictorError extends Error implements BaseErrorToLog {
    constructor(responseCode: number,
                message: string,
                responseStatus: number = 400,
                customField: string = null) {
        super(message);

        this.responseCode = responseCode;
        this.message = message;
        this.responseStatus = responseStatus;
        this.customField = customField;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.responseCode,
                message: this.message,
                stack: this.stack
            }
        };
    }

    public responseCode: number;
    public message: string;
    public responseStatus: number;
    public customField: string;
}

/**
 * End of Base
 */

/**
 * MOCK Custom
 */

export class BetVictorError404 extends BetVictorError {
    public responseStatus: number;
    constructor() {
        super(773, "NotFound");
        this.responseStatus = 404;
    }
}

export class EmptyFieldError extends BetVictorError {
    constructor(message: string) {
        super(774, `MockErrorEmptyField ${message}`);
    }
}

export class OperatorNotSetError extends BetVictorError {
    constructor() {
        super(774, "OPERATOR_NOT_SET");
    }
}

export class JurisdictionNotSetError extends BetVictorError {
    constructor() {
        super(775,
            "Jurisdiction not set. please setup jurisdiction either in token" +
            "launchToken{anyString}_{merchantCode}_{jurisdiction}' or in customer settings (user settings)");
    }
}

export class InvalidJurisdictionError extends BetVictorError {
    constructor() {
        super(776,
            "Jurisdiction is invalid. Possible values: " +
            `${BET_VICTOR_JURISDICTION.UK}, ${BET_VICTOR_JURISDICTION.GIB}, ${BET_VICTOR_JURISDICTION.IOM}`);
    }
}

export class WrongTokenFormatError extends BetVictorError {
    constructor() {
        super(
            777,
            `Wrong token format, token must contains two '_' literals.
            "Please use right format like 'betVictor{anyString}_{merchantCode}_{jurisdiction}'`);
    }
}

export class OperatorNotFoundError extends BetVictorError {
    constructor() {
        super(778, "OPERATOR_NOT_FOUND");
    }
}

/**
 * End of Custom
 */

/**
 * Return Codes
 */

export class InternalError extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INTERNAL_ERROR,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INTERNAL_ERROR, 500);
    }
}

export class ValidationError extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.VALIDATION_ERROR,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.VALIDATION_ERROR);
    }
}

export class ApiKeyNotFound extends BetVictorError {
    constructor(msg = null) {
        super(BET_VICTOR_RETURN_CODE.API_KEY_NOT_FOUND,
            `${BET_VICTOR_RETURN_CODE_DESCRIPTION.API_KEY_NOT_FOUND}. ${msg}`);
    }
}

export class SessionExpired extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.SESSION_EXPIRED,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.SESSION_EXPIRED);
    }
}

export class SessionNotFound extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.SESSION_NOT_FOUND,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.SESSION_NOT_FOUND);
    }
}

export class InvalidBetAmount extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INVALID_BET_AMOUNT,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_BET_AMOUNT);
    }
}

export class InvalidGameId extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INVALID_GAME_ID,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_GAME_ID);
    }
}

export class InvalidGamePlayId extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INVALID_GAME_PLAY_ID,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_GAME_PLAY_ID);
    }
}

export class InvalidLoseAmount extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INVALID_LOSE_AMOUNT,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_LOSE_AMOUNT);
    }
}

export class InsufficientFunds extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INSUFFICIENT_FUNDS,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INSUFFICIENT_FUNDS);
    }
}

export class GameAlreadySettled extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.GAME_ALREADY_SETTLED,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.GAME_ALREADY_SETTLED);
    }
}

export class AccountClosed extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.ACCOUNT_CLOSED,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.ACCOUNT_CLOSED);
    }
}

export class AccountNotFound extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.ACCOUNT_NOT_FOUND,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.ACCOUNT_NOT_FOUND);
    }
}

export class InvalidRollbackRequest extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INVALID_ROLLBACK_REQUEST,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_ROLLBACK_REQUEST);
    }
}

export class GameBlocked extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.GAME_BLOCKED,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.GAME_BLOCKED);
    }
}

export class GameMappingNotAvailable extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.GAME_MAPPING_NOT_AVAILABLE,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.GAME_MAPPING_NOT_AVAILABLE);
    }
}

export class ApiVersionNotAllowed extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.API_VERSION_NOT_ALLOWED,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.API_VERSION_NOT_ALLOWED);
    }
}

export class InvalidPromoAmount extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.INVALID_PROMO_AMOUNT,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.INVALID_PROMO_AMOUNT);
    }
}

export class PromoCreditNotAllowed extends BetVictorError {
    constructor() {
        super(BET_VICTOR_RETURN_CODE.PROMO_CREDIT_NOT_ALLOWED,
            BET_VICTOR_RETURN_CODE_DESCRIPTION.PROMO_CREDIT_NOT_ALLOWED);
    }
}

/**
 * End of Return codes
 */

export function getBetVictorErrorInstance(code: number) {
    switch (code) {
        case BET_VICTOR_RETURN_CODE.INTERNAL_ERROR:
            return new InternalError();
        case BET_VICTOR_RETURN_CODE.VALIDATION_ERROR:
            return new ValidationError();
        case BET_VICTOR_RETURN_CODE.API_KEY_NOT_FOUND:
            return new ApiKeyNotFound();
        case BET_VICTOR_RETURN_CODE.SESSION_EXPIRED:
            return new SessionExpired();
        case BET_VICTOR_RETURN_CODE.SESSION_NOT_FOUND:
            return new SessionNotFound();
        case BET_VICTOR_RETURN_CODE.INVALID_BET_AMOUNT:
            return new InvalidBetAmount();
        case BET_VICTOR_RETURN_CODE.INVALID_GAME_ID:
            return new InvalidGameId();
        case BET_VICTOR_RETURN_CODE.INVALID_GAME_PLAY_ID:
            return new InvalidGamePlayId();
        case BET_VICTOR_RETURN_CODE.INVALID_LOSE_AMOUNT:
            return new InvalidLoseAmount();
        case BET_VICTOR_RETURN_CODE.INSUFFICIENT_FUNDS:
            return new InsufficientFunds();
        case BET_VICTOR_RETURN_CODE.GAME_ALREADY_SETTLED:
            return new GameAlreadySettled();
        case BET_VICTOR_RETURN_CODE.ACCOUNT_CLOSED:
            return new AccountClosed();
        case BET_VICTOR_RETURN_CODE.ACCOUNT_NOT_FOUND:
            return new AccountNotFound();
        case BET_VICTOR_RETURN_CODE.INVALID_ROLLBACK_REQUEST:
            return new InvalidRollbackRequest();
        case BET_VICTOR_RETURN_CODE.GAME_BLOCKED:
            return new GameBlocked();
        case BET_VICTOR_RETURN_CODE.GAME_MAPPING_NOT_AVAILABLE:
            return new GameMappingNotAvailable();
        case BET_VICTOR_RETURN_CODE.API_VERSION_NOT_ALLOWED:
            return new ApiVersionNotAllowed();
        case BET_VICTOR_RETURN_CODE.INVALID_PROMO_AMOUNT:
            return new InvalidPromoAmount();
        case BET_VICTOR_RETURN_CODE.PROMO_CREDIT_NOT_ALLOWED:
            return new PromoCreditNotAllowed();
        default:
            throw new Error("Unknown BetVictor error code");
    }
}
