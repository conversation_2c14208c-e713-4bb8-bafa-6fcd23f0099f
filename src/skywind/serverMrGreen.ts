"use strict";

import createApplication from "../config/express";
import { defineRoutes } from "./api/routers";
import { Application } from "express";
import { Server } from "http";
import * as fs from "fs";
import logger from "./utils/logger";
import config from "./config";
import { INTEGRATION_TYPE } from "../config/integrationType";

const log = logger(INTEGRATION_TYPE.MR_GREEN);
const version = fs.readFileSync(__dirname + "/version", "utf8");

export default async (port = 8005): Promise<Server> => {
    const app = await getApplication();

    return new Promise<Server>((resolve) => {
        const server: Server = require("http")
            .createServer(app)
            .listen(port, null, () => {
                log.info("MrGreen mock server listening on " + port);
                log.info("AppVersion: " + version);
                resolve(server);
            });
    });
};

let app: Application;

export async function getApplication(): Promise<Application> {
    config.serverName = INTEGRATION_TYPE.MR_GREEN;
    if (!app) {
        app = createApplication();
        await defineRoutes(app, INTEGRATION_TYPE.MR_GREEN, log);
    }
    return app;
}
