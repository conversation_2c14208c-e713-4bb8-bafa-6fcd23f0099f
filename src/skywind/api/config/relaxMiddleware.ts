import { RelaxSessionExpiredError, RelaxUnhandledError } from "../../errorRelax";
import { authenticate as authBase, authenticateCustomer as authCustomerBase} from "./middleware";

export const authenticate = authBase(
    (req) => req.params.partnerId,
    new RelaxUnhandledError("Merchant not found"));

export const authenticateCustomer = authCustomerBase(
    (req) => ({
        code: req.body.playerid,
        sessionId: req.body.sessionid
    }), new RelaxSessionExpiredError());
