import { Customer, Merchant } from "../models/merchant";
import { Ticket } from "../models/ticket";

export interface GANExtraRequestData {
    parsedRequest?: any;
    merchant?: Merchant;
    customer?: Customer;
    tokenData?: Ticket;
}

export interface GANRequest {
    Auth: {
        _user: string;
        _secret: string;
        _token?: string;
    };
    Channel: {
        _ref: string;
    };
}

export interface GANResponse {
    _status?: GAN_RESPONSE_STATUS; // should be in all responses?
}

export enum GAN_RESPONSE_STATUS {
    SUCCESS = "SUCCESS",
    FAILURE = "FAILURE",
}

export interface GANBalance {
    _total: number;
    _cash: number;
    _restrictedCash: number;
    _bonus: number;
}

export interface GANGetPlayerInfoRequest extends GANRequest {
    Player: {
        _playerGuid: string;
        _includeBalance?: boolean;
        _includePendingBuyIns?: boolean;
        _includeLimits?: boolean;
    };
}

export interface GANGetPlayerInfoResponse extends GANResponse {
    Player: {
        _playerGuid: string;
        _partner?: string;
        _alias: string;
        _country: string;
        _currency: string;
        _locale: string; // language

        Balance: GANBalance;
    };
}

export enum GAN_CONTEST_STATUS {
    PENDING = "PENDING",
    OPEN = "OPEN",
    CLOSED = "CLOSED",
}

export enum GAN_AAMS_SESSION_STATUS {
    SESSION_CREATION_REQUESTED = "SESSION_CREATION_REQUESTED",
    SESSION_CREATION_FAILED = "SESSION_CREATION_FAILED",
    SESSION_CREATED = "SESSION_CREATED",
    BUY_IN_REQUESTED = "BUY_IN_REQUESTED",
    BOUGHT_IN = "BOUGHT_IN",
    SESSION_TERMINATED = "SESSION_TERMINATED",
}

export interface GANContestResponse extends GANResponse {
    Contest: {
        _contestRef: string;
        _gameTypeRef: string;
        _playerGuid: string;
        _state: GAN_CONTEST_STATUS;
        _created: string;

        AamsSession: {
            _id: string;
            _state: GAN_AAMS_SESSION_STATUS;
        };
        AamsParticipationRight?: {
            _id: string;
        };
    };
}

export interface GANEnsureContestForPlayerRequest extends GANRequest {
    Contest: {
        _newContestRef: string;
        _gameTypeRef: string;
        _playerGuid: string;
        _createIfRequired: boolean;
        _launchLocation?: string;
        _includePurseBalance?: boolean;
        _includeExternalReferences?: boolean;
        _includePendingBuyIns?: boolean;
        _includeLimits?: boolean;
    };
}

export interface GANEnsureContestForPlayerResponse extends GANContestResponse {}

export interface GANCloseContestRequest extends GANRequest {
    Contest: {
        _contestRef: string;
        _close: boolean;
    };
}

export interface GANCloseContestResponse extends GANContestResponse {}

export enum GAN_PLAY_STATE {
    OPEN = "OPEN",
    COMPLETED = "COMPLETED",
    VOIDED = "VOIDED",
}

export interface GANPaymentRequest<A = CommonPaymentRequestAction> extends GANRequest {
    Play: {
        _playRef: string; // roundId ???
        _playerGuid: string;
        _contestRef: string;
        _finish?: boolean; //
        _includeBalance?: boolean;
        Action: A;
    };
}

export interface GANPaymentResponse {
    Play: {
        _playRef: string; // roundId ???
        _playerGuid: string;
        _contestRef: string;
        _staked?: number;
        _won?: number;
        _state: GAN_PLAY_STATE;
        _completed?: string;

        Balance?: GANBalance;
    };
}

export enum GAN_PAYMENT_TYPE {
    BET = "STAKE",
    WIN = "WIN"
}

export interface CommonPaymentRequestAction {
    _ref: string; // trxId ???
    _type: GAN_PAYMENT_TYPE;
    _currency: string;
    _amount: number;
    _void: boolean;
    _description?: string;
}

export interface GANBetPaymentRequestAction {
    _ref: string; // trxId ???
    _type: GAN_PAYMENT_TYPE.BET;
    _currency: string;
    _amount: number;
    _description?: string;
}

export interface GANWinPaymentRequestAction {
    _ref: string;
    _type: GAN_PAYMENT_TYPE.WIN;
    _currency: string;
    _amount: number;
    _description?: string;
}

export interface GANRollbackPaymentRequestAction {
    _ref: string; // GANBetPaymentRequestAction._ref
    _void: boolean;
    _description?: string;
}

export interface GANErrorResponse extends GANResponse {
    Error: {
        _code: number;
        _message: string;
    };
}
