import { Customer } from "../models/merchant";

export enum RELAX_JURISDICTION {
    UK = "UK",
    MT = "MT",
    SE = "SE"
}

export interface VerifyTokenRequest {
    channel: string;
    clientid: string;
    token: string;
    gameref: string;
    partnerid: string;
    ip: string;
}

export interface VerifyTokenPromotion {
    gameref: string;
    promotionid: number;
    txid: string;
    promocode: string;
}

export interface VerifyTokenOperatorBetSettings {
    minimumbet?: number;
    defaultbet?: number;
    maximumbet?: number;
}

export interface VerifyTokenResponse {
    playerid: string;
    customerid: string;
    username: string;
    locale: string;
    countrycode: string;
    gender: number;
    currency: string;
    birthdate: string;
    lastlogin: string;
    jurisdiction: string;
    balance: number;
    sessionid: string;
    partnerid: string;
    currencyrate: number;
    promotions?: VerifyTokenPromotion[];
    operatorbetsettings?: VerifyTokenOperatorBetSettings;
}

export interface BalanceRequest {
    currency: string;
    gameref: string;
    playerid: string;
    sessionid: string;
}

export interface BalanceResponse {
    balance: number;
    currency: string;
    sessionid: string;
}

export interface PaymentRequest {
    playerid: string;
    roundid: string;
    gameref: string;
    channel: string;
    currency: string;
    clientid: string;
    txid: string;
    sessionid: number;
    amount: number;
    txtype: string;
    ended: boolean;
}

export interface PaymentResponse {
    balance?: number;
    txid: string; // Platform's transactionid.
    relaxid: string; // Unique transactionid from Relax Gaming.
    sessionid: number;
}

export interface RollbackRequest {
    amount: number;
    currency: string;
    gameref: string;
    originaltxid: string;
    playerid: string;
    roundid: string;
    sessionid: number;
    txid: string;
}

export interface RollbackResponse {
    relaxtxid: string;
    sessionid: number;
    txid: string;
}

export enum RELAX_ACTION {
    VERIFY = "verifytoken",
    DEBIT = "debit",
    CREDIT = "credit",
    ROLLBACK = "rollback",
    GET_BALANCE = "getbalance",
}

export interface RelaxCustomer extends Customer {
    promotions: VerifyTokenPromotion[];
    operatorbetsettings?: VerifyTokenOperatorBetSettings;
}

export interface AckPromotionRequest {
    promotions: AckPromotion[];
}

export interface AckPromotion {
    data: {
        channel: string;
        freespinid: string;
    };
    playerid: string;
    promotionid: number;
    txid: string;
}

export interface AckPromotionResponse {
    promotion_statuses: { status: string, txid: string }[];
}
