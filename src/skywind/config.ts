"use strict";

const config = {

    environment: process.env.NODE_ENV || "development",

    logLevel: process.env.LOG_LEVEL || "info",

    logParams: {
        secureKeys: [
            "merch_pwd"
        ],
        allowedHeaders: process.env.ALLOWED_HEADERS_TO_LOG || "",
    },

    graylog: {
        host: process.env.GRAYLOG_HOST || undefined,
        port: +process.env.GRAYLOG_PORT || undefined,
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4003,
    },

    currencyUnitMultiplier: 100,

    expirationTime: {
        ticket: +process.env.IPM_MOCK_TICKET_EXPIRATION_MS || 5 * 60 * 1000, // 5 min,
        terminalTicket: +process.env.IPM_MOCK_TERMINAL_TICKET_EXPIRATION_MS || 60 * 60 * 1000, // 5 min
        customerSession: +process.env.IPM_MOCK_SESSION_EXPIRATION_MS || 5 * 60 * 1000, // 5 min
        terminalSession: +process.env.IPM_MOCK_TERMINAL_SESSION_EXPIRATION_MS  || 60 * 60 * 1000, // 5 min
    },

    mockLatency: +process.env.IPM_MOCK_LATENCY || 0,
    mockLatencySpreading: +(process.env.IPM_MOCK_LATENCY_SPREADING) || (+process.env.IPM_MOCK_LATENCY || 0) / 10,
    mockLatencyPathMask: process.env.IPM_MOCK_LATENCY_PATH_MASK || "/api/*",

    defaultSettings: {
        decreaseLoad: process.env.DECREASE_LOAD  === "true", // false
        notSaveAnyData: process.env.NOT_SAVE_ANY_DATA  === "true", // false
        amount: +process.env.MOCK_SETTINGS_CUSTOMER_AMOUNT || 1000000,
        addNickname: process.env.ADD_NICKNAME === "true",
        useTestPlayers: process.env.USE_TEST_PLAYERS === "true"
    },

    serverName: "ipm",

    bodyParserJsonLimit: +process.env.BODY_PARSER_JSON_LIMIT || 5242880,
    bodyParserUrlLimit: +process.env.BODY_PARSER_URL_LIMIT || 5242880,
    compressionThreshold: +process.env.COMPRESSION_THRESHOLD || 1024,

    queryLogging: process.env.POSTGRES_QUERY_LOGGING === "true",

    specialFeatures: {
        phantomCompanyTournamentTicket: process.env.PHANTOM_COMPANY_TOURNAMENT_TICKET === "true"
    },

    db: {
        database: process.env.PGDATABASE || "management",
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
        schema: process.env.PGSCHEMA || "public",
        syncOnStart: process.env.SYNC_ON_START === "true"
    },
    auth: {
        username: process.env.BASE_AUTH_USERNAME || "skywind",
        password: process.env.BASE_AUTH_PASSWORD || "91NTfJbsPGK8N0oX"
    },

    operator: {
        secretKey: process.env.OPERATOR_SECRET_KEY || "5FbzFNDHYRghixCwd8NTNJn9Tq4cl1Ue",
        cryptoAlgorythm: process.env.OPERATOR_CRYPTO_ALG || "sha256",
    },
    loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || process.env.GRAYLOG_HOST && "graylog" || "console") as any,
};

export default config;
