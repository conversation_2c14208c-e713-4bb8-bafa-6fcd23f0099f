///<reference path="../../skywind/api/routers.ts"/>
import { getApplication } from "../../skywind/serverPOP";
import logger from "../../skywind/utils/logger";
import * as merchants from "../../skywind/models/merchant";
import { addMoneyForCustomer, createCustomer, createError, createMerchant, createTicketForCustomer } from "./helper";
import * as CustomErrorModel from "../../skywind/models/customError";
import { clearAll } from "../../skywind/models/settings";
import * as TransactionModel from "../../skywind/models/transaction";
import * as SessionModel from "../../skywind/models/session";
import { MoneyTransactionRequest } from "../../skywind/scheme/pop/protocol";
import { SpecialTicket } from "../../skywind/service/ticket";
import config from "../../skywind/config";
import { Application } from "express";

const chai = require("chai");
const uuid = require("uuid");
chai.use(require("chai-shallow-deep-equal"));
const expect = chai.expect;

const log = logger("pop-mock:tests");

const request = require("supertest");

describe("POP-mock test API", async () => {
    let server: Application;
    let merchantId: string;
    let merchantPassword: string;
    let customerId: string;
    let balance: number;

    before(async () => {
        server = await getApplication();
    });

    beforeEach(async () => {
        clearAll();
        merchants.setAll({});
        CustomErrorModel.setAll({});

        SessionModel.clearAll();
        TransactionModel.setAll({});

        merchantId = "merchant_" + uuid.v4().substr(0, 8);
        merchantPassword = "pass" + uuid.v4().substr(0, 8);
        customerId = "customer_" + uuid.v4().substr(0, 8);
        balance = Math.floor(Math.random() * 1000) + 10;
    });

    it("Create merchant", async () => {
        await createMerchant(server, merchantId, merchantPassword);

        log.debug("Get merchant: " + merchantId);
        await request(server)
            .get("/v1/merchant")
            .expect(200)
            .expect({
                [merchantId]:
                    {
                        merch_id: merchantId,
                        merch_pwd: merchantPassword,
                        customers: {},
                        isPromoInternal: false,
                        multiple_session: true,
                    }
            });
    });

    it("Create customer", async () => {
        await createMerchant(server, merchantId, merchantPassword);
        await createCustomer(server, merchantId, customerId);

        log.debug("Get customer: " + customerId);
        await request(server)
            .get(`/v1/merchant/${merchantId}/customer/${customerId}`)
            .expect(200)
            .then(response => {
                expect(response.body).to.have.property("cust_id", customerId);
            });
    });

    describe("Validate ticket", () => {
        beforeEach(async () => {
            server = await getApplication();
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);
            await addMoneyForCustomer(server, merchantId, customerId, balance, 1000);
            config.specialFeatures.phantomCompanyTournamentTicket = false;
        });

        it("Validate ticket", async () => {
            const ticket = await createTicketForCustomer(server, merchantId, customerId);
            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: ticket,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(200)
                .then(response => {
                    expect(response.body.accountBalance).deep.include({
                        currencyCode: "USD",
                        playerId: customerId
                    });
                    expect(response.body.secureToken).to.be.not.undefined;
                });
        });

        it("Validate ticket twice - error", async () => {
            const ticket = await createTicketForCustomer(server, merchantId, customerId);
            const ticketParams = {
                skinId: merchantId,
                playerId: customerId,
                secureToken: ticket,
                currencyCode: "USD",
                accountId: "",
                localeCode: "en",
                gameId: "sw_al"
            };
            await request(server)
                .post("/api/player/verifyplayersession")
                .send(ticketParams)
                .expect(200)
                .then(response => {
                    expect(response.body.accountBalance).deep.include({
                        currencyCode: "USD",
                        playerId: customerId
                    });
                    expect(response.body.secureToken).to.be.not.undefined;
                });

            await request(server)
                .post("/api/player/verifyplayersession")
                .send(ticketParams)
                .expect(400)
                .then((response) => {
                    expect(response.body).deep.equal({
                        errorCode: "ERR022",
                        errorMsg: "Invalid secure token"
                    });
                });
        });

        it("Get custom Error for validate ticket", async () => {
            let ticket = await createTicketForCustomer(server, merchantId, customerId);
            await createError(server, merchantId, customerId, "verifyplayersession", {
                "responseStatus": 400,
                "errorCode": "ERR032",
                "errorMsg": "Player has reached his deposit limit",
            });

            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: ticket,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(400)
                .then(response => {
                    expect(response.body).to.deep.equal(
                        {
                            "errorCode": "ERR032",
                            "errorMsg": "Player has reached his deposit limit"
                        });
                });

            ticket = await createTicketForCustomer(server, merchantId, customerId);

            log.debug("Get normal response foк second verifyplayersession" + ticket);

            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: ticket,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(200)
                .then(response => {
                    expect(response.body.accountBalance).deep.include({
                        currencyCode: "USD",
                        playerId: customerId
                    });
                    expect(response.body.secureToken).to.be.not.undefined;
                });
        });

        it("Validate ticket phantom special ticket turn on", async () => {
            config.specialFeatures.phantomCompanyTournamentTicket = true;
            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: SpecialTicket.PHANTOM,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(200)
                .then(response => {
                    expect(response.body.accountBalance).deep.include({
                        currencyCode: "CNY"
                    });
                    expect(response.body.secureToken).to.be.not.undefined;
                });
        });

        it("Validate ticket phantom special ticket turn off", async () => {
            config.specialFeatures.phantomCompanyTournamentTicket = false;
            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: "123",
                    secureToken: SpecialTicket.PHANTOM,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(400)
                .then(response => {
                    expect(response.body).deep.equal({
                        errorCode: "ERR022",
                        errorMsg: "Invalid secure token"
                    });
                });
        });
    });

    describe("Get Player Info", () => {
        let session;
        beforeEach(async () => {
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);
            await addMoneyForCustomer(server, merchantId, customerId, balance, 1000);
            const ticket = await createTicketForCustomer(server, merchantId, customerId);
            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: ticket,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(200)
                .then(response => {
                    expect(response.body.accountBalance).deep.include({
                        currencyCode: "USD",
                        playerId: customerId
                    });
                    expect(response.body.secureToken).to.be.not.undefined;
                    session = response.body.secureToken;
                });
        });

        it("Get player info", async () => {
            await request(server)
                .post("/api/player/getplayerinfo")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session,
                })
                .expect(200)
                .then(response => {
                    expect(response.body).deep.include({
                        currencyCode: "USD",
                        countryCode: "CN",
                        playerId: customerId
                    });
                });
        });

        it("Get player info - invalid session", async () => {
            await request(server)
                .post("/api/player/getplayerinfo")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session + "invalid",
                })
                .expect(400)
                .then((response) => {
                    expect(response.body).deep.equal({
                        errorCode: "ERR1004",
                        errorMsg: "Session expired"
                    });
                });
        });
    });

    describe("Get balance", () => {
        let session;
        beforeEach(async () => {
            server = await getApplication();
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);
            await addMoneyForCustomer(server, merchantId, customerId, balance, 1000);
            const ticket = await createTicketForCustomer(server, merchantId, customerId);
            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: ticket,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(200)
                .then(response => {
                    expect(response.body.accountBalance).deep.include({
                        currencyCode: "USD",
                        playerId: customerId
                    });
                    expect(response.body.secureToken).to.be.not.undefined;
                    session = response.body.secureToken;
                });
        });

        it("Get player balance", async () => {
            await request(server)
                .post("/api/player/getplayerbalance")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session,
                    localeCode: "en"
                })
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 1000
                                },
                                {
                                    balanceType: "freespin",
                                    balanceAmt: 0
                                }
                            ]
                        }
                    });
                });
        });

        it("Get player balance - invalid params", async () => {
            await request(server)
                .post("/api/player/getplayerbalance")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session + "fail",
                    localeCode: "en"
                })
                .expect(400)
                .then((response) => {
                    expect(response.body).deep.equal({
                        errorCode: "ERR1004",
                        errorMsg: "Session expired"
                    });
                });

            await request(server)
                .post("/api/player/getplayerbalance")
                .send({
                    skinId: merchantId,
                    secureToken: session,
                    playerId: customerId,
                })
                .expect(400)
                .then((response) => {
                    expect(response.body).deep.equal({
                        errorCode: "ERR036",
                        errorMsg: "Incorrect playerId or parameters for secure token"
                    });
                });
        });
    });

    describe("Wallet", () => {
        let session;
        beforeEach(async () => {
            await createMerchant(server, merchantId, merchantPassword);
            await createCustomer(server, merchantId, customerId);
            await addMoneyForCustomer(server, merchantId, customerId, balance, 1000);
            const ticket = await createTicketForCustomer(server, merchantId, customerId);
            await request(server)
                .post("/api/player/verifyplayersession")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: ticket,
                    currencyCode: "USD",
                    accountId: "",
                    localeCode: "en",
                    gameId: "sw_al"
                })
                .expect(200)
                .then(response => {
                    expect(response.body.accountBalance).deep.include({
                        currencyCode: "USD",
                        playerId: customerId
                    });
                    expect(response.body.secureToken).to.be.not.undefined;
                    session = response.body.secureToken;
                });
        });

        it("Debit", async () => {
            const requestParams = {
                secureToken: session,
                playerId: customerId,
                localeCode: "en",
                gameId: "sw_al",
                gameCycleId: "96",
                skinId: merchantId,
                currencyCode: "USD",
                moneyTransArray: [
                    {
                        transSeq: 0,
                        transId: "cool_transaction_id",
                        transAmt: 5,
                        transType: "debit",
                        transDateTime: new Date().toISOString(),
                        transCategory: "wager",
                        transDesc: "normal bet",
                    }
                ],
                gameCycleStarted: true,
                gameCycleFinished: false
            };
            await request(server)
                .post("/api/gamesession/moneytransactions")
                .send(requestParams)
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        gameCycleId: requestParams.gameCycleId,
                        playerId: customerId,
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 995,
                                },
                                {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        },
                        errorCode: "",
                        errorMsg: "",
                        logout: false,
                        moneyAckArray: [
                            {
                                ...requestParams.moneyTransArray[0],
                                moneyDetailArray: [
                                    {
                                        "balanceType": "cashable",
                                        "detailAmt": -5,
                                        "detailType": "debit",
                                        "balanceAmt": 995
                                    }
                                ],
                                errorCode: "",
                                errorMsg: ""
                            }
                        ]
                    });
                });

            await request(server)
                .post("/api/player/getplayerbalance")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session,
                    localeCode: "en"
                })
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 995
                                }, {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        }
                    });
                });
        });

        it("Open cycle on bet = 0", async () => {
            const requestParams = {
                secureToken: session,
                playerId: customerId,
                localeCode: "en",
                gameId: "sw_al",
                gameCycleId: "96",
                skinId: merchantId,
                currencyCode: "USD",
                moneyTransArray: [],
                gameCycleStarted: true,
                gameCycleFinished: false
            };
            await request(server)
                .post("/api/gamesession/moneytransactions")
                .send(requestParams)
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        gameCycleId: requestParams.gameCycleId,
                        playerId: customerId,
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 1000,
                                },
                                {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        },
                        errorCode: "",
                        errorMsg: "",
                        logout: false,
                        moneyAckArray: []
                    });
                });

            await request(server)
                .post("/api/player/getplayerbalance")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session,
                    localeCode: "en"
                })
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 1000
                                }, {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        }
                    });
                });
        });

        it("Fail to debit with freespin", async () => {
            const requestParams = {
                secureToken: session,
                playerId: customerId,
                localeCode: "en",
                gameId: "sw_al",
                gameCycleId: "96",
                skinId: merchantId,
                currencyCode: "USD",
                moneyTransArray: [
                    {
                        transSeq: 1,
                        transId: "cool_transaction_id",
                        transAmt: 5,
                        transType: "debit",
                        transDateTime: new Date().toISOString(),
                        transCategory: "wager",
                        transDesc: "invalid bet",
                    }
                ],
                gameCycleStarted: true,
                gameCycleFinished: false
            };
            await request(server)
                .post("/api/gamesession/moneytransactions")
                .send(requestParams)
                .expect(400)
                .then(response => {
                    expect(response.body).deep.equal({
                        errorCode: "ERR034",
                        errorMsg: "First bet in round must be with gameType = 'normal'"
                    });
                });
        });

        it("Credit", async () => {
            const requestParams = {
                secureToken: session,
                playerId: customerId,
                localeCode: "en",
                gameId: "sw_al",
                gameCycleId: "97",
                skinId: merchantId,
                currencyCode: "USD",
                moneyTransArray: [
                    {
                        transSeq: 1,
                        transId: "cool_transaction_id",
                        transAmt: 3,
                        transType: "credit",
                        transDateTime: new Date().toISOString(),
                        // freeBet VS freeGame
                        transCategory: "win",
                        transDesc: "normal win",
                    }
                ],
                gameCycleStarted: true,
                gameCycleFinished: false
            };
            await request(server)
                .post("/api/gamesession/moneytransactions")
                .send(requestParams)
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        gameCycleId: requestParams.gameCycleId,
                        playerId: customerId,
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 1003,
                                },
                                {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        },
                        errorCode: "",
                        errorMsg: "",
                        logout: false,
                        moneyAckArray: [
                            {
                                ...requestParams.moneyTransArray[0],
                                moneyDetailArray: [
                                    {
                                        "balanceType": "cashable",
                                        "detailAmt": 3,
                                        "detailType": "credit",
                                        "balanceAmt": 1003
                                    }
                                ],
                                errorCode: "",
                                errorMsg: ""
                            }
                        ]
                    });
                });

            await request(server)
                .post("/api/player/getplayerbalance")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session,
                    localeCode: "en"
                })
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 1003
                                }, {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        }
                    });
                });
        });

        it("Close cycle with win = 0", async () => {
            const requestParams = {
                secureToken: session,
                playerId: customerId,
                localeCode: "en",
                gameId: "sw_al",
                gameCycleId: "97",
                skinId: merchantId,
                currencyCode: "USD",
                moneyTransArray: [],
                gameCycleStarted: false,
                gameCycleFinished: true
            };
            await request(server)
                .post("/api/gamesession/moneytransactions")
                .send(requestParams)
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        gameCycleId: requestParams.gameCycleId,
                        playerId: customerId,
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 1000,
                                }, {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        },
                        errorCode: "",
                        errorMsg: "",
                        logout: false,
                        moneyAckArray: []
                    });
                });

            await request(server)
                .post("/api/player/getplayerbalance")
                .send({
                    skinId: merchantId,
                    playerId: customerId,
                    secureToken: session,
                    localeCode: "en"
                })
                .expect(200)
                .then(response => {
                    expect(response.body).deep.equal({
                        accountBalance: {
                            playerId: customerId,
                            accountId: "",
                            currencyCode: "USD",
                            balanceArray: [
                                {
                                    balanceType: "cashable",
                                    balanceAmt: 1000
                                }, {
                                    balanceType: "freespin",
                                    balanceAmt: 0,
                                }
                            ]
                        }
                    });
                });
        });

        it("Win JP with zero contribution amount", async () => {
            const requestParams: MoneyTransactionRequest = {
                secureToken: session,
                playerId: customerId,
                localeCode: "en",
                gameId: "sw_al",
                gameCycleId: "97",
                skinId: merchantId,
                currencyCode: "USD",
                moneyTransArray: [
                    {
                        transSeq: 1,
                        transId: "cool_transaction_id",
                        transAmt: 3,
                        transType: "credit",
                        transDateTime: new Date().toISOString(),
                        transCategory: "win",
                        transDesc: "normal win",
                    }
                ],
                jackpot: {
                    contribAmt: 0,
                    awardAmt: 3
                },
                gameCycleStarted: false,
                gameCycleFinished: true
            };
            await request(server)
                .post("/api/gamesession/moneytransactions")
                .send(requestParams)
                .expect(400)
                .then(response => {
                    expect(response.body).deep.equal({
                        errorCode: "ERR004",
                        errorMsg: "Invalid remote service identifier."
                    });
                });
        });

        it("Win JP on cycleFinish = true", async () => {
            const requestParams: MoneyTransactionRequest = {
                secureToken: session,
                playerId: customerId,
                localeCode: "en",
                gameId: "sw_al",
                gameCycleId: "97",
                skinId: "skiin",
                currencyCode: "USD",
                moneyTransArray: [
                    {
                        transSeq: 1,
                        transId: "cool_transaction_id",
                        transAmt: 3,
                        transType: "credit",
                        transDateTime: new Date().toISOString(),
                        transCategory: "win",
                        transDesc: "normal win",
                    }
                ],
                jackpot: {
                    contribAmt: 1,
                    awardAmt: 3
                },
                gameCycleStarted: false,
                gameCycleFinished: true
            };
            await request(server)
                .post("/api/gamesession/moneytransactions")
                .send(requestParams)
                .expect(200);
        });
    });
});
