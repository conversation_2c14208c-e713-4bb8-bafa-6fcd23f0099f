import { NextFunction, Request, Response, Router } from "express";
import logger from "../utils/logger";
import { createTicket, generateTicketForUser } from "../service/ticket";
import { getMerchant } from "../service/merchant";
import { allowOnlyJSON } from "./config/middleware";
import { validateRequestFields } from "./config/popValidator";
import {
    MoneyTransactionRequest,
    PlayerBalanceRequest,
    PlayerInfoRequest,
    VerifyPlayerSessionRequest
} from "../scheme/pop/protocol";
import { MoorgateService, PopService } from "../service/pop";
import { resumeBrokenGame } from "../service/brokenGames";
import { INTEGRATION_TYPE } from "../../config/integrationType";
import {
    cancelTrxResponder, createBrokenGamesResponder, getGameConfigurationResponder,
    getPlayerBalanceResponder,
    moneyTransactionResponder,
    playerInfoResponder,
    verifyPlayerSessionResponder
} from "../service/popResponders";
import {
    authCustomerForVerifySession,
    authenticate,
    authenticateCustomer,
    authenticateCustomerIgnoringValidation
} from "./config/popMiddleware";
import { Customer, Merchant } from "../models/merchant";
import { CustomerHolder, MerchantHolder } from "../entities/common";
import { notSaveAnyDataMiddleware } from "./notSaveAnyData/not-save-any-data.middleware";

const log = logger(`${INTEGRATION_TYPE.POP}:api`);

const router: Router = Router();

enum ProtocolId {
    POP = "pop",
    MOORGATE = "moorgate"
}

let currentProtocolId: ProtocolId = ProtocolId.POP;

router.post("/player/verifyplayersession",
    notSaveAnyDataMiddleware(verifyPlayerSessionResponder),
    authenticate,
    authCustomerForVerifySession,
    verifyPlayerSession);

router.post("/player/getplayerinfo",
    notSaveAnyDataMiddleware(playerInfoResponder),
    authenticate,
    authenticateCustomer,
    getPlayerInfo);

router.post("/gamesession/moneytransactions",
    notSaveAnyDataMiddleware(moneyTransactionResponder),
    authenticate,
    authenticateCustomer,
    moneyTransactions);

router.post("/gamesession/canceltransactions",
    notSaveAnyDataMiddleware(cancelTrxResponder),
    authenticate,
    authenticateCustomer,
    cancelTransactions);

router.post("/player/getplayerbalance",
    notSaveAnyDataMiddleware(getPlayerBalanceResponder),
    authenticate,
    authenticateCustomer,
    getPlayerBalance);
router.post("/gamesession/regulation/:regulationCode/realitycheckdialogresponse", submitRealityCheck);
router.post("/gamesession/regulation/es/setlimits", popSpainSetLimits);
router.post("/gamesession/regulation/es/setslotsgamblingselfexclusion", popSpainSelfExcludePlayer);
router.post("/gamesession/regulation/ro/automaticreminder", popRomaniaGetSessionLimit);
router.post("/gamesession/getgameconfiguration",
    notSaveAnyDataMiddleware(getGameConfigurationResponder),
    authenticate,
    authenticateCustomer,
    getGameConfiguration);
router.post("/player/logout", logoutPlayer);
router.post("/insight", insightRequest);

async function verifyPlayerSession(req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) {
    try {
        const body = req.body as VerifyPlayerSessionRequest;
        validateRequestFields(body, "secureToken", "skinId", "playerId", "gameId", "localeCode");
        const result = await getService(req.merchant, req.customer).verifyPlayerSession(body);

        res.send(result);

    } catch (err) {
        next(err);
    }
}

async function getPlayerInfo(req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) {
    try {
        const body = req.body as PlayerInfoRequest;
        validateRequestFields(body, "skinId", "playerId", "secureToken");
        const result = await getService(req.merchant, req.customer).getPlayerInfo(body);

        res.send(result);

    } catch (err) {
        next(err);
    }
}

async function moneyTransactions(req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) {
    try {

        const body = req.body as MoneyTransactionRequest;
        validateRequestFields(body, "secureToken", "moneyTransArray", "playerId", "gameCycleId", "localeCode",
            "gameId", "skinId", "currencyCode");
        const result = await getService(req.merchant, req.customer).moneyTransactions(body);

        res.send(result);

    } catch (err) {
        next(err);
    }
}

async function cancelTransactions(req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) {
    try {

        const body = req.body;
        const result = getService(req.merchant, req.customer).cancelTransactions(body);

        res.send(result);

    } catch (err) {
        next(err);
    }
}

async function getPlayerBalance(req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) {
    try {
        const body = req.body as PlayerBalanceRequest;
        validateRequestFields(body, "secureToken", "playerId", "localeCode", "skinId");
        const result = await getService(req.merchant, req.customer).getPlayerBalance(body);

        res.send(result);

    } catch (err) {
        next(err);
    }
}

function getService(merchant: Merchant, customer?: Customer): PopService {
    switch (currentProtocolId) {
        case "pop" :
            return new PopService(merchant, customer);
        case "moorgate":
            return new MoorgateService(merchant, customer);
        default:
            throw Error("Invalid POP protocol");
    }
}

// generates a valid ticket for a test user
router.post("/get_ticket",
    (req: Request, res: Response, next: NextFunction) => {
        const merchant = getMerchant(req);
        const custId = req.query.cust_id || req.body.cust_id;

        generateTicketForUser(
            merchant,
            custId,
            req.query.currency_code || req.body.currency_code,
            req.query.single_session || req.body.single_session
        );

        const ticket = createTicket(merchant.merch_id, custId);
        res.send({ ticket: ticket.id });
    });

router.post("/get_test_customers",
    allowOnlyJSON,
    (req: Request, res: Response, next: NextFunction) => {
        const merchant = getMerchant(req);
        const testCustomers = [];

        for (const customerId in merchant.customers) {
            if (merchant.customers.hasOwnProperty(customerId)) {
                const customer = merchant.customers[customerId];
                if (customer.test_cust) {
                    testCustomers.push(customer);
                }
            }
        }
        res.send({ testCustomers });
    });

router.get("/get_protocol", (req: Request, res: Response, next: NextFunction) => {
    try {
        res.send({ protocol: currentProtocolId });
    } catch (err) {
        next(err);
    }
});

router.get("/set_protocol", (req: Request, res: Response, next: NextFunction) => {
    const protocol = req.query["protocol"] as ProtocolId;

    if (!(protocol in ProtocolId)) {
        return res.send({ error: `Invalid protocol, select one of [${ProtocolId.POP}, ${ProtocolId.MOORGATE}]` });
    }

    currentProtocolId = protocol;

    try {
        res.send({ protocol: currentProtocolId });
    } catch (err) {
        next(err);
    }
});

router.post("/gamesession/createbrokengame",
    notSaveAnyDataMiddleware(createBrokenGamesResponder),
    authenticate,
    authenticateCustomerIgnoringValidation,
    async (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        try {
            log.info(req.body, "Create broken game");
            const { brokenGameId, gameCycleId } = req.body;
            getService(req.merchant, req.customer).createBrokenGame(gameCycleId);

            res.send({ brokenGameId: brokenGameId });
        } catch (err) {
            next(err);
        }
    });

router.post("/gamesession/resumebrokengame", async (req: Request, res: Response, next: NextFunction) => {
    try {
        log.info(req.body, "Resume broken game");
        const { gameCycleId } = req.body;
        resumeBrokenGame(gameCycleId);

        res.send({
            resumeBrokenGameData: {
                command: "resumeAck"
            }
        });
    } catch (err) {
        next(err);
    }
});

async function submitRealityCheck(req: Request, res: Response, next: NextFunction) {
    try {
        log.info(req.body, "Submit reality check");
        validateRequestFields(req.body, "secureToken", "playerId", "localeCode", "skinId", "userAction");

        res.send({
            regulationTypeData: {
                regulationCommand: "RealityCheckDialogResponseAck"
            }
        });

    } catch (err) {
        next(err);
    }
}

async function popSpainSetLimits(req: Request, res: Response, next: NextFunction) {
    try {
        log.info(req.body, "SetLimits for Spanish regulation");
        validateRequestFields(req.body, "secureToken", "playerId", "skinId", "durationLimitMinutes");

        res.send({
            regulationTypeData: {
                regulationCommand: "SetGamblingSessionLimitsAck"
            }
        });

    } catch (err) {
        next(err);
    }
}

async function popSpainSelfExcludePlayer(req: Request, res: Response, next: NextFunction) {
    try {
        log.info(req.body, "SelfExclude for Spanish regulation");
        validateRequestFields(req.body, "secureToken", "playerId", "skinId", "endDate");

        res.send({
            regulationTypeData: {
                regulationCommand: "OK"
            }
        });

    } catch (err) {
        next(err);
    }
}

async function logoutPlayer(req: Request, res: Response, next: NextFunction) {
    try {
        log.info(req.body, "Logout player stub");
        validateRequestFields(req.body, "secureToken", "playerId", "skinId");

        res.send({
            logoutData: {
                command: "LogoutAck"
            }
        });

    } catch (err) {
        next(err);
    }
}

async function insightRequest(req: Request, res: Response, next: NextFunction) {
    try {
        log.info(req.body, "Insight request stub");
        validateRequestFields(req.body, "secureToken", "playerId", "skinId");

        res.send({
            inSight: {
                type: "response",
                correlationId: "efgojcw3efn32",
                data: {
                    insightVersion: "1.0.0"
                }
            }
        });

    } catch (err) {
        next(err);
    }
}

async function popRomaniaGetSessionLimit(req: Request, res: Response, next: NextFunction) {
    try {
        log.info(req.body, "Get remaining session limit for Romanian regulation");
        validateRequestFields(req.body, "secureToken", "playerId", "skinId");

        const currentDate = new Date();
        const futureDate = new Date(currentDate.getTime());
        futureDate.setMinutes(currentDate.getMinutes() + 1);

        res.send({
            regulationTypeData: {
                regulationCommand: "AutomaticReminderAck",
                regulationCommandData: {
                    data: {
                        // "2017-08-03 09:56:52.405", GMT Timestamp of the next time to show message
                        resetDateTime: futureDate.toUTCString(),
                        configuredLimit: "3600"
                    }
                }
            }
        });

    } catch (err) {
        next(err);
    }
}

async function getGameConfiguration(req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) {
    log.info(req.body, "Get game configuration");
    try {
        return res.send({
            currency: req.body.currency,
            data: {
                currencyMultiplier: 50,
                baseCurrency: req.body.currency,
                configData: {
                    bonus: {
                        freeSpinCoinSize: req.customer.freeBets && req.customer.freeBets.coin
                    }
                }
            }
        });
    } catch (err) {
        next(err);
    }
}

export default router;
