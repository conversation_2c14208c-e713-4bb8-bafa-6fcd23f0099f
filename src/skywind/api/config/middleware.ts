import { NextFunction, Request, Response } from "express";
import { MethodWorksOnlyWithJSON } from "../../errors";
import { buildBodyToLog, buildHeadersToLog, JSON_ACCEPT_TYPE, MOCK_RESPONSE_HEADER } from "../../utils/helper";
import config from "../../config";
import { settings } from "../../models/settings";
import { CustomerHolder, MerchantHolder } from "../../entities/common";
import { getById } from "../../models/merchant";
import * as Session from "../../service/session";

export async function allowOnlyJSON(req: Request,
                                    res: Response,
                                    next: NextFunction) {
    if (req.header("Accept") !== JSON_ACCEPT_TYPE) {
        next(new MethodWorksOnlyWithJSON());
    }
    next();
}

export const routerCallbackWrapper = (callback) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            await callback(req, res, next);
        } catch (err) {
            next(err);
        }
    };
};

export const routerAsyncCallbackWrapper = (callback) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            await callback(req, res, next);
        } catch (err) {
            next(err);
        }
    };
};

export function requestLogging(logger): (req: Request, res: Response, next: NextFunction) => any {
    return (req: Request, res: Response, next: NextFunction): any => {
        const httpRequestLogData = {
            request_headers: buildHeadersToLog(req),
            request_body: buildBodyToLog(req),
            request_query: req.query
        };
        logger.info(httpRequestLogData, "Http request", req.method, req.url);
        next();
    };
}

export const validateBaseAuth = (error: Error) => (req, res, next) => {
    const b64auth = (req.headers.authorization || "").split(" ")[1] || "";
    const [login, password] = new Buffer(b64auth, "base64").toString().split(":");

    if (login && password && login === config.auth.username && password === config.auth.password) {
        // Access granted...
        return next();
    }

    next(error);
};

export const authenticate = (getMerchantCode: (req: Request) => string, error?: Error) =>
    (req: Request & MerchantHolder, res: Response, next: NextFunction) => {
        const code = getMerchantCode(req);
        const merchant = getById(code, !error);
        if (!merchant) {
            return next(error);
        }

        req.merchant = merchant;

        return next();
    };

export const authenticateCustomer = (getPlayerCodeAndSession: (req: Request) => { code: string, sessionId: string },
                                     error: Error,
                                     throwErrorOnSessionValidation: (req: Request) => boolean = (req) => true) =>
    (req: Request & MerchantHolder & CustomerHolder, res: Response, next: NextFunction) => {
        const { code, sessionId } = getPlayerCodeAndSession(req);
        const customer = code && req.merchant.customers[code];
        if (!(customer && Session.validateCustomerSession(sessionId)) && throwErrorOnSessionValidation(req)) {
            return next(error);
        }

        req.customer = customer;

        return next();
    };
