import { NextFunction, Request, Response, Router } from "express";
import { SisalExtraRequestData } from "./sisal.entities";
import * as Merchants from "../models/merchant";
import { Merchant, Customer } from "../models/merchant";
import { Ticket } from "../models/ticket";
import { settings } from "../models/settings";
import { getSisalTicket } from "../service/ticket";
import config from "../config";
import * as SisalErrors from "./sisal.errors";
import { isEmpty } from "../utils/isEmpty";
import * as sha256 from "crypto-js/sha256";
import * as hex from "crypto-js/enc-hex";

export enum HTTP_METHOD {
    GET = "GET",
    POST = "POST",
    PATCH = "PATCH",
    PUT = "PUT",
    DELETE = "DELETE"
}

export const authenticate = (req: Request & SisalExtraRequestData, res: Response, next: NextFunction) => {
    const gameSessionId = req.params["gameSessionId"];
    const [_, authorization] = (req.headers["authorization"] as string).split(" ");
    if (!gameSessionId && !authorization) {
        throw new SisalErrors.UnauthorizedError();
    }

    req.tokenData = getSisalTicket(gameSessionId || authorization, config.expirationTime.ticket) as Ticket;
    const merchantId = req.tokenData.merchantId;
    const playerId = req.tokenData.custId;

    const merchant: Merchant = Merchants.getById(merchantId, false);
    if (!merchant) {
        throw new SisalErrors.UnauthorizedError();
    }

    const customer: Customer = merchant.customers[playerId];
    if (!customer) {
        throw new SisalErrors.UnauthorizedError();
    }

    req.merchant = merchant;
    req.customer = customer;

    if (req.method === HTTP_METHOD.GET) {
        return next();
    }

    const actualXAuth = req.headers["x-authorization"];
    const expectedXAuth = createXAuthorization(req.body, merchant.merch_pwd);
    if (actualXAuth !== expectedXAuth) {
        throw new SisalErrors.InvalidXAuthError(
            `Actual xAuth: ${actualXAuth}; Expected xAuth: ${expectedXAuth}`);
    }

    next();
};

type CustomFunction = (req: Request, res: Response, next: NextFunction) => void;
export type ControllersToBeWrapped = CustomFunction[] | CustomFunction;

export function wrapApiControllers(controllers: ControllersToBeWrapped): Array<any> {
    if (!controllers || !controllers.length) {
        return;
    }
    controllers = Array.isArray(controllers) ? controllers : [controllers];

    return controllers.map(controller =>
        async (req: Request, res: Response, next: NextFunction) => {
            try {
                await controller(req, res, next);
            } catch (error) {
                next(error);
            }
        });
}

export function notSaveAnyData(responder: (req: Request & SisalExtraRequestData) => object) {
    return (req: Request & SisalExtraRequestData, res: Response, next: NextFunction) => {
        if (settings.notSaveAnyData) {
            try {
                return res.send(responder(req));
            } catch (err) {
                return next(err);
            }
        }
        return next();
    };
}

export function validateFieldsForEmpty(fields: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        for (const param of fields) {
            const value = req.body[param];
            if (isEmpty(value)) {
                return next(new SisalErrors.GenericError());
            }
        }
        return next();
    };
}

export function createXAuthorization(payload: object, key: string = ""): string {
    return hex.stringify(sha256(JSON.stringify(payload) + key));
}
