import { BaseErrorToLog, ErrorInfoToLog } from "../../errors";
import { MR_GREEN_JURISDICTION } from "./entities";

export enum MR_GREEN_RETURN_CODE {
    SUCCESS = 0,
    TECHNICAL_ERROR = 1,
    CONFIG_ERROR = 3,
    BAD_ARGUMENT = 12,
    ROLLBACK_FAILURE = 108,
    NOT_LOGGED_ON = 1000,
    AUTHENTICATION_FAILURE = 1003,
    OUT_OF_MEMORY = 1006,
    PARAMETER_REQUIRED = 1008,
    GAMING_LIMITS = 1019,
    TURNOVER_DAY = 1103,
    TURNOVER_WEEK = 1104,
    TURNOVER_MONTH = 1105,
    LOSS_LIMIT_DAY = 1106,
    LOSS_LIMIT_WEEK = 1107,
    LOSS_LIMIT_MONTH = 1108,
    ACCOUNT_BLOCKED = 1035
}

export enum MR_GREEN_RETURN_CODE_DESCRIPTION {
    TECHNICAL_ERROR = "Technical error",
    CONFIG_ERROR = "Config Error",
    BAD_ARGUMENT = "Bad Argument",
    ROLL<PERSON>CK_FAILURE = "Rollback Failure",
    NOT_LOGGED_ON = "Not logged on",
    AUTHENTICATION_FAILURE = "Authentiation Failure",
    OUT_OF_MEMORY = "Out of money",
    PARAMETER_REQUIRED = "Parameter required",
    GAMING_LIMITS = "Gaming limits",
    TURNOVER_DAY = "Turnover: day",
    TURNOVER_WEEK = "Turnover: week",
    TURNOVER_MONTH = "Turnover: month",
    LOSS_LIMIT_DAY = "Loss limit: day",
    LOSS_LIMIT_WEEK = "Loss limit: week",
    LOSS_LIMIT_MONTH = "Loss limit: month",
    ACCOUNT_BLOCKED = "Account blocked"
}

/**
 * Base
 */

export class MrGreenError extends Error implements BaseErrorToLog {
    constructor(rc: number,
                msg: string,
                responseStatus: number = 200,
                customField: string = null) {
        super(msg);

        this.rc = rc;
        this.msg = msg;
        this.responseStatus = responseStatus;
        this.customField = customField;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.rc,
                message: this.msg,
                stack: this.stack
            }
        };
    }

    public rc: number;
    public msg: string;
    public responseStatus: number;
    public customField: string;
}

/**
 * End of Base
 */

/**
 * MOCK Custom
 */

export class MrGreenError404 extends MrGreenError {
    public responseStatus: number;
    constructor() {
        super(773, "NotFound");
        this.responseStatus = 404;
    }
}

export class EmptyFieldError extends MrGreenError {
    constructor(message: string) {
        super(774, `MockErrorEmptyField ${message}`);
    }
}

export class OperatorNotSetError extends MrGreenError {
    constructor() {
        super(774, "OPERATOR_NOT_SET");
    }
}

export class JurisdictionNotSetError extends MrGreenError {
    constructor() {
        super(775,
            "Jurisdiction not set. please setup jurisdiction either in token" +
            "emLaunchToken{anyString}_{merchantCode}_{jurisdiction}' or in customer settings (user settings)");
    }
}

export class InvalidJurisdictionError extends MrGreenError {
    constructor() {
        super(776,
            "Jurisdiction is invalid. Possible values: " +
            `${MR_GREEN_JURISDICTION.UK}, ${MR_GREEN_JURISDICTION.MGA}, ${MR_GREEN_JURISDICTION.Sweden}`);
    }
}

export class WrongTokenFormatError extends MrGreenError {
    constructor() {
        super(
            777,
            `Wrong token format, token must contains two '_' literals.
            "Please use right format like 'nyxSession{anyString}_{merchantCode}_{jurisdiction}'`);
    }
}

export class OperatorNotFoundError extends MrGreenError {
    constructor() {
        super(778, "OPERATOR_NOT_FOUND");
    }
}

/**
 * End of Custom
 */

/**
 * Return Codes
 */

export class TechnicalError extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.TECHNICAL_ERROR,
            MR_GREEN_RETURN_CODE_DESCRIPTION.TECHNICAL_ERROR);
    }
}

export class ConfigError extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.CONFIG_ERROR,
            MR_GREEN_RETURN_CODE_DESCRIPTION.CONFIG_ERROR);
    }
}

export class BadArgument extends MrGreenError {
    constructor(msg = null) {
        super(MR_GREEN_RETURN_CODE.BAD_ARGUMENT,
            `${MR_GREEN_RETURN_CODE_DESCRIPTION.BAD_ARGUMENT}. ${msg}`);
    }
}

export class RollbackFailure extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.ROLLBACK_FAILURE,
            MR_GREEN_RETURN_CODE_DESCRIPTION.ROLLBACK_FAILURE);
    }
}

export class NotLoggedOn extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.NOT_LOGGED_ON,
            MR_GREEN_RETURN_CODE_DESCRIPTION.NOT_LOGGED_ON);
    }
}

export class AuthenticationFailure extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.AUTHENTICATION_FAILURE,
            MR_GREEN_RETURN_CODE_DESCRIPTION.AUTHENTICATION_FAILURE);
    }
}

export class OutOfMemory extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.OUT_OF_MEMORY,
            MR_GREEN_RETURN_CODE_DESCRIPTION.OUT_OF_MEMORY);
    }
}

export class ParameterRequired extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.PARAMETER_REQUIRED,
            MR_GREEN_RETURN_CODE_DESCRIPTION.PARAMETER_REQUIRED);
    }
}

export class GamingLimits extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.GAMING_LIMITS,
            MR_GREEN_RETURN_CODE_DESCRIPTION.GAMING_LIMITS);
    }
}

export class TurnoverDay extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.TURNOVER_DAY,
            MR_GREEN_RETURN_CODE_DESCRIPTION.TURNOVER_DAY);
    }
}

export class TurnoverWeek extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.TURNOVER_WEEK,
            MR_GREEN_RETURN_CODE_DESCRIPTION.TURNOVER_WEEK);
    }
}

export class TurnoverMonth extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.TURNOVER_MONTH,
            MR_GREEN_RETURN_CODE_DESCRIPTION.TURNOVER_MONTH);
    }
}

export class LossLimitDay extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.LOSS_LIMIT_DAY,
            MR_GREEN_RETURN_CODE_DESCRIPTION.LOSS_LIMIT_DAY);
    }
}

export class LossLimitWeek extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.LOSS_LIMIT_WEEK,
            MR_GREEN_RETURN_CODE_DESCRIPTION.LOSS_LIMIT_WEEK);
    }
}

export class LossLimitMonth extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.LOSS_LIMIT_MONTH,
            MR_GREEN_RETURN_CODE_DESCRIPTION.LOSS_LIMIT_MONTH);
    }
}

export class AccountBlocked extends MrGreenError {
    constructor() {
        super(MR_GREEN_RETURN_CODE.ACCOUNT_BLOCKED,
            MR_GREEN_RETURN_CODE_DESCRIPTION.ACCOUNT_BLOCKED);
    }
}

/**
 * End of Return codes
 */

export function getMrGreenErrorInstance(code: number) {
    switch (code) {
        case MR_GREEN_RETURN_CODE.TECHNICAL_ERROR:
            return new TechnicalError();
        case MR_GREEN_RETURN_CODE.CONFIG_ERROR:
            return new ConfigError();
        case MR_GREEN_RETURN_CODE.BAD_ARGUMENT:
            return new BadArgument();
        case MR_GREEN_RETURN_CODE.ROLLBACK_FAILURE:
            return new RollbackFailure();
        case MR_GREEN_RETURN_CODE.NOT_LOGGED_ON:
            return new NotLoggedOn();
        case MR_GREEN_RETURN_CODE.AUTHENTICATION_FAILURE:
            return new AuthenticationFailure();
        case MR_GREEN_RETURN_CODE.OUT_OF_MEMORY:
            return new OutOfMemory();
        case MR_GREEN_RETURN_CODE.PARAMETER_REQUIRED:
            return new ParameterRequired();
        case MR_GREEN_RETURN_CODE.GAMING_LIMITS:
            return new GamingLimits();
        case MR_GREEN_RETURN_CODE.TURNOVER_DAY:
            return new TurnoverDay();
        case MR_GREEN_RETURN_CODE.TURNOVER_WEEK:
            return new TurnoverWeek();
        case MR_GREEN_RETURN_CODE.TURNOVER_MONTH:
            return new TurnoverMonth();
        case MR_GREEN_RETURN_CODE.LOSS_LIMIT_DAY:
            return new LossLimitDay();
        case MR_GREEN_RETURN_CODE.LOSS_LIMIT_WEEK:
            return new LossLimitWeek();
        case MR_GREEN_RETURN_CODE.LOSS_LIMIT_MONTH:
            return new LossLimitMonth();
        case MR_GREEN_RETURN_CODE.ACCOUNT_BLOCKED:
            return new AccountBlocked();
        default:
            throw new Error("Unknown MrGreen error code");
    }
}
