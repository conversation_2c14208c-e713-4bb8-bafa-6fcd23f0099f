import { Request } from "express";
import { Customer, Merchant } from "../../models/merchant";

export enum REQUEST_STATE {
    SINGLE = "single",
    MULTI = "multi"
}

export enum REQUEST_COMMAND {
    INIT = "initsession",
    BALANCE = "balance",
    BET = "bet",
    WIN = "win",
    DEPOSIT = "depositmoney",
    CANCEL = "cancel",
    END = "end",
    TOKEN = "token",
    ROUNDDETAILS = "rounddetails"
}

export interface RequestParameters {
    transactionid: string;
    roundid: string;
    closeround?: boolean;
    amount: number;
}

export interface RequestAction {
    command: REQUEST_COMMAND;
    parameters?: RequestParameters;
}

export interface RequestEx extends Request {
    operatorCommand: REQUEST_COMMAND;
    merchant: Merchant;
    customer: Customer;
}

export interface AdapterRequest {
    providergameid: string;
    licenseeid: number;
    operator: string;
    token: string;
    playerid: string;
    username: string;
    currency: string;
    country?: string;
    ISBskinid: number;
    ISBgameid: number;
    state: REQUEST_STATE;
    action: RequestAction;
    sessionid: string;
}

export enum RESPONSE_STATUS {
    SUCCESS = "success",
    ERROR = "error"
}

export interface BaseResponse {
    status: RESPONSE_STATUS;
    balance?: number;
}

export interface AuthenticateResponse extends BaseResponse {
    sessionid: string;
    playerid: string;
    username?: string;
    currency: string;
    max_bet?: number;
}

export interface OperatorTokenResponse {
    token: string;
    ISBskinid: number;
    ISBgameid: number;
}

export interface BalanceResponse extends BaseResponse {
    currency: string;
}

export enum ISB_ACTION {
    INIT = "init",
    BET = "bet",
    WIN = "win",
    DEPOSIT = "deposit",
    CANCEL = "cancel",
    BALANCE = "balance",
    END = "end",
    TOKEN = "token",
    ROUNDDETAILS = "rounddetails"
}
