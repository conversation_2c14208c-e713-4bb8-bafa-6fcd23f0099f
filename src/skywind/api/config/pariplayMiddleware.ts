import { authenticate as authBase, authenticateCustomer as authCustomerBase } from "./middleware";
import { AuthenticateFailedError, SessionExpiredError } from "../../errorsPariplay";

export const authenticate = authBase(
    (req) => req?.body?.Account?.Username,
    new AuthenticateFailedError());

export const authenticateCustomer = authCustomerBase(
    (req) => ({
        code: req.body.PlayerId,
        sessionId: req.body.Token
    }), new SessionExpiredError());
