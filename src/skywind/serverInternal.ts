import http = require("http");
import  { setupBase, setupMetricHandlers } from "./express";

import { createErrorHandler } from "./api/config/errorMiddleware";
import config from "./config";
import { lazy } from "@skywind-group/sw-utils";

import { Application } from "express";
import * as express from "express";

import logger from "./utils/logger";

export async function startServer(integration: string): Promise<http.Server> {
    const log = logger(`${integration}-internal-api`);
    log.info(`Server Internal (${integration}) is starting`);
    const app = application.get();
    const internalServer: http.Server = require("http").createServer(app);
    app.use(createErrorHandler(log));
    return new Promise<http.Server>((resolve, reject) => {
        const port = config.internalServer.port;
        internalServer.listen(port, null, () => {
            resolve(internalServer);
            log.info(`Server Internal API (${integration}) listening on ` + port);
        });
    });
}

const application = lazy(() => {
    const app: Application = express();
    setupMetricHandlers(app);
    setupBase(app);
    return app;
});
