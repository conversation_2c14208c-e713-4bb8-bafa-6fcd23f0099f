import { BaseErrorToLog, ErrorInfoToLog } from "./errors";

export interface PokerStarError extends BaseErrorToLog {
    responseStatus: number;
    errorType: string;
    errorMessage: string;
    extraData?: any;
}

export class PokerStarBaseError extends Error implements PokerStarError {
    public readonly responseStatus: number;
    public readonly errorType: string;
    public readonly errorMessage: string;
    public readonly extraData: string;

    constructor(status: number, message: string, type: string = "SystemError", extraData?: any) {
        super(message);
        this.responseStatus = status;
        this.errorType = type;
        this.errorMessage = message;
        this.extraData = extraData;
    }

    public toLog(): ErrorInfoToLog {
        return {
            err: {
                name: this.name,
                code: this.errorType,
                message: this.errorMessage,
                stack: this.stack
            }
        };
    }
}

export class SessionTerminatedError extends PokerStarBaseError {
    constructor(message) {
        super(200, message || "Customer ticket or session not found", "SessionTerminatedError");
    }
}

export class MessageValidationError extends PokerStarBaseError {
    constructor(message) {
        super(200, message || "Validation Error", "MessageValidationError");
    }
}

export class SystemError extends PokerStarBaseError {
    constructor(message?: string) {
        super(400, message || "Invalid request", "SystemError");
    }
}

export class InsufficientFundsError extends PokerStarBaseError {
    constructor(message?: string) {
        super(200, message || "Insufficient Funds Error", "InsufficientFundsError");
    }
}
