export enum ERROR_CODES {
    ERR_GENERAL_REQUEST = "R_01",
    ERR_REQ_PARAM_MISSED = "R_02",
    ERR_INVALID_PARAM = "R_03",
    ERR_PARAM_CONFIG = "R_04",
    ERR_AUTH = "R_05",
    ERR_BET_GENERAL = "B_01",
    ERR_BET_INSUFICIENT = "B_03",
    ERR_BET_TIME_EXCEED = "B_08",
    ERR_WIN_GENERAL = "W_01",
    ERR_CANCEL_GENERAL = "C_01",
    ERR_DEPOSIT_GENERAL = "D_01",
    ERR_DEPOSIT_NOTSUPPORTED = "D_02",
    ERR_REPEATABLE = "ER02"
}

export enum RESPONSE_STATUS {
    SUCCESS = "success",
    ERROR = "error"
}

export enum ERROR_ACTION {
    VOID = "void",
    CONTINUE = "continue",
    BUTTONS = "buttons"
}

export enum BUTTON_ACTION {
    VOID = "void",
    CONTINUE = "continue",
    HISTORY = "history"
}

export interface OperatorErrorAction {
    text: string;
    action: BUTTON_ACTION;
}

export interface OperatorResponse {
    status: RESPONSE_STATUS;
}

export interface OperatorErrorResponse extends OperatorResponse {
    code: string;
    message: string;
    action: ERROR_ACTION;
    display?: boolean;
    retry?: boolean;
    buttons?: [OperatorErrorAction];
}

export class MockError extends Error {
    public responseStatus: number;
    public errorCode: number;
    public errorMsg: string;
    public extraData?: any;
}

export class ISBError extends Error {
    private status: number;
    private operatorError: OperatorErrorResponse;
    constructor(
                code: ERROR_CODES,
                message: string,
                status: number = 400,
                action: ERROR_ACTION = ERROR_ACTION.VOID,
                display?: boolean,
                retry?: boolean,
                buttons?: [OperatorErrorAction]) {
        super(message);
        this.operatorError = {
            status: RESPONSE_STATUS.ERROR,
            code,
            message,
            action,
            display,
            retry,
            buttons
        };
        this.status = status;
    }

    public getStatus() {
        return this.status;
    }

    public getOperatorError() {
        return this.operatorError;
    }
}

export class GeneralRequestError extends ISBError {
    constructor(message: string) {
        super(ERROR_CODES.ERR_GENERAL_REQUEST, message);
    }
}

export class ParamMissedError extends ISBError {
    constructor(message: string) {
        super(ERROR_CODES.ERR_REQ_PARAM_MISSED, message);
    }
}

export class InvalidParamError extends ISBError {
    constructor(message: string) {
        super(ERROR_CODES.ERR_INVALID_PARAM, message);
    }
}

export class AuthError extends ISBError {
    constructor(message: string = "Authentication error") {
        super(ERROR_CODES.ERR_AUTH, message);
    }
}

export class InvalidTokenError extends ISBError {
    constructor(message: string = "Invalid token") {
        super(ERROR_CODES.ERR_INVALID_PARAM, message);
    }
}

export class InvalidSessionError extends ISBError {
    constructor(message: string = "Session not found") {
        super(ERROR_CODES.ERR_INVALID_PARAM, message);
    }
}

export class MerchantNotFoundError extends ISBError {
    constructor(message: string = "Merchant not found") {
        super(ERROR_CODES.ERR_INVALID_PARAM, message);
    }
}

export class PlayerNotFoundError extends ISBError {
    constructor(message: string = "Customer not found") {
        super(ERROR_CODES.ERR_INVALID_PARAM, message);
    }
}

export class InvalidRollbackError extends ISBError {
    constructor() {
        super(ERROR_CODES.ERR_CANCEL_GENERAL, "Invalid transaction rollback");
    }
}

export class InsufficientBalanceError extends ISBError {
    constructor() {
        super(ERROR_CODES.ERR_BET_INSUFICIENT, "Not enough money ot make a bet");
    }
}
