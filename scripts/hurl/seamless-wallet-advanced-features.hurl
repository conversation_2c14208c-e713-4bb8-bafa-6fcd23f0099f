# Seamless Wallet Advanced Features Test
# This script tests advanced features like jackpots, tournaments, and complex game scenarios

## Setup: Authentication and Configuration
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"

POST {{MAPI_URL}}/v1/entities/MERCHANT_ENTITY/terminals/token/generate
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{}
HTTP 200
[Captures]
terminalToken: jsonpath "$['terminalToken']"

## Setup: Create Advanced Features Merchant
POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "advanced_merchant",
  "merch_pwd": "AdvancedPassword123!",
  "isPromoInternal": true,
  "multiple_session": true,
  "serverUrl": "{{MOCK_URL}}",
  "type": "seamless",
  "features": {
    "jackpots": true,
    "tournaments": true,
    "multiCurrency": true,
    "liveGames": true
  }
}
HTTP 200

## Test 1: Create High-Value Player for Jackpot Testing
POST {{MOCK_URL}}/v1/merchant/advanced_merchant/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "jackpot_player_001",
  "cust_login": "JACKPOTPLAYER001",
  "currency_code": "EUR",
  "language": "en",
  "country": "GB",
  "test_cust": false,
  "status": "vip",
  "bet_limit": 10000,
  "first_name": "Jackpot",
  "last_name": "Player",
  "email": "<EMAIL>",
  "jurisdiction": "MGA",
  "balance": {
    "amount": 1000000,
    "currency_code": "EUR"
  },
  "vip_level": "platinum",
  "jackpot_eligible": true
}
HTTP 200
[Captures]
jackpotPlayerId: jsonpath "$.cust_id"

## Test 2: Generate Ticket and Login for Jackpot Player
GET {{MOCK_URL}}/v1/merchant/advanced_merchant/customer/jackpot_player_001/ticket
accept: text/plain
HTTP 200
[Captures]
jackpotPlayerTicket: body

POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "{{jackpotPlayerTicket}}"
}
HTTP 200
[Captures]
jackpotPlayerToken: jsonpath "$['token']"
jackpotPlayerSessionId: jsonpath "$['playerId']"

## Test 3: Get Jackpot Information
GET {{MAPI_URL}}/v1/jackpots/{{JACKPOT_POOL_ID}}
accept: application/json
x-access-token: {{accessToken}}
HTTP 200
[Captures]
jackpotAmount: jsonpath "$.pools.main.amount"
jackpotCurrency: jsonpath "$.currency"
[Asserts]
jsonpath "$.jackpotId" == "{{JACKPOT_POOL_ID}}"
jsonpath "$.pools.main.amount" > 0
jsonpath "$.currency" == "EUR"

## Test 4: Place Jackpot-Eligible Bet
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "advanced_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "AdvancedPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{jackpotPlayerSessionId}}",
    "currency": "EUR",
    "brandId": 1
  },
  "request": {
    "transactionId": "jackpot_bet_001",
    "roundId": "jackpot_round_001",
    "gameCode": "sw_mrmnky",
    "bet": 50.00,
    "win": 0,
    "currency": "EUR",
    "operationType": "bet",
    "jackpotContribution": 2.50,
    "jackpotEligible": true
  },
  "regulation": "default"
}
HTTP 200
[Captures]
afterJackpotBet: jsonpath "$.balance"
[Asserts]
jsonpath "$.transactionId" == "jackpot_bet_001"
jsonpath "$.jackpotContribution" == 2.50

## Test 5: Simulate Jackpot Win
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "advanced_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "AdvancedPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{jackpotPlayerSessionId}}",
    "currency": "EUR",
    "brandId": 1
  },
  "request": {
    "transactionId": "jackpot_win_001",
    "roundId": "jackpot_round_001",
    "gameCode": "sw_mrmnky",
    "bet": 0,
    "win": 125000.00,
    "currency": "EUR",
    "operationType": "win",
    "jackpotWin": {
      "jackpotId": "{{JACKPOT_POOL_ID}}",
      "poolType": "main",
      "amount": 125000.00
    }
  },
  "regulation": "default"
}
HTTP 200
[Captures]
afterJackpotWin: jsonpath "$.balance"
[Asserts]
jsonpath "$.transactionId" == "jackpot_win_001"
jsonpath "$.jackpotWin.amount" == 125000.00
jsonpath "$.balance" > {{afterJackpotBet}}

## Test 6: Create Tournament Player
POST {{MOCK_URL}}/v1/merchant/advanced_merchant/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "tournament_player_001",
  "cust_login": "TOURNAMENTPLAYER001",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "bet_limit": 5000,
  "first_name": "Tournament",
  "last_name": "Player",
  "email": "<EMAIL>",
  "jurisdiction": "COM",
  "balance": {
    "amount": 500000,
    "currency_code": "USD"
  },
  "tournament_eligible": true
}
HTTP 200

## Test 7: Tournament Entry
GET {{MOCK_URL}}/v1/merchant/advanced_merchant/customer/tournament_player_001/ticket
accept: text/plain
HTTP 200
[Captures]
tournamentPlayerTicket: body

POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "{{tournamentPlayerTicket}}"
}
HTTP 200
[Captures]
tournamentPlayerSessionId: jsonpath "$['playerId']"

## Test 8: Join Tournament
POST {{MAPI_URL}}/v1/tournaments/{{TOURNAMENT_ID}}/join
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "playerId": "{{tournamentPlayerSessionId}}",
  "entryFee": "{{TOURNAMENT_ENTRY_FEE}}",
  "currency": "USD"
}
HTTP 200
[Captures]
tournamentEntryId: jsonpath "$.entryId"
[Asserts]
jsonpath "$.tournamentId" == "{{TOURNAMENT_ID}}"
jsonpath "$.entryFee" == {{TOURNAMENT_ENTRY_FEE}}
jsonpath "$.status" == "active"

## Test 9: Tournament Bet with Score Tracking
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "advanced_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "AdvancedPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{tournamentPlayerSessionId}}",
    "currency": "USD",
    "brandId": 1
  },
  "request": {
    "transactionId": "tournament_bet_001",
    "roundId": "tournament_round_001",
    "gameCode": "sw_mrmnky",
    "bet": 25.00,
    "win": 75.00,
    "currency": "USD",
    "operationType": "bet_win",
    "tournamentData": {
      "tournamentId": "{{TOURNAMENT_ID}}",
      "entryId": "{{tournamentEntryId}}",
      "score": 300,
      "multiplier": 3.0
    }
  },
  "regulation": "default"
}
HTTP 200
[Captures]
tournamentScore: jsonpath "$.tournamentData.score"
[Asserts]
jsonpath "$.tournamentData.score" == 300
jsonpath "$.tournamentData.multiplier" == 3.0

## Test 10: Multi-Currency Player Setup
POST {{MOCK_URL}}/v1/merchant/advanced_merchant/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "multicurrency_player_001",
  "cust_login": "MULTICURRENCYPLAYER001",
  "currency_code": "GBP",
  "language": "en",
  "country": "GB",
  "test_cust": false,
  "status": "normal",
  "bet_limit": 2000,
  "first_name": "MultiCurrency",
  "last_name": "Player",
  "email": "<EMAIL>",
  "jurisdiction": "UKGC",
  "balances": {
    "GBP": {
      "amount": 100000,
      "currency_code": "GBP"
    },
    "EUR": {
      "amount": 50000,
      "currency_code": "EUR"
    },
    "USD": {
      "amount": 75000,
      "currency_code": "USD"
    }
  },
  "multi_currency_enabled": true
}
HTTP 200

## Test 11: Currency Exchange Transaction
GET {{MOCK_URL}}/v1/merchant/advanced_merchant/customer/multicurrency_player_001/ticket
accept: text/plain
HTTP 200
[Captures]
multiCurrencyTicket: body

POST {{TERMINAL_URL}}/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "{{multiCurrencyTicket}}"
}
HTTP 200
[Captures]
multiCurrencySessionId: jsonpath "$['playerId']"

## Test 12: Cross-Currency Bet
POST {{WALLET_URL}}/v1/payments/
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "advanced_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "AdvancedPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{multiCurrencySessionId}}",
    "currency": "GBP",
    "brandId": 1
  },
  "request": {
    "transactionId": "cross_currency_bet_001",
    "roundId": "cross_currency_round_001",
    "gameCode": "sw_mrmnky",
    "bet": 20.00,
    "win": 0,
    "currency": "GBP",
    "operationType": "bet",
    "exchangeRate": 1.15,
    "originalCurrency": "EUR",
    "originalAmount": 17.39
  },
  "regulation": "default"
}
HTTP 200
[Asserts]
jsonpath "$.exchangeRate" == 1.15
jsonpath "$.originalCurrency" == "EUR"

## Test 13: Live Game Session
POST {{MAPI_URL}}/v1/game/url
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "merchantType": "seamless",
  "merchantCode": "advanced_merchant",
  "gameCode": "live_blackjack_001",
  "playmode": "real",
  "language": "en",
  "ip": "127.0.0.1",
  "lobby": "http://localhost:3000/lobby",
  "cashier": "http://localhost:3000/cashier",
  "ticket": "{{jackpotPlayerTicket}}",
  "currency": "EUR",
  "gameType": "live",
  "tableId": "BJ_001"
}
HTTP 200
[Captures]
liveGameUrl: jsonpath "$.url"
liveGameToken: jsonpath "$.token"
tableId: jsonpath "$.tableId"
[Asserts]
jsonpath "$.gameType" == "live"
jsonpath "$.tableId" == "BJ_001"

## Test 14: Deferred Payment Operation
POST {{WALLET_URL}}/v1/payments/deferred
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "advanced_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "AdvancedPassword123!"
    }
  },
  "gameTokenData": {
    "playerId": "{{jackpotPlayerSessionId}}",
    "currency": "EUR",
    "brandId": 1
  },
  "request": {
    "transactionId": "deferred_payment_001",
    "roundId": "deferred_round_001",
    "gameCode": "sw_mrmnky",
    "bet": 100.00,
    "currency": "EUR",
    "operationType": "deferred_bet",
    "deferredData": {
      "reason": "pending_verification",
      "timeout": 300,
      "maxRetries": 3
    }
  },
  "regulation": "default"
}
HTTP 202
[Captures]
deferredOperationId: jsonpath "$.operationId"
[Asserts]
jsonpath "$.status" == "pending"
jsonpath "$.operationId" exists

## Test 15: Complete Deferred Payment
POST {{WALLET_URL}}/v1/payments/deferred/{{deferredOperationId}}/complete
accept: application/json
Content-Type: application/json
{
  "merchantInfo": {
    "code": "advanced_merchant",
    "type": "seamless",
    "params": {
      "serverUrl": "{{MOCK_URL}}",
      "password": "AdvancedPassword123!"
    }
  },
  "result": {
    "status": "approved",
    "finalAmount": 100.00,
    "win": 250.00
  }
}
HTTP 200
[Asserts]
jsonpath "$.status" == "completed"
jsonpath "$.finalAmount" == 100.00
jsonpath "$.win" == 250.00
