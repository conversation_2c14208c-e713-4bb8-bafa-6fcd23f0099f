import { NextFunction, Request, Response, Router } from "express";
import logger from "../utils/logger";
import config from "../config";
import { sumMajorUnits, XML_TYPE, } from "../utils/helper";
import {
    InsufficientFundsError,
    MessageValidationError,
    SessionTerminatedError,
    SystemError
} from "../errorsPokerStar";
import * as Merchants from "../models/merchant";
import { Customer, Merchant } from "../models/merchant";
import { routerCallbackWrapper } from "./config/middleware";
import { settings } from "../models/settings";
import * as TransactionModel from "../models/transaction";
import { WALLET_ACTION } from "../models/transaction";
import * as RoundModel from "../models/round";
import { Ticket, tickets } from "../models/ticket";
import { throwCustomErrorIfExists } from "../service/customError";
import { defaultCustomerCurrency } from "../service/customer";
import { INTEGRATION_TYPE } from "../../config/integrationType";
import { createXML } from "../utils/createXml";
import { transform } from "camaro";

const bodyParser = require("body-parser");
// const transform = require("camaro");

const log = logger(`${INTEGRATION_TYPE.POKER_STAR}:api`);
const router: Router = Router();
const templateHeader = {
    UserId: "Message/Header/UserId",
    RgToken: "Message/Header/RgToken",
    RequestTime: "Message/Header/RequestTime",
    RequestSn: "number(Message/Header/RequestSn)",
    RequestType: "Message/Body/@xsi:type",
};

export interface MerchantCustomerHolder {
    merchant: Merchant;
    customer: Customer;
    pokerStarHeader: PokerStarHeader;
}

export interface PokerStarHeader {
    UserId: string;
    RgToken: string;
    RequestTime: string;
    RequestSn: number;
    RequestType: string;
}

export enum ACTION {
    BetRequest = "BetRequest",
    BetResponse = "BetResponse",
    HandResultRequest = "HandResultRequest",
    HandResultResponse = "HandResultResponse",
    StartGameRequest = "StartGameRequest",
    StartGameResponse = "StartGameResponse",
    GetBalanceRequest = "GetBalanceRequest",
    GetBalanceResponse = "GetBalanceResponse",
    FinalizedHandResultRequest = "FinalizedHandResultRequest",
    FinalizedHandResultResponse = "FinalizedHandResultResponse",
}

function isEmptyObject(obj): boolean {
    return Object.entries(obj).length === 0 && obj.constructor === Object;
}
function checkObject(obj: any) {
    if (isEmptyObject(obj)) {
        throw new TypeError("Invalid input: Malformed xml");
    }
}

// a middleware sub-stack to get header
router.post("/*",
    bodyParser.text({ type: "*/xml" }),
    routerCallbackWrapper(async (req: Request & MerchantCustomerHolder & PokerStarHeader,
                           res: Response,
                           next: NextFunction) => {

        req.pokerStarHeader = await transform(req.body, templateHeader);
        checkObject(req.pokerStarHeader);
        log.debug(req.pokerStarHeader, "Parsed XML header");

        // Do not anything
        if (!settings.notSaveAnyData) {

            const expirationTime = req.url.includes("finalizedhandresult") ? undefined : config.expirationTime.ticket;
            const ticket = getTicket(req.pokerStarHeader.RgToken, expirationTime);
            if (!ticket.merchantId) {
                throw new SystemError("merchantId isn't found");
            }
            req.merchant = Merchants.getById(ticket.merchantId, true);
            if (ticket.custId !== req.pokerStarHeader.UserId) {
                throw new SystemError("UserId isn't match");
            }
            const customer: Customer = req.merchant.customers[req.pokerStarHeader.UserId];
            req.customer = customer;
            if (!Number.isInteger(req.pokerStarHeader.RequestSn) || req.pokerStarHeader.RequestSn < 0) {
                throw new MessageValidationError("RequestSn is invalid value");
            }
        }

        next();
    }));

router.post("/startgame", routerCallbackWrapper(startGame));
router.post("/getbalance", routerCallbackWrapper(getBalance));
router.post("/bet", routerCallbackWrapper(bet));
router.post("/handresult", routerCallbackWrapper(handResult)); // win
router.post("/finalizedhandresult", routerCallbackWrapper(finalizedHandResult)); // finalized win

async function bet(req: Request & MerchantCustomerHolder, res: Response, next: NextFunction) {
    let AvailableFunds;

    // Do not save and check anything
    if (settings.notSaveAnyData) {
        AvailableFunds = settings.amount;
    } else {

        await throwCustomErrorIfExists("bet", req.merchant, req.customer);

        const action = WALLET_ACTION.debit;
        const template = {
            trxId: "Message/Body/RgTxId",
            betAmount: "number(Message/Body/BetAmount)",
        };

        const body = await transform(req.body, template);
        checkObject(body);
        log.debug(body, "BET: Parsed Request body XML");

        if (ACTION.BetRequest !== req.pokerStarHeader.RequestType) {
            throw new SystemError("Types isn't match");
        }

        if (!body.betAmount) {
            throw new SystemError("betAmount not found");
        }

        const amount = +body.betAmount / config.currencyUnitMultiplier;
        const newBalance = sumMajorUnits(req.customer.balance.amount, amount * action);
        if (newBalance < 0) {
            throw new InsufficientFundsError();
        }

        req.customer.balance.amount = newBalance;
        if (req.customer.freeBets && req.customer.freeBets.count > 0) {
            req.customer.freeBets.count--;
        }

        // Do not save transactions to decrease the load
        if (!settings.decreaseLoad) {
            TransactionModel.setById(body.trxId, action, amount, req.customer.cust_id);
            RoundModel.createOrUpdate({
                merchantId: req.merchant.merch_id,
                customerId: req.customer.cust_id,
                gameId: body.trxId,
            }, action, amount);
        }
        AvailableFunds = Math.round(req.customer.balance.amount * config.currencyUnitMultiplier);
    }

    const response = getResponseTemplate(req);

    response.Message.Body = {
        "@xsi:type": ACTION.BetResponse,
        TransactionId: (+new Date() / 10000).toFixed(),
        HandId: 1,
        AvailableFunds: AvailableFunds,
    };

    send(req, res, createXML(response, log));
}

// WIN
async function handResult(req: Request & MerchantCustomerHolder, res: Response, next: NextFunction) {

    let AvailableFunds;
    let TransactionId: number = +(+new Date() / 10000).toFixed();
    const response = getResponseTemplate(req);

    // Do not save and check anything
    if (settings.notSaveAnyData) {
        AvailableFunds = settings.amount;
    } else {

        await throwCustomErrorIfExists("handresult", req.merchant, req.customer);

        const action = WALLET_ACTION.credit;
        const template = {
            trxId: "Message/Body/Pay/RgTxId",
            amount: "number(Message/Body/Pay/PayAmount)",
            payType: "Message/Body/Pay/PayType",
            Jackpot: [
                "/Message/Body/Handover/Jackpots/Jackpot",
                {
                    JackpotId: "JackpotId",
                    JackpotBet: "number(JackpotBet)",
                    JackpotContribution: "number(JackpotContribution)",
                    JackpotResetContribution: "number(JackpotResetContribution)",
                    JackpotPay: "number(JackpotPay)",
                }

            ]
        };

        const body = await transform(req.body, template);
        checkObject(body);
        log.debug(body, "handResult: Parsed Request body XML");

        if (ACTION.HandResultRequest !== req.pokerStarHeader.RequestType) {
            throw new SystemError("Types isn't match");
        }

        if (Number.isFinite(body.amount) && (body.amount < 0 || !Number.isInteger(body.amount))) {
            throw new SystemError("PayAmount is invalid value");
        }
        if (body.Jackpot) {
            body.Jackpot.map(jackpot => {
                if (!Number.isInteger(jackpot.JackpotBet) || jackpot.JackpotBet < 0) {
                    throw new MessageValidationError("JackpotBet is invalid value");
                }

                if (!Number.isInteger(jackpot.JackpotContribution) || jackpot.JackpotContribution <= 0) {
                    throw new MessageValidationError("JackpotContribution is invalid value");
                }

                if (!Number.isInteger(jackpot.JackpotPay) || jackpot.JackpotPay < 0) {
                    throw new MessageValidationError("JackpotPay is invalid value");
                }

                if (!Number.isInteger(jackpot.JackpotResetContribution) || jackpot.JackpotResetContribution <= 0) {
                    throw new MessageValidationError("JackpotResetContribution is invalid value");
                }
            });
        }

        if (!body.amount) {
            AvailableFunds = Math.round(req.customer.balance.amount * config.currencyUnitMultiplier);
            TransactionId = 0;
        } else {

            const amount = +body.amount / config.currencyUnitMultiplier;
            const initialBalance = req.customer.balance.amount;
            req.customer.balance.amount = sumMajorUnits(initialBalance, amount * action);

            // Do not save transactions to decrease the load
            if (!settings.decreaseLoad) {
                TransactionModel.setById(body.trxId, action, amount, req.customer.cust_id);
                RoundModel.createOrUpdate({
                    merchantId: req.merchant.merch_id,
                    customerId: req.customer.cust_id,
                    gameId: body.trxId,
                }, action, amount);
            }
            // PokerStars sends us the initial balance on this request, not the updated one
            AvailableFunds = Math.round(initialBalance * config.currencyUnitMultiplier);
        }
    }

    response.Message.Body = {
        "@xsi:type": ACTION.HandResultResponse,
        HandId: 1,
        Pay: {
            TransactionId: TransactionId,
            AvailableFunds: AvailableFunds,
        }
    };

    if (req.customer && req.customer.freeBets) {
        response.Message.Body.Fsb = {
            FsbId: 1234,
            BetPerSpin: req?.customer?.freeBets?.coin || 0,
            TotalFreeSpins: req?.customer?.freeBets?.count || 0,
            FreeSpinsUsed: 0,
            TotalAward: 0
        };
    }

    send(req, res, createXML(response, log));
}

// FINALIZED WIN
async function finalizedHandResult(req: Request & MerchantCustomerHolder, res: Response, next: NextFunction) {

    let AvailableFunds;
    let TransactionId: number = +(+new Date() / 10000).toFixed();
    const response = getResponseTemplate(req);

    // Do not save and check anything
    if (settings.notSaveAnyData) {
        AvailableFunds = settings.amount;
    } else {

        await throwCustomErrorIfExists("finalizedhandresult", req.merchant, req.customer);

        const action = WALLET_ACTION.credit;
        const template = {
            fgReqId: "Message/Body/FgReqId",
            userId: "Message/Body/UserId",
            rgToken: "Message/Body/RgToken",
            finalizedStatus: "Message/Body/FinalizedStatus",
            regularBet: "number(/Message/Body/Handover/RegularBet)",
            regularPay: "number(/Message/Body/Handover/RegularPay)",
            sideBet: "number(/Message/Body/Handover/sideBet)",
            sideBetPay: "number(/Message/Body/Handover/sideBetPay)",
            refund: "number(/Message/Body/Handover/Refund)",
        };

        const body = await transform(req.body, template);
        checkObject(body);
        log.debug(body, "finalizedHandResult: Parsed Request body XML");

        if (ACTION.FinalizedHandResultRequest !== req.pokerStarHeader.RequestType) {
            throw new SystemError("Types isn't match");
        }

        if (Number.isFinite(body.regularPay) && (body.amount < 0 || !Number.isInteger(body.regularPay))) {
            throw new SystemError("RegularPay is invalid value");
        }

        if (body.finalizedStatus === "FINALIZED") {

            AvailableFunds = Math.round(req.customer.balance.amount * config.currencyUnitMultiplier);
            TransactionId = 0;
        }
    }

    response.Message.Body = {
        "@xsi:type": ACTION.FinalizedHandResultResponse,
        HandId: 1,
        Pay: {
            TransactionId: TransactionId,
            AvailableFunds: AvailableFunds,
        },
    };

    send(req, res, createXML(response, log));
}

async function startGame(req: Request & MerchantCustomerHolder, res: Response, next: NextFunction) {
    const response = getResponseTemplate(req);

    // Do not save and check anything
    if (settings.notSaveAnyData) {

        response.Message.Body = {
            "@xsi:type": ACTION.StartGameResponse,
            GameSessionId: 1234,
            UserScreenName: req.pokerStarHeader.UserId,
            UserCountry: "CN",
            AvailableFunds: settings.amount,
            TableType: {
                TableName: "TableName",
                TableTypeId: 1234,
                GameTypeId: 1234,
                VariantTypeId: 1234,
                IsPlayForFun: false,
                Currency: defaultCustomerCurrency,
                MinBetLimit: 0,
                MaxBetLimit: 1500,
                VendorConfig: "any extra information",
            },
            InterruptedGame: {
                GameSessionId: "",
                RgToken: "",
                HandId: "",
            }
        };
    } else {

        await throwCustomErrorIfExists("startgame", req.merchant, req.customer);

        const body = await transform(req.body, {
            Type: "Message/Body/Type",
        });
        checkObject(body);
        log.debug(body, "startGame: Parsed Request body XML");

        if (ACTION.StartGameRequest !== req.pokerStarHeader.RequestType) {
            throw new SystemError("Types isn't match");
        }

        if ("FINALIZABLE" !== body.Type) {
            throw new SystemError("FINALIZABLE only allowed");
        }

        response.Message.Body = {
            "@xsi:type": ACTION.StartGameResponse,
            GameSessionId: 1234,
            UserScreenName: req.customer.cust_id,
            UserCountry: req.customer.country,
            AvailableFunds: Math.round(req.customer.balance.amount * config.currencyUnitMultiplier),
            TableType: {
                TableName: "TableName",
                TableTypeId: 1234,
                GameTypeId: 1234,
                VariantTypeId: 1234,
                IsPlayForFun: false,
                Currency: req.customer.currency_code,
                MinBetLimit: 0,
                MaxBetLimit: req.customer.bet_limit,
                VendorConfig: "any extra information",
            },
            InterruptedGame: {
                GameSessionId: "",
                RgToken: "",
                HandId: "",
            }
        };
    }

    if (req.customer && req.customer.freeBets) {
        response.Message.Body.Fsb = {
            FsbId: 1234,
            BetPerSpin: req?.customer?.freeBets?.coin || 0,
            TotalFreeSpins: req?.customer?.freeBets?.count || 0,
            FreeSpinsUsed: 0,
            TotalAward: 0
        };
    }

    if (req.customer && req.customer.bet_limit) {
        response.Message.Body.PlayerMaxBet = req.customer.bet_limit;
    }

    send(req, res, createXML(response, log));
}

async function getBalance(req: Request & MerchantCustomerHolder, res: Response, next: NextFunction) {
    let AvailableFunds;
    const response = getResponseTemplate(req);

    // Do not save and check anything
    if (settings.notSaveAnyData) {
        AvailableFunds = settings.amount;

    } else {
        await throwCustomErrorIfExists("getbalance", req.merchant, req.customer);

        const body = await transform(req.body, {
            Type: "Message/Body/Type",
        });
        checkObject(body);
        log.debug(body, "GetBalanceRequest: Parsed Request body XML");

        if (ACTION.GetBalanceRequest !== req.pokerStarHeader.RequestType) {
            throw new SystemError("Types isn't match");
        }

        AvailableFunds = Math.round(req.customer.balance.amount * config.currencyUnitMultiplier);
    }
    response.Message.Body = {
        "@xsi:type": ACTION.GetBalanceResponse,
        AvailableFunds: AvailableFunds,
    };

    send(req, res, createXML(response, log));
}

export function getResponseTemplate(req) {
    return {
        Message: {
            "@xmlns:xsd": "http://www.w3.org/2001/XMLSchema",
            "@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance",
            Header: {
                UserId: req?.pokerStarHeader?.UserId || "",
                RgToken: req?.pokerStarHeader?.RgToken || "",
                RequestTime: req?.pokerStarHeader?.RequestTime || "",
                RequestSn: req?.pokerStarHeader?.RequestSn || 0,
            },
            Body: {} as any
        }
    };
}

export function send(req: Request, res: Response, body: object) {
    res.type(XML_TYPE);
    res.send(body);
    return null;
}

function getTicket(ticketId, expirationTime?) {
    const currentTimeMs = new Date().getTime();
    const ticket: Ticket = tickets[ticketId] && { ...tickets[ticketId] } as Ticket;
    if (ticket) {
        if (expirationTime && (currentTimeMs > ticket.creationTime + expirationTime)) {
            delete tickets[ticketId];
            throw new SessionTerminatedError("Ticket is expired");
        }
        // Extend if valid
        tickets[ticketId].creationTime = currentTimeMs;
    } else {
        throw new SessionTerminatedError("Ticket not found");
    }
    ticket.id = ticketId;
    return ticket;
}

export default router;
