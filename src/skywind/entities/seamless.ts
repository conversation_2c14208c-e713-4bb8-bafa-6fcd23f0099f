import {
    ExtraDataContainer,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameURLInfo,
    MerchantStartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { JackpotWinDetails } from "./sw";

export interface SeamlessGameInitRequest extends MerchantGameInitRequest, Partial<SeamlessSessionData> {
    merch_login_url?: string;
    previousStartTokenData?: SeamlessStartGameTokenData;
    currency?: string;
    gameGroup?: string;
    playerCode?: string;
    oldGameCode?: string;
}

interface SeamlessSessionData {
    /**
     * IPM session id, used for debit/credit/balance operations
     */
    ipmSessionId: string;
}

interface SeamlessRCData {
    rce?: number; // reality check elapsed time, in minutes
    rci?: number; // reality check interval (period of reality check), in minutes
}

interface OperatorInfo {
    siteUrl?: string;
}

export interface SeamlessStartGameTokenData extends MerchantStartGameTokenData, SeamlessSessionData, SeamlessRCData,
    OperatorInfo {
    oldGameCode?: string;
    hasLobby?: boolean;
    disablePlayerPhantomFeatures?: boolean;
}

export interface SeamlessAuthTokenData extends MerchantGameTokenData, SeamlessSessionData {
    rci?: number; // Reality check interval
    oldGameCode?: string;
    hasLobby?: boolean;
    disablePlayerPhantomFeatures?: boolean;
}

export interface SeamlessPlayerTokenData {
    playerCode?: string;
    ipmSessionId?: string;
}

export interface SeamlessUrlParams {
    language: string;
    playmode: string;
    merch_login_url?: string;
    lobby?: string;
    cashier?: string;
}

export interface SeamlessGameUrlInfo extends MerchantGameURLInfo {
    urlParams: SeamlessUrlParams;
    tokenData: SeamlessStartGameTokenData;
}

export interface SeamlessGetPlayerResponse {
    error_code: number;
    cust_id: string;
    game_group: string;
    cust_login: string;
    currency_code: string;
    language: string;
    country: string;
    first_name: string;
    last_name: string;
    nickname?: string;
    email: string;
    test_cust: string;
}

export interface SeamlessErrorContainer {
    error_code: number;
    error_msg?: string;
    rci?: number; // Reality check interval
}

export interface SeamlessPlayerInfoResponse extends SeamlessErrorContainer {
    cust_id: string;
    game_group: string;
    cust_login: string;
    currency_code: string;
    language?: string;
    country: string;
    first_name: string;
    last_name: string;
    nickname?: string;
    email: string;
    test_cust: string;
    max_total_bet?: number;
}

export interface SeamlessFreeBetResponse {
    error_code: number;
    free_bet_count?: number;
    free_bet_coin?: number;
}

export interface SeamlessVerifyTokenRequest {
    ticket: string;
    merch_id: string;
    merch_pwd: string;
    ip?: string;
}

export interface SeamlessVerifyTokenResponse extends SeamlessPlayerInfoResponse, SeamlessRCData {
    cust_session_id: string;
    disable_offers?: boolean;
}

export interface SeamlessRenewSessionRequest extends SeamlessMerchantData, SeamlessCustomerData {
    old_game_code: string;
    new_game_code: string;
}

export interface SeamlessRenewSessionResponse {
    new_cust_session_id: string;
}

export interface SeamlessCMAMessage {
    msgType: string; // Message type. Currently, can be 'message' only.
    message: string; // Message to present to player. Can be both plain string or HTML code. Message must be localized.
    nonIntrusive?: boolean; // If specified and true, message can be shown in a non-intrusive fashion. False by default.
    title?: string; // Optional title string to use for popup when presenting CMA message to player.
                    // If not specified, default title is used. If present, must be localized.
}

export interface SeamlessCMAContainer {
    messages?: SeamlessCMAMessage[];
}

export interface SeamlessBalanceResponse extends SeamlessErrorContainer, SeamlessCMAContainer {
    balance: number;
    currency_code?: string; // only in get balance
    free_bet_count?: number;
}

export enum EVENT_TYPE {
    WIN = "win",
    BET = "bet",
    FREEBET = "free-bet",
    FREEBET_WIN = "free-bet-win",
    ROLLBACK = "rollback",
    BONUS = "bonus",
    TRANSFER_IN = "transfer-in",
    TRANSFER_OUT = "transfer-out",
    ROUND_STATISTICS = "round-statistics",
    FORCE_FINISH = "force-finish"
}

export interface SeamlessMerchantData {
    merch_id: string;
    merch_pwd: string;
}

export interface SeamlessCustomerData {
    cust_id?: string;
    cust_session_id?: string;
}

export interface SeamlessBonusData {
    sub_trx_type?: string;
    promo_id?: string;
    promo_pid?: string;
    promo_external_id?: string;
    distribution_type?: string;
}

export interface SeamlessPaymentRequest extends SeamlessBonusData, SeamlessMerchantData, SeamlessCustomerData,
    ExtraDataContainer {
    currency_code: string;
    game_code: string;
    trx_id: string | number;
    game_id: number;
    round_id: string;
    timestamp: number;
    event_id: number;
    platform: string;
    game_type: string;
    event_type: string;
    amount: number;
    jp_contribution?: number;
    grc_redeem_info?: {
        stars?: number;
        amount?: number;
    };
    is_finalization_payment?: boolean;
    sm_result?: string;
    operation_ts: number;
    promo_type?: string;
}

export interface SeamlessBetRequest extends SeamlessPaymentRequest {
    free_bet_coin?: number;
}

export interface SeamlessWinRequest extends SeamlessPaymentRequest {
    game_status: string;
    jp_win?: boolean;
    jp_ids?: string[];
    jp_win_details?: JackpotWinDetails[];
}

export interface SeamlessTransferRequest extends SeamlessPaymentRequest {
    game_status: string;
}

export interface SeamlessPaymentResponse extends SeamlessBalanceResponse {
    balance: number;
    trx_id: string;
    free_bet_count?: number;
}

export interface SeamlessRollbackRequest extends SeamlessMerchantData, SeamlessCustomerData {
    currency_code: string;
    game_code: string;
    trx_id: string | number;
    game_id: number;
    event_type: EVENT_TYPE.ROLLBACK;
    game_type: string;
    game_status: string;
    timestamp: number;
    event_id: number;
    round_id: string;
    operation_ts: number;
}

export interface TransferOutRequest extends SeamlessPaymentRequest {
    left_amount: number;
    actual_bet_amount: number;
    actual_win_amount: number;
    jp_win_amount: number;
    game_status: string;
}

export interface SeamlessOfflineBonusRequest {
    merch_id: string;
    merch_pwd?: string;
    cust_id: string;
    amount: number;
    trx_id: string | number;
    currency_code: string;
    timestamp: number;
    promo_id: string;
    promo_type: string;
    promo_pid?: string;
    hash?: string;
    operation_ts?: number;
    distribution_type?: string;
}

export interface SeamlessOfflineBonusResponse extends SeamlessErrorContainer {
    balance: number;
    trx_id: string;
}

export interface SeamlessCertificate extends SeamlessCertificateCredentials {
    cert?: string;
    key?: string;
}

export interface SeamlessCertificateCredentials {
    password?: string;
    ca?: string;
}
