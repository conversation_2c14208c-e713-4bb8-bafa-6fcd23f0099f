######################### SONAR STUFF #########################
FROM node:22.14.0-bullseye as sonar

WORKDIR /app
COPY . /app/

# Install dependencies
RUN apt-get -yqq --no-install-recommends install curl unzip

ENV COREPACK_INTEGRITY_KEYS=0
RUN corepack enable && pnpm install --no-frozen-lockfile \
    && pnpm run lint \
    && pnpm run clean \
    && pnpm run compile \
    && pnpm run version \
    && pnpm run test

CMD ["node", "/app/out/skywind/app"]

######################### MAIN IMAGE #########################
FROM node:22.14.0-alpine as main

WORKDIR /app

COPY --chown=node:node --from=sonar /app/package.json /app/pnpm-lock.yaml /app/preinstall.cjs /app/.npmrc /app/swagger.json /app/swagger-softswiss.json ./
COPY --chown=node:node --from=sonar /app/out ./out
COPY --chown=node:node --from=sonar /app/res ./res

ENV COREPACK_INTEGRITY_KEYS=0
RUN corepack enable \
    && pnpm install --frozen-lockfile --prod \
    && pnpm store prune \
    && rm .npmrc \
    && rm -fr /root/.cache \
    && corepack disable

USER node
CMD ["node", "/app/out/skywind/app"]
