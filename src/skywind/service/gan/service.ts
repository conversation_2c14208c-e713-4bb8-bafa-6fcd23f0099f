import * as uuid from "uuid";
import {
    GAN_AAMS_SESSION_STATUS,
    GAN_CONTEST_STATUS,
    GAN_PLAY_STATE,
    GAN_RESPONSE_STATUS,
    GANBetPaymentRequestAction,
    GANEnsureContestForPlayerRequest,
    GANEnsureContestForPlayerResponse,
    GANGetPlayerInfoRequest,
    GANGetPlayerInfoResponse,
    GANPaymentRequest,
    GANPaymentResponse,
    GANRollbackPaymentRequestAction,
    GANWinPaymentRequestAction,
} from "../../entities/gan";
import { PAYMENT_TYPE } from "../../entities/common";
import * as GANErrors from "../../errorsGAN";

import { Customer, Merchant } from "../../models/merchant";
import { throwCustomError } from "../customError";
import { extraDataHandler } from "../extraData";
import * as TransactionModel from "../../models/transaction";
import { WALLET_ACTION } from "../../models/transaction";
import { sumMajorUnits } from "../../utils/helper";

export const DEFAULT_GAN_PLAYER_ALIAS = "gan_player_alias";
export const DEFAULT_GAN_PLAYER_PARTNER = ""; // ???
export const DEFAULT_GAN_PLAYER_LOCALE = "it";
export const DEFAULT_GAN_PLAYER_COUNTRY = "IT";
export const DEFAULT_GAN_PLAYER_CURRENCY = "EUR";

export interface GANService {
    ensureContestForPlayer(data: GANEnsureContestForPlayerRequest): Promise<GANEnsureContestForPlayerResponse>;

    getPlayerInfo(data: GANGetPlayerInfoRequest): Promise<GANGetPlayerInfoResponse>;

    debit(data: GANPaymentRequest<GANBetPaymentRequestAction>): Promise<GANPaymentResponse>;

    credit(data: GANPaymentRequest<GANWinPaymentRequestAction>): Promise<GANPaymentResponse>;

    rollback(data: GANPaymentRequest<GANRollbackPaymentRequestAction>): Promise<GANPaymentResponse>;

    // endRound(data: SBTechEndRoundRequest): SBTechEndRoundResponse;
}

export class GANServiceImpl implements GANService {
    constructor(public merchant: Merchant, public customer?: Customer) {
    }

    @throwCustomError({ action: "ensureContestForPlayer" })
    @extraDataHandler()
    public async ensureContestForPlayer(data: GANEnsureContestForPlayerRequest): Promise<GANEnsureContestForPlayerResponse> {
        return {
            _status: GAN_RESPONSE_STATUS.SUCCESS,
            Contest: {
                _contestRef: data.Contest._newContestRef,
                _gameTypeRef: data.Contest._gameTypeRef,
                _playerGuid: data.Contest._playerGuid,
                _state: GAN_CONTEST_STATUS.OPEN,
                _created: this.getTimestamp(),

                AamsSession: {
                    _id: uuid.v4(),
                    _state: GAN_AAMS_SESSION_STATUS.SESSION_CREATED,
                }
            }
        } as GANEnsureContestForPlayerResponse;
    }

    @throwCustomError({ action: "getPlayerInfo" })
    @extraDataHandler()
    public async getPlayerInfo(data: GANGetPlayerInfoRequest): Promise<GANGetPlayerInfoResponse> {
        return {
            _status: GAN_RESPONSE_STATUS.SUCCESS,
            Player: {
                _playerGuid: data.Player._playerGuid,
                _partner: DEFAULT_GAN_PLAYER_PARTNER,
                _alias: DEFAULT_GAN_PLAYER_ALIAS,
                _country: DEFAULT_GAN_PLAYER_COUNTRY,
                _currency: DEFAULT_GAN_PLAYER_CURRENCY,
                _locale: DEFAULT_GAN_PLAYER_LOCALE, // language

                Balance: {
                    _total: this.customer.balance.amount,
                    _cash: 0, // ???
                    _restrictedCash: 0, // ???
                    _bonus: 0, // ???
                }
            }
        } as GANGetPlayerInfoResponse;
    }

    @throwCustomError({ action: "debit" })
    @extraDataHandler()
    public async debit(data: GANPaymentRequest<GANBetPaymentRequestAction>): Promise<GANPaymentResponse> {
        const { _ref: trxId, _amount: amount } = data.Play.Action;

        this.validateTransaction(WALLET_ACTION.debit, trxId);
        this.checkPlayerBalance(this.customer.balance.amount, Math.abs(amount) * WALLET_ACTION.debit);

        TransactionModel.setById(trxId, WALLET_ACTION.debit, amount, this.customer.cust_id);
        this.customer.balance.amount = sumMajorUnits(this.customer.balance.amount,
            Math.abs(amount) * WALLET_ACTION.debit);

        return {
            Play: {
                _playRef: data.Play._playRef,
                _playerGuid: data.Play._playerGuid,
                _contestRef: data.Play._contestRef,
                _staked: amount,
                _state: GAN_PLAY_STATE.OPEN,
                _completed: this.getTimestamp(), // needs to be set when state is completed or voided
                Balance: {
                    _total: this.customer.balance.amount,
                    _cash: 0, // ???
                    _restrictedCash: 0, // ???
                    _bonus: 0, // ???
                }
            }
        } as GANPaymentResponse;
    }

    @throwCustomError({ action: "credit" })
    @extraDataHandler()
    public async credit(data: GANPaymentRequest<GANWinPaymentRequestAction>): Promise<GANPaymentResponse> {
        const { _ref: trxId, _amount: amount } = data.Play.Action;

        this.validateTransaction(WALLET_ACTION.credit, trxId);

        TransactionModel.setById(trxId, WALLET_ACTION.credit, amount, this.customer.cust_id);
        this.customer.balance.amount = sumMajorUnits(this.customer.balance.amount,
            Math.abs(amount) * WALLET_ACTION.credit);

        return {
            Play: {
                _playRef: data.Play._playRef,
                _playerGuid: data.Play._playerGuid,
                _contestRef: data.Play._contestRef,
                _staked: amount,
                _state: data.Play._finish
                        ? GAN_PLAY_STATE.COMPLETED
                        : GAN_PLAY_STATE.OPEN,
                _completed: this.getTimestamp(),
                Balance: {
                    _total: this.customer.balance.amount,
                    _cash: 0, // ???
                    _restrictedCash: 0, // ???
                    _bonus: 0, // ???
                }
            }
        } as GANPaymentResponse;
    }

    @throwCustomError({ action: "rollback" })
    @extraDataHandler()
    public async rollback(data: GANPaymentRequest<GANRollbackPaymentRequestAction>): Promise<GANPaymentResponse> {
        const { _ref: debitTrxId } = data.Play.Action;
        const debitTransaction = TransactionModel.getById(debitTrxId, WALLET_ACTION.debit);

        if (!debitTransaction) {
            throw new GANErrors.PlayHasNotBeenStaked();
        }

        const rollbackTrxId = this.createTrxId(debitTrxId, PAYMENT_TYPE.ROLLBACK);
        const creditTrxId = this.createTrxId(debitTrxId, PAYMENT_TYPE.ROLLBACK);
        const creditTransaction = TransactionModel.getById(creditTrxId, WALLET_ACTION.credit);

        const [_, debitAmount, __, isRollback] = debitTransaction;
        const [___, creditAmount] = creditTransaction;

        if (!TransactionModel.getById(rollbackTrxId, WALLET_ACTION.rollback)) {
            if (isRollback) {
                throw new GANErrors.PlayIsVoided();
            }

            TransactionModel.rollbackById(debitTrxId, WALLET_ACTION.debit);
            this.customer.balance.amount = this.customer.balance.amount - debitAmount;
            TransactionModel.setById(rollbackTrxId, WALLET_ACTION.rollback, 0, this.customer.cust_id);
        }

        return {
            Play: {
                _playRef: data.Play._playRef,
                _playerGuid: data.Play._playerGuid,
                _contestRef: data.Play._contestRef,
                _staked: debitAmount, // extra info ???
                _won: creditAmount, // extra info ???
                _state: GAN_PLAY_STATE.VOIDED, // OPEN state ???
                _completed: this.getTimestamp(),
                Balance: {
                    _total: this.customer.balance.amount,
                    _cash: 0, // ???
                    _restrictedCash: 0, // ???
                    _bonus: 0, // ???
                }
            }
        } as GANPaymentResponse;
    }

    /*
    @throwCustomError({ action: "endRound" })
    @extraDataHandler()
    public endRound(data: SBTechEndRoundRequest): SBTechEndRoundResponse {
        const { trxId } = data;

        this.validateTransaction(WALLET_ACTION.credit, trxId); // check ???

        const transaction = TransactionModel.setById(trxId, WALLET_ACTION.credit, 0, this.customer.cust_id);

        return {
            trxId: transaction[TRANSACTION_INDEX.EXTERNAL_ID],
            sessionId: data.sessionId,
            balance: this.toMinorUnits(this.customer.balance.amount),
            timestamp: this.getTimestamp(),
        } as SBTechEndRoundResponse;

        private toMinorUnits(value: number): number {
            return value * 100;
        }

        private toMajorUnits(value: number): number {
            return value
                   ? value / 100
                   : 0;
        }
    }  */

    private validateTransaction(type: WALLET_ACTION, trxId: string) {
        const transaction = TransactionModel.getById(trxId, type);

        if (transaction) {
            throw new GANErrors.InvalidActionRef();
        }
    }

    private checkPlayerBalance(playerBalance: number, amount: number) {
        if (playerBalance + amount < 0) {
            throw new GANErrors.InsufficientFunds();
        }
    }

    private getTimestamp(): string {
        return new Date().toISOString().replace(/T/, " ").replace(/\.\d{3}Z/, "");
    }

    private createTrxId(trxId: string, type: PAYMENT_TYPE) {
        const [originTrxId, roundId] = trxId.split("_");
        return `${originTrxId}_${roundId}_${type}`;
    }
}

export function getGANService(merchant: Merchant, customer?: Customer): GANServiceImpl {
    return new GANServiceImpl(merchant, customer);
}
