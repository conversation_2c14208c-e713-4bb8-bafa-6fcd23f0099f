import { measures } from "../measures";
import * as asyncHooks from "async_hooks";
import { EventEmitter } from "events";
import MeasurableFunction = measures.MeasurableFunction;
import { type TransactionRunner } from "./definition";
import { lazy } from "../../lazy";

const wrapEmitterLazy = lazy(() => require("emitter-listener"));

const ASYNC_STORAGE_SYMBOL = "asyncContext";
const TRANSFERABLE = Symbol.for("transferable");

interface AsyncCtx {
    ts?: number;
}

export class AsyncStorageTransactionRunner implements TransactionRunner {
    private readonly storage = new asyncHooks.AsyncLocalStorage();

    public instrumentModule(name: string, module: any): any {
        return undefined;
    }

    public runInTransaction<T>(action: (...args) => T): T {
        let result;
        this.storage.run(this.createContext(), () => {
            result = action();
        });

        return result;
    }

    public runOutOfTransaction<T>(action: (...args) => T): T {
        let result;
        this.storage.exit(() => {
            result = action();
        });

        return result;
    }

    public trackTransaction<T>(action: MeasurableFunction<T>): MeasurableFunction<T> {
        if (action instanceof EventEmitter) {
            this.bindEmitter(action as EventEmitter);
            return action;
        } else {
            const context = this.storage.getStore() || this.createContext();
            return (...args) => {
                let result;
                this.storage.run(context, () => {
                    result = action(...args);
                });
                return result;
            };

        }
    }

    public getContext(transferable: boolean = false): any {
        return !transferable ? this.storage.getStore() : this.getTransferable();
    }

    public getContextVariable<T>(name: string, transferable: boolean = false): T {
        const ctx = this.getContext(transferable);
        if (ctx) {
            return ctx[name];
        }
    }

    public setContextVariable<T>(name: string, value: T, transferable: boolean = false): boolean {
        const ctx = this.getContext(transferable);
        if (ctx) {
            ctx[name] = value;
            return true;
        } else {
            return false;
        }
    }

    private getTransferable() {
        const ctx: any = this.storage.getStore();
        if (ctx) {
            let result = ctx[TRANSFERABLE];
            if (!result) {
                result = ctx[TRANSFERABLE] = {};
            }
            return result;
        }
    }

    private bindEmitter(emitter: EventEmitter) {
        const storage = this.storage;

        // Capture the context active at the time the emitter is bound.
        function attach(listener) {
            if (!listener) {
                return;
            }

            listener[ASYNC_STORAGE_SYMBOL] = storage.getStore();
        }

        function bind(listener) {
            if (!(listener && listener[ASYNC_STORAGE_SYMBOL])) {
                return listener;
            }

            const context = listener[ASYNC_STORAGE_SYMBOL];

            return function() {
                return storage.run(context, () => {
                    return listener.apply(this, arguments);
                });
            };
        }

        (wrapEmitterLazy.get())(emitter, attach, bind);
    }

    private createContext() {
        return { ts: Date.now() };
    }
}
